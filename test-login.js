// Simple test script to verify the login API
const axios = require('axios');

const API_URL = 'http://*************:4244';
const LOGIN_ENDPOINT = '/auth/user/login';

// Test credentials - replace with valid credentials for testing
const testCredentials = {
  email: '<EMAIL>',
  password: 'password123'
};

async function testLogin() {
  console.log('Testing login API...');
  console.log(`API URL: ${API_URL}`);
  console.log(`Login Endpoint: ${LOGIN_ENDPOINT}`);
  console.log('Test credentials:', testCredentials);
  
  try {
    const response = await axios.post(`${API_URL}${LOGIN_ENDPOINT}`, testCredentials);
    console.log('Login successful!');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
    return true;
  } catch (error) {
    console.error('Login failed!');
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    return false;
  }
}

// Run the test
testLogin()
  .then(success => {
    console.log('Test completed.');
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
