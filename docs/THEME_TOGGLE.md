# Theme Toggle Implementation

This document describes the dark mode theme toggle implementation in the AVAS application.

## Overview

The application now supports three theme modes:
- **Light Mode**: Traditional light theme
- **Dark Mode**: Dark theme for better viewing in low-light conditions
- **System**: Automatically follows the user's system preference

## Implementation Details

### Components Added/Modified

1. **ThemeProvider Setup** (`src/components/providers.tsx`)
   - Added `next-themes` ThemeProvider wrapper
   - Configured with `class` attribute strategy
   - Enabled system theme detection
   - Disabled transition on theme change to prevent flashing

2. **Theme Toggle Component** (`src/components/ui/theme-toggle.tsx`)
   - Dropdown menu with three theme options
   - Animated sun/moon icons that transition based on current theme
   - Handles hydration properly with mounted state check
   - Accessible with proper ARIA labels

3. **Header Integration** (`src/components/dashboard/header.tsx`)
   - Added theme toggle button to the header
   - Positioned between user avatar and logout button
   - Maintains responsive design

4. **Layout Updates** (`src/app/layout.tsx`)
   - Added `suppressHydrationWarning` to prevent hydration mismatches
   - Ensures proper theme switching without console warnings

### CSS Variables

The application uses CSS custom properties for theming, defined in `src/app/globals.css`:

- **Light theme variables** (`:root`)
- **Dark theme variables** (`.dark`)
- Covers all UI elements including:
  - Background and foreground colors
  - Card and popover colors
  - Primary, secondary, and accent colors
  - Border and input colors
  - Chart colors
  - Sidebar colors

### Features

- **Persistent Theme Selection**: User's theme preference is saved in localStorage
- **System Theme Detection**: Automatically detects and follows system dark/light mode
- **Smooth Transitions**: Icons animate smoothly when switching themes
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Mobile Responsive**: Works correctly on all screen sizes
- **PWA Compatible**: Theme changes work in installed PWA mode

## Usage

Users can change the theme by:
1. Clicking the sun/moon icon in the header
2. Selecting from the dropdown menu:
   - Light
   - Dark
   - System

The selected theme will be remembered across browser sessions.

## Technical Notes

- Uses `next-themes` library for theme management
- Implements proper hydration handling to prevent SSR/client mismatches
- CSS variables ensure consistent theming across all components
- Shadcn UI components automatically support the theme system
- Theme state is managed globally and accessible throughout the app

## Dependencies

- `next-themes`: ^0.4.6 (already installed)
- `lucide-react`: For sun/moon icons
- Shadcn UI components: Button, DropdownMenu

## Browser Support

The theme toggle works in all modern browsers that support:
- CSS custom properties
- localStorage
- prefers-color-scheme media query (for system theme detection)
