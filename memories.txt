

















# UI Development
- User wants to build a user interface compatible for both desktop and mobile devices.
- User has integrated Shadcn UI components library into their Next.js project for UI development.
- The project requires Radix UI dependencies to be installed when using Shadcn UI components.
- User wants the dashboard to include a sticky header and a sidebar for navigation to other pages.
- User wants the dashboard to display the number of trials in the upcoming events section.
- User wants the dashboard to show the 'Sıradaki' (Next) part by comparing the current time with the date of the first upcoming trial.
- User wants a 'Kıdem Tazminatı' (Severance Pay) calculator section in the sidebar that submits data to '/api/user/compensation/calculate' endpoint and displays calculation results.
- User wants a 'Duruşmalar' (Trials/Hearings) section in the sidebar that fetches data from '/api/user/trials' endpoint and displays trial information.
- User wants a 'Dosyalar' (Cases) section in the sidebar that fetches data from '/api/user/cases?active=true/false' endpoints and displays cases with color coding based on active status and 'dosyaTur' field. Color 'icra' (enforcement/execution) items in red to visually distinguish them in the UI.
- User wants the 'Dosyalar' (Cases) sidebar section to include detailed views with tabs for 'Dosya Detayları' (Case Details), 'Notlar' (Notes), 'Dosya Rapor' (Case Report), 'Taraflar' (Parties), 'Tahsilat Reddiyat' (Collections/Refunds), and 'Safahat' (Stages) when an active case is clicked.
- Route case details pages by dosyaNo instead of dosyaId, and account for users having multiple cases.
- User wants a 'Hatırlatmalar' (Reminders) section in the sidebar that interacts with '/api/reminders' endpoints (GET, POST, PUT, DELETE) to manage user reminders with fields for title, description, dueDate, repeatIntervalInHours, and priority. The dueDate field from the API is returned as epoch time and requires appropriate formatting in the UI. Reminders should support four priority levels: LOW, MEDIUM, HIGH, and CRITICAL.
- User wants a 'Görevler' (Tasks) section in the sidebar that interacts with '/api/tasks' endpoints (GET, POST, PUT, DELETE) to manage tasks with fields for title, description, priority, startDate, dueDate, caseNumber, status, taskType, and notes, with support for different task types and priority levels.
- User wants the Görevler (Tasks) section to fetch case numbers from 'api/user/cases/taraflar-all' endpoint instead of active cases API, displaying both 'adi' and 'rol' fields separated by comma next to the case number in the dropdown.
- User wants task status change functionality via dropdown similar to Jira's issue management interface.
- User prefers using Shadcn date picker components instead of basic input elements for date fields.
- User prefers adding new reminders via modal dialogs rather than inline forms, similar to the update functionality.
- User wants interactive profile photo displays with modal/popover on click, hover previews, smooth animations, and proper aspect ratio maintenance across all avatar instances.
- User wants a notification system with two bell icons in the header: one for UYAP system notifications (fetched from '/api/user/notifications') and another for future app notifications, both using Shadcn UI components with specific display requirements.
- User wants notification unread counts to be visible without clicking on the notification icon.
- User wants the dashboard to include a card displaying detailed user information including financial and task summaries fetched from the '/api/user/report' endpoint, with a detailed view on click, and the component should be mobile compatible.
- User wants a 'Users' tab in the sidebar that fetches data from '/api/user/cases/taraflar-all' endpoint, displaying case parties with search functionality and pagination.
- User wants a 'Vekaletler' (Power of Attorney) tab in the sidebar that interacts with '/api/user/cases/power-of-attorneys' endpoints (GET, POST, PUT, DELETE) to manage power of attorney records with fields for powerOfAttorneyNumber, notaryName, clientNameList, lawyerList, powerList, yevmiyeNo, startDate, endDate, and caseNumber.
- User wants to set the 'Statue of lady justice' as the background image of the application.
- User wants their web app to be converted into a Progressive Web App (PWA) that can be downloaded and installed directly from the browser.
- User wants to show a splash screen or loading indicator when making API calls.
- User wants a signup flow with form submission, email OTP verification using standard input boxes instead of Shadcn OTP components, and redirection to login page after successful verification, with specific API endpoints at /auth/user/create and /auth/user/verify-email.
- After login, if 'isNewUser' field is true, show a notification about UYAP sync requirement instead of routing to dashboard.
- User wants a forgot password flow with email input, OTP verification, and password reset using specific API endpoints at /auth/user/forgot-password/send-otp, /auth/user/forgot-password/validate-otp, and /auth/user/forgot-password/update-password.
- User prefers routing to a separate screen for detailed views rather than using modal dialogs to have more space.
- When caseId contains a '+' character, it gets URL-encoded as '%2B', causing issues with finding cases in the application.
- User wants text content in UI components to use word-wrap: break-word to prevent overflow issues with long strings like URLs or filenames.
- User wants the Tahsilat Reddiyat (Collections/Refunds) screens to be mobile-responsive as the dialogs are not readable on mobile devices.
- User wants mobile layouts to handle keyboard opening properly so that content remains visible when the keyboard is displayed.
- User prefers dialogs not to be relocated to the edge of the screen.
- User wants dialogs to have blank space below them for better visibility on mobile when keyboard is open, and all dialogs should be scrollable to view content below.

# Backend API
- User's backend API is at http://*************:4244 and requires JWT authentication with specific login request/response formats.
- The API endpoint '/api/user/photo' returns user profile photos as base64-encoded strings in a JSON response that requires proper authentication headers.
- The API endpoint '/api/user/report' returns detailed user information including financial and task summaries.
- The API endpoint '/api/reminders' is used to manage user reminders with fields for title, description, dueDate, repeatIntervalInHours, and priority (GET, POST, PUT, DELETE).
- The API endpoint '/api/tasks' is used to manage tasks with fields for title, description, priority, startDate, dueDate, caseNumber, status, taskType, and notes (GET, POST, PUT, DELETE).
- The API endpoints '/api/user/cases/notes' (POST, GET, PUT, DELETE) are used to manage case notes with fields for content, caseNumber, and return additional metadata including timestamps and owner information.
- The API endpoints '/api/user/case-details' (POST, PUT, GET, DELETE) are used to manage case details with fields for caseNumber, caseType (enum), crimeType (enum, required only when caseType is CEZA_DAVASI), derdest, caseValue, caseReason, and caseTitle.
- API requests for dates should use string format 'YYYY-MM-DD' while API responses return dates in epoch time format.
- The createdAt and updatedAt fields in API responses are returned as epoch time.
- The 'Taraflar' (Parties) data is fetched from '/api/user/cases/taraflar?caseNumber=XXXXXX' endpoint, returning an array of objects with 'adi' (name), 'rol' (role), 'vekil' (attorney, can be empty), and 'kisiKurum' (person/institution, can be 'Kişi' or 'Kurum') fields.
- The 'Tahsilat Reddiyat' (Collections/Refunds) data is fetched from '/api/user/cases/tahsilat-reddiyat?caseNumber={caseNumber}' endpoint and displays financial transactions including collections, refunds, and fees with summary information. The API response includes fields like 'yatirilanMiktar', 'hesaplamaYapilanTutar', 'odenebilirMiktar', 'durumAciklama', and 'kapattigiTahsilatIDler'.
- API error responses may contain an 'error' field with a message that should be extracted and displayed to users in Turkish, along with a retry button, while logging complete error details to the console.

# Case Details Form
- All fields in the case details form should be required except for caseReason.