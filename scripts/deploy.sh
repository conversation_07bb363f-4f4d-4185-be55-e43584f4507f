#!/bin/bash

# Deployment Script for Next.js Application
# This script builds and deploys the Next.js application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="hukapp-nextjs-frontend"
APP_DIR="/opt/hukapp-nextjs-frontend"
DEPLOY_DIR="/var/www/api4js.com.tr"
SERVICE_NAME="hukapp-nextjs"
NODE_VERSION="18" # Adjust as needed

echo -e "${GREEN}=== Deploying $APP_NAME ===${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if running in the correct directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root directory."
    exit 1
fi

# Check Node.js version
print_step "Checking Node.js version..."
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js $NODE_VERSION or later."
    exit 1
fi

NODE_CURRENT=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_CURRENT" -lt "$NODE_VERSION" ]; then
    print_warning "Node.js version $NODE_CURRENT detected. Recommended version is $NODE_VERSION or later."
fi

# Install dependencies
print_step "Installing dependencies..."
if [ -f "package-lock.json" ]; then
    npm ci --production=false
else
    npm install
fi

# Run linting (optional, comment out if not needed)
print_step "Running linter..."
npm run lint || print_warning "Linting failed, continuing with deployment..."

# Build the application
print_step "Building Next.js application..."
npm run build

# Stop the service if it's running
print_step "Stopping application service..."
if systemctl is-active --quiet $SERVICE_NAME; then
    sudo systemctl stop $SERVICE_NAME
    print_status "Service $SERVICE_NAME stopped"
else
    print_status "Service $SERVICE_NAME is not running"
fi

# Create deployment directory if it doesn't exist
print_step "Preparing deployment directory..."
sudo mkdir -p $DEPLOY_DIR
sudo chown -R $USER:$USER $DEPLOY_DIR

# Copy built application to deployment directory
print_step "Copying application files..."
rsync -av --delete \
    --exclude='.git' \
    --exclude='node_modules' \
    --exclude='.next/cache' \
    --exclude='*.log' \
    ./ $DEPLOY_DIR/

# Install production dependencies in deployment directory
print_step "Installing production dependencies..."
cd $DEPLOY_DIR
npm ci --production

# Set proper permissions
print_step "Setting file permissions..."
sudo chown -R www-data:www-data $DEPLOY_DIR
sudo chmod -R 755 $DEPLOY_DIR

# Start the service
print_step "Starting application service..."
sudo systemctl start $SERVICE_NAME
sudo systemctl enable $SERVICE_NAME

# Wait a moment for the service to start
sleep 3

# Check service status
if systemctl is-active --quiet $SERVICE_NAME; then
    print_status "${GREEN}Service $SERVICE_NAME is running successfully!${NC}"
else
    print_error "Service $SERVICE_NAME failed to start. Check logs with: journalctl -u $SERVICE_NAME"
    exit 1
fi

# Test the application
print_step "Testing application..."
if curl -f -s http://localhost:3000/health > /dev/null 2>&1; then
    print_status "Application is responding on port 3000"
else
    print_warning "Application health check failed. Check the logs."
fi

# Reload Nginx to ensure it picks up any changes
print_step "Reloading Nginx..."
sudo nginx -t && sudo systemctl reload nginx

print_status "${GREEN}Deployment completed successfully!${NC}"
print_status "Application is available at: https://api4js.com.tr"
print_status "Service status: systemctl status $SERVICE_NAME"
print_status "Application logs: journalctl -u $SERVICE_NAME -f"
print_status "Nginx logs: tail -f /var/log/nginx/api4js.com.tr.access.log"

echo -e "${GREEN}=== Deployment Summary ===${NC}"
echo -e "Application: $APP_NAME"
echo -e "Service: $SERVICE_NAME"
echo -e "Directory: $DEPLOY_DIR"
echo -e "URL: https://api4js.com.tr"
echo -e "Status: $(systemctl is-active $SERVICE_NAME)"
