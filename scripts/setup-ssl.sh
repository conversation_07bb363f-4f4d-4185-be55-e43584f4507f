#!/bin/bash

# SSL Certificate Setup Script for api4js.com.tr
# This script sets up Let's Encrypt SSL certificates using Certbot

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="api4js.com.tr"
EMAIL="<EMAIL>"  # Replace with your actual email
WEBROOT="/var/www/certbot"

echo -e "${GREEN}=== SSL Certificate Setup for $DOMAIN ===${NC}"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}This script must be run as root${NC}"
   exit 1
fi

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Update system packages
print_status "Updating system packages..."
apt update

# Install Certbot and Nginx plugin
print_status "Installing Certbot..."
apt install -y certbot python3-certbot-nginx

# Create webroot directory for Let's Encrypt challenges
print_status "Creating webroot directory..."
mkdir -p $WEBROOT
chown -R www-data:www-data $WEBROOT

# Check if certificates already exist
if [ -d "/etc/letsencrypt/live/$DOMAIN" ]; then
    print_warning "Certificates for $DOMAIN already exist."
    read -p "Do you want to renew them? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Renewing certificates..."
        certbot renew --nginx
    else
        print_status "Skipping certificate generation."
    fi
else
    # Prompt for email if not set
    if [ "$EMAIL" = "<EMAIL>" ]; then
        read -p "Enter your email address for Let's Encrypt notifications: " EMAIL
    fi
    
    # Generate SSL certificates
    print_status "Generating SSL certificates for $DOMAIN..."
    certbot certonly \
        --webroot \
        --webroot-path=$WEBROOT \
        --email $EMAIL \
        --agree-tos \
        --no-eff-email \
        -d $DOMAIN \
        -d www.$DOMAIN
fi

# Set up automatic renewal
print_status "Setting up automatic certificate renewal..."

# Create renewal script
cat > /etc/cron.d/certbot-renewal << EOF
# Automatic certificate renewal for Let's Encrypt
0 12 * * * root /usr/bin/certbot renew --quiet --post-hook "systemctl reload nginx"
EOF

# Test automatic renewal
print_status "Testing automatic renewal..."
certbot renew --dry-run

# Set proper permissions
print_status "Setting certificate permissions..."
chmod 600 /etc/letsencrypt/live/$DOMAIN/privkey.pem
chmod 644 /etc/letsencrypt/live/$DOMAIN/fullchain.pem

# Generate Diffie-Hellman parameters for enhanced security
print_status "Generating Diffie-Hellman parameters (this may take a while)..."
if [ ! -f /etc/ssl/certs/dhparam.pem ]; then
    openssl dhparam -out /etc/ssl/certs/dhparam.pem 2048
fi

# Test Nginx configuration
print_status "Testing Nginx configuration..."
nginx -t

if [ $? -eq 0 ]; then
    print_status "Reloading Nginx..."
    systemctl reload nginx
    print_status "${GREEN}SSL certificates have been successfully set up for $DOMAIN!${NC}"
    print_status "Your site should now be accessible via HTTPS."
else
    print_error "Nginx configuration test failed. Please check your configuration."
    exit 1
fi

# Display certificate information
print_status "Certificate information:"
certbot certificates

echo -e "${GREEN}=== SSL Setup Complete ===${NC}"
echo -e "Your SSL certificates are now active and will auto-renew."
echo -e "Certificate location: /etc/letsencrypt/live/$DOMAIN/"
echo -e "Auto-renewal is configured via cron job."
