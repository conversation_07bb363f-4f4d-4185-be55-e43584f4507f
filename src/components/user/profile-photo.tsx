'use client';

import { useState } from 'react';
import { RefreshCw } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { UserAvatar } from '@/components/user/user-avatar';
import { useUserPhoto } from '@/hooks/useUserPhoto';

interface ProfilePhotoProps {
  userName?: string;
  userSurname?: string;
  className?: string;
}

export function ProfilePhoto({ 
  userName, 
  userSurname, 
  className 
}: ProfilePhotoProps) {
  const { photoSrc, isLoading, error, refreshPhoto } = useUserPhoto();
  const [refreshing, setRefreshing] = useState(false);
  
  const handleRefresh = async () => {
    setRefreshing(true);
    await refreshPhoto();
    setRefreshing(false);
  };
  
  return (
    <Card className={className}>
      <CardContent className="pt-6">
        <div className="flex flex-col items-center space-y-4">
          <UserAvatar 
            userName={userName} 
            userSurname={userSurname} 
            size="xl" 
          />
          
          <div className="text-center">
            <h3 className="text-lg font-medium">
              {userName} {userSurname}
            </h3>
            <p className="text-sm text-muted-foreground">Profile Photo</p>
          </div>
          
          {error && (
            <p className="text-sm text-destructive text-center">
              {error.message}
            </p>
          )}
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={isLoading || refreshing}
            className="mt-2"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh Photo
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
