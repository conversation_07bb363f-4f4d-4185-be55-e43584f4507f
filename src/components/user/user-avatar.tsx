'use client';

import { useUserPhoto } from '@/hooks/useUserPhoto';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface UserAvatarProps {
  userName?: string;
  userSurname?: string;
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
}

export function UserAvatar({
  userName,
  userSurname,
  size = "md",
  className
}: UserAvatarProps) {
  const { photoSrc, isLoading } = useUserPhoto();

  // Generate initials for fallback
  const initials = userName && userSurname
    ? `${userName[0]}${userSurname[0]}`.toUpperCase()
    : undefined;

  // Generate alt text
  const altText = userName && userSurname
    ? `${userName} ${userSurname}'s profile photo`
    : 'User profile photo';

  if (isLoading) {
    return (
      <Skeleton
        className={cn(
          "rounded-full",
          size === "sm" && "h-8 w-8",
          size === "md" && "h-10 w-10",
          size === "lg" && "h-14 w-14",
          size === "xl" && "h-20 w-20",
          className
        )}
      />
    );
  }

  return (
    <Avatar
      className={cn(
        size === "sm" && "h-8 w-8",
        size === "md" && "h-10 w-10",
        size === "lg" && "h-14 w-14",
        size === "xl" && "h-20 w-20",
        className
      )}
    >
      {photoSrc && (
        <AvatarImage
          src={photoSrc}
          alt={altText}
        />
      )}
      <AvatarFallback>
        {initials ? (
          <span className="text-xs font-medium">{initials}</span>
        ) : (
          <span className="text-xs font-medium">?</span>
        )}
      </AvatarFallback>
    </Avatar>
  );
}
