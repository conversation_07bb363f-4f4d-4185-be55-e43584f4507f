'use client';

import { useState } from 'react';
import { BellRing } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useNotifications } from '@/contexts/notifications-context';

export function AppNotifications() {
  const [open, setOpen] = useState(false);
  const { appUnreadCount } = useNotifications();

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <BellRing className="h-5 w-5" />
          <Badge
            variant="secondary"
            className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-gray-200 text-gray-500"
          >
            {appUnreadCount}
          </Badge>
          <span className="sr-only">App Notifications</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="font-semibold">Uygulama Bildirimleri</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <div className="p-4 text-center">
          <div className="flex flex-col items-center justify-center py-4">
            <BellRing className="h-10 w-10 text-muted-foreground mb-2" />
            <h3 className="font-medium">Çok Yakında</h3>
            <p className="text-sm text-muted-foreground mt-1">
              Uygulama bildirimleri yakında kullanıma sunulacaktır.
            </p>
          </div>
        </div>

        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="justify-center font-medium text-muted-foreground"
          disabled
        >
          Tüm Bildirimleri Görüntüle
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
