'use client';

import { useState } from 'react';
import { Bell } from 'lucide-react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { useNotifications } from '@/contexts/notifications-context';

export function UyapNotifications() {
  const [open, setOpen] = useState(false);
  const {
    uyapNotifications: notifications,
    uyapUnreadCount: unreadCount,
    isLoading,
    error,
    refreshNotifications,
    markUyapNotificationAsRead: markAsRead
  } = useNotifications();

  // Format date from "Mar 24, 2025 9:35:41 AM" to a more readable format
  const formatDate = (dateString: string) => {
    try {
      // Parse the date string
      const date = new Date(dateString);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return dateString; // Return original if parsing fails
      }

      // Format the date
      return format(date, 'dd MMM yyyy HH:mm', { locale: tr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge
              variant="default"
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
          <span className="sr-only">UYAP Notifications</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="font-semibold">UYAP Bildirimleri</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {isLoading ? (
          <div className="p-2 space-y-2">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        ) : error ? (
          <div className="p-4 text-center text-sm text-muted-foreground">
            {error}
            <Button
              variant="outline"
              size="sm"
              className="mt-2 w-full"
              onClick={() => refreshNotifications()}
            >
              Yeniden Dene
            </Button>
          </div>
        ) : notifications.length === 0 ? (
          <div className="p-4 text-center text-sm text-muted-foreground">
            Bildirim bulunmamaktadır.
          </div>
        ) : (
          <ScrollArea className="h-[300px]">
            <div className="p-2 space-y-1">
              {notifications.map((notification) => (
                <DropdownMenuItem
                  key={notification.bildirimId}
                  className={cn(
                    "flex flex-col items-start p-3 cursor-pointer",
                    !notification.okunduMu && "bg-accent/50"
                  )}
                  onClick={() => markAsRead(notification.bildirimId)}
                >
                  <div className="flex justify-between w-full">
                    <span className="font-medium">{notification.baslik}</span>
                    {!notification.okunduMu && (
                      <Badge variant="default" className="ml-2 h-5">Yeni</Badge>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                    {notification.mesaj}
                  </p>
                  <span className="text-xs text-muted-foreground mt-1">
                    {formatDate(notification.gonderilmeTarihi)}
                  </span>
                </DropdownMenuItem>
              ))}
            </div>
          </ScrollArea>
        )}

        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="justify-center font-medium"
          asChild
        >
          <a href="/dashboard/notifications">Tüm Bildirimleri Görüntüle</a>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
