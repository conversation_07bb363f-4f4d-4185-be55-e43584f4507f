'use client';

import { Badge } from '@/components/ui/badge';
import { useNotifications } from '@/contexts/notifications-context';

interface NotificationCountProps {
  className?: string;
}

export function NotificationCount({ className }: NotificationCountProps) {
  const { uyapUnreadCount, appUnreadCount } = useNotifications();
  
  // Total unread count
  const totalUnreadCount = uyapUnreadCount + appUnreadCount;
  
  if (totalUnreadCount === 0) {
    return null;
  }
  
  return (
    <Badge 
      variant="default" 
      className={className}
    >
      {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
    </Badge>
  );
}
