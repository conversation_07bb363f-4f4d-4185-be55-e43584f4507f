'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Edit2, Trash2, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { CaseNote } from '@/lib/schemas/case-notes';
import { caseNotesService } from '@/lib/api';

interface CaseNoteListProps {
  notes: CaseNote[];
  onEdit: (note: CaseNote) => void;
  onDelete: (noteId: number) => void;
}

export function CaseNoteList({ notes, onEdit, onDelete }: CaseNoteListProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [noteToDelete, setNoteToDelete] = useState<CaseNote | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Format date from epoch time (seconds with decimal precision)
  const formatDate = (epochTime: number) => {
    // Convert seconds to milliseconds by multiplying by 1000
    return format(new Date(epochTime * 1000), 'dd MMMM yyyy HH:mm', { locale: tr });
  };

  // Get initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!noteToDelete) return;

    try {
      setIsDeleting(true);
      setError(null);
      await caseNotesService.deleteCaseNote(noteToDelete.id);
      onDelete(noteToDelete.id);
      setDeleteDialogOpen(false);
    } catch (err) {
      console.error('Delete note error:', err);
      setError(err instanceof Error ? err.message : 'Not silinirken bir hata oluştu.');
    } finally {
      setIsDeleting(false);
    }
  };

  // Open delete dialog
  const openDeleteDialog = (note: CaseNote) => {
    setNoteToDelete(note);
    setDeleteDialogOpen(true);
  };

  if (notes.length === 0) {
    return (
      <div className="bg-muted/30 p-4 rounded-lg">
        <p className="text-sm text-muted-foreground">
          Henüz not eklenmemiş. Yeni not eklemek için "Yeni Not Ekle" butonunu kullanabilirsiniz.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {error && (
        <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
          {error}
        </div>
      )}

      {notes.map((note) => (
        <div key={note.id} className="bg-muted/30 p-4 rounded-lg">
          <div className="flex items-start gap-3">
            <Avatar className="h-8 w-8">
              <AvatarFallback>{getInitials(note.ownerName)}</AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">{note.ownerName}</span>
                  <span className="text-xs text-muted-foreground">
                    {formatDate(note.createdAt)}
                  </span>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7"
                    onClick={() => onEdit(note)}
                    title="Düzenle"
                  >
                    <Edit2 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 text-destructive"
                    onClick={() => openDeleteDialog(note)}
                    title="Sil"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <p className="text-sm whitespace-pre-wrap break-words">{note.content}</p>
              {note.updatedAt > note.createdAt && (
                <p className="text-xs text-muted-foreground mt-2">
                  Son düzenleme: {formatDate(note.updatedAt)}
                </p>
              )}
            </div>
          </div>
        </div>
      ))}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Notu silmek istediğinize emin misiniz?</AlertDialogTitle>
            <AlertDialogDescription>
              Bu işlem geri alınamaz. Not kalıcı olarak silinecektir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDeleteConfirm();
              }}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Siliniyor...' : 'Sil'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
