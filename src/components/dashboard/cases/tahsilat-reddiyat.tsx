'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { RefreshCw, AlertCircle, Info, FileText, Receipt, ArrowLeftRight, FileSignature } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';

import { casesService } from '@/lib/api';
import { TahsilatReddiyatResponse, Tahsilat, Reddiyat, Harc } from '@/lib/schemas/tahsilat-reddiyat';

interface TahsilatReddiyatProps {
  readonly caseNumber: string;
}

export function TahsilatReddiyat({ caseNumber }: TahsilatReddiyatProps) {
  const [data, setData] = useState<TahsilatReddiyatResponse | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [errorCode, setErrorCode] = useState<string | null>(null);

  // Dialog states
  const [selectedTahsilat, setSelectedTahsilat] = useState<Tahsilat | null>(null);
  const [selectedReddiyat, setSelectedReddiyat] = useState<Reddiyat | null>(null);
  const [selectedHarc, setSelectedHarc] = useState<Harc | null>(null);
  const [tahsilatDialogOpen, setTahsilatDialogOpen] = useState<boolean>(false);
  const [reddiyatDialogOpen, setReddiyatDialogOpen] = useState<boolean>(false);
  const [harcDialogOpen, setHarcDialogOpen] = useState<boolean>(false);

  // Format date from string like "May 5, 2025 11:06:15 AM"
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'dd MMM yyyy HH:mm', { locale: tr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Fetch tahsilat-reddiyat data
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      setErrorCode(null);
      const response = await casesService.getTahsilatReddiyat(caseNumber);
      setData(response);
    } catch (err) {
      console.error('Failed to fetch tahsilat-reddiyat data:', err);

      // Extract error message and code if available
      if (err instanceof Error) {
        const errorMessage = err.message;

        // Check if the error message contains an error code in parentheses
        const errorCodeMatch = errorMessage.match(/\(([^)]+)\)$/);
        if (errorCodeMatch && errorCodeMatch[1]) {
          setErrorCode(errorCodeMatch[1]);
          // Remove the error code from the message
          setError(errorMessage.replace(/\s*\([^)]+\)$/, ''));
        } else {
          setError(errorMessage);
        }
      } else {
        setError('Tahsilat ve reddiyat bilgileri yüklenirken bir hata oluştu.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, [caseNumber]);

  // Render loading state
  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-full max-w-sm" />
        <Skeleton className="h-64 w-full" />
        <Skeleton className="h-64 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-5 w-5" />
        <AlertTitle className="font-semibold">
          {errorCode ? `Hata (${errorCode})` : 'Hata'}
        </AlertTitle>
        <AlertDescription className="mt-2">
          <p className="mb-4">{error}</p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchData}
            >
              Tekrar Dene
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                console.log('Detailed error information:', { error, errorCode, caseNumber });
              }}
            >
              Detayları Göster
            </Button>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Render empty state
  if (!data) {
    return (
      <Alert className="mb-4">
        <Info className="h-5 w-5" />
        <AlertTitle className="font-semibold">Veri Bulunamadı</AlertTitle>
        <AlertDescription className="mt-2">
          <p className="mb-4">Bu dosya için tahsilat ve reddiyat bilgileri bulunamadı.</p>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchData}
          >
            Tekrar Dene
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Card */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle>Özet Bilgiler</CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchData}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Yenile
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <div className="bg-muted/30 p-3 rounded-md">
              <p className="text-sm text-muted-foreground">Toplam Tahsilat</p>
              <p className="text-lg font-semibold">{formatCurrency(data.toplamTahsilat || 0)}</p>
            </div>
            <div className="bg-muted/30 p-3 rounded-md">
              <p className="text-sm text-muted-foreground">Toplam Reddiyat</p>
              <p className="text-lg font-semibold">{formatCurrency(data.toplamreddiyat || 0)}</p>
            </div>
            <div className="bg-muted/30 p-3 rounded-md">
              <p className="text-sm text-muted-foreground">Toplam Harç</p>
              <p className="text-lg font-semibold">{formatCurrency(data.toplamTahsilHarci || 0)}</p>
            </div>
            <div className="bg-muted/30 p-3 rounded-md">
              <p className="text-sm text-muted-foreground">Toplam Kalan</p>
              <p className="text-lg font-semibold">{formatCurrency(data.toplamKalan || 0)}</p>
            </div>
            <div className="bg-muted/30 p-3 rounded-md">
              <p className="text-sm text-muted-foreground">Haricen</p>
              <p className="text-lg font-semibold">{formatCurrency(data.haricen || 0)}</p>
            </div>
            <div className="bg-muted/30 p-3 rounded-md">
              <p className="text-sm text-muted-foreground">Toplam Teminat</p>
              <p className="text-lg font-semibold">{formatCurrency(data.toplamTeminat || 0)}</p>
            </div>
            <div className={`bg-muted/30 p-3 rounded-md ${data.isIcraMi ? 'border border-red-500' : ''}`}>
              <p className="text-sm text-muted-foreground">İcra Durumu</p>
              <p className={`text-lg font-semibold ${data.isIcraMi ? 'text-red-600' : ''}`}>
                {data.isIcraMi ? 'Evet' : 'Hayır'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tahsilat (Collections) Table */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Tahsilatlar</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[300px] w-full">
            <Table className="w-full">
              <TableHeader className="sticky top-0 bg-background">
                <TableRow>
                  <TableHead>Makbuz No</TableHead>
                  <TableHead>Ödeyen Kişi</TableHead>
                  <TableHead>Tahsilat Türü</TableHead>
                  <TableHead>Tarih</TableHead>
                  <TableHead>Kalan Miktar</TableHead>
                  <TableHead>Tahsilat</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {!data.tahsilatList || data.tahsilatList.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      Tahsilat kaydı bulunamadı
                    </TableCell>
                  </TableRow>
                ) : (
                  data.tahsilatList.map((tahsilat, index) => (
                    <TableRow
                      key={`${tahsilat.makbuzNo}-${index}`}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        setSelectedTahsilat(tahsilat);
                        setTahsilatDialogOpen(true);
                      }}
                      title="Detayları görmek için tıklayın"
                    >
                      <TableCell className="font-medium">{tahsilat.makbuzNo}</TableCell>
                      <TableCell className="break-words">{tahsilat.odeyenKisi}</TableCell>
                      <TableCell className="break-words">{tahsilat.tahsilatTuru}</TableCell>
                      <TableCell>{formatDate(tahsilat.tahsilatTarihi)}</TableCell>
                      <TableCell>{formatCurrency(tahsilat.kalanMiktar || 0)}</TableCell>
                      <TableCell>{formatCurrency(tahsilat.tahsilatTutari || 0)}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Reddiyat (Refunds) Table */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Reddiyatlar</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[300px] w-full">
            <Table className="w-full">
              <TableHeader className="sticky top-0 bg-background">
                <TableRow>
                  <TableHead>Makbuz No</TableHead>
                  <TableHead>Ödeyen Kişi</TableHead>
                  <TableHead>Reddiyat Nedeni</TableHead>
                  <TableHead>Durum</TableHead>
                  <TableHead>Tarih</TableHead>
                  <TableHead>Miktar</TableHead>
                  <TableHead>Ödenecek</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {!data.reddiyatList || data.reddiyatList.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4">
                      Reddiyat kaydı bulunamadı
                    </TableCell>
                  </TableRow>
                ) : (
                  data.reddiyatList.map((reddiyat, index) => (
                    <TableRow
                      key={`${reddiyat.makbuzNo}-${index}`}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        setSelectedReddiyat(reddiyat);
                        setReddiyatDialogOpen(true);
                      }}
                      title="Detayları görmek için tıklayın"
                    >
                      <TableCell className="font-medium">{reddiyat.makbuzNo}</TableCell>
                      <TableCell className="break-words">{reddiyat.odeyenKisi}</TableCell>
                      <TableCell className="break-words">{reddiyat.reddiyatNedeni}</TableCell>
                      <TableCell>
                        {reddiyat.durumAciklama && (
                          <Badge variant="outline" className="whitespace-nowrap">
                            {reddiyat.durumAciklama}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>{formatDate(reddiyat.reddiyatTarihi)}</TableCell>
                      <TableCell>{formatCurrency(reddiyat.miktar)}</TableCell>
                      <TableCell>{formatCurrency(reddiyat.odenecekMiktar)}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Harç (Fees) Table */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Harçlar</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[300px] w-full">
            <Table className="w-full">
              <TableHeader className="sticky top-0 bg-background">
                <TableRow>
                  <TableHead>Makbuz No</TableHead>
                  <TableHead>Ödeyen Kişi</TableHead>
                  <TableHead>Harç Türü</TableHead>
                  <TableHead>Tarih</TableHead>
                  <TableHead>Yatırılan</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {!data.harcList || data.harcList.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4">
                      Harç kaydı bulunamadı
                    </TableCell>
                  </TableRow>
                ) : (
                  data.harcList.map((harc, index) => (
                    <TableRow
                      key={`${harc.makbuzNo}-${index}`}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => {
                        setSelectedHarc(harc);
                        setHarcDialogOpen(true);
                      }}
                      title="Detayları görmek için tıklayın"
                    >
                      <TableCell className="font-medium">{harc.makbuzNo}</TableCell>
                      <TableCell className="break-words">{harc.odeyenKisi}</TableCell>
                      <TableCell className="break-words">{harc.tahsilatTuru}</TableCell>
                      <TableCell>{formatDate(harc.tahsilatTarihi)}</TableCell>
                      <TableCell>{formatCurrency(harc.yatirilanMiktar || harc.miktar || 0)}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Tahsilat Details Dialog */}
      <Dialog open={tahsilatDialogOpen} onOpenChange={setTahsilatDialogOpen}>
        <DialogContent className="sm:max-w-[600px] w-full max-h-[85vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 flex-wrap">
              <span className="break-words">Tahsilat Detayları</span>
              {data.isIcraMi && (
                <Badge variant="destructive" className="ml-2">İcra</Badge>
              )}
            </DialogTitle>
            <DialogDescription className="break-words">
              {selectedTahsilat?.makbuzNo} - {selectedTahsilat?.tahsilatTuru}
            </DialogDescription>
          </DialogHeader>
          {selectedTahsilat && (
            <div className="space-y-4">
              <div className="bg-muted/30 p-3 sm:p-4 rounded-lg">
                <h3 className="text-base font-semibold mb-2 sm:mb-3 flex items-center">
                  <FileText className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="break-words">Temel Bilgiler</span>
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Makbuz No</p>
                    <p className="font-medium break-words">{selectedTahsilat.makbuzNo}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Dosya No</p>
                    <p className="font-medium break-words">{selectedTahsilat.dosyaNo}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Dosya Türü</p>
                    <p className="font-medium break-words">{selectedTahsilat.dosyaTurAciklama}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Birim Adı</p>
                    <p className="font-medium break-words">{selectedTahsilat.birimAdi}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Ödeyen Kişi</p>
                    <p className="font-medium break-words">{selectedTahsilat.odeyenKisi}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Tahsilat Türü</p>
                    <p className="font-medium break-words">{selectedTahsilat.tahsilatTuru}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Tahsilat Tarihi</p>
                    <p className="font-medium break-words">{formatDate(selectedTahsilat.tahsilatTarihi)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Kayıt ID</p>
                    <p className="font-medium break-words">{selectedTahsilat.kayitId}</p>
                  </div>
                </div>
              </div>

              <div className="bg-muted/30 p-3 sm:p-4 rounded-lg">
                <h3 className="text-base font-semibold mb-2 sm:mb-3 flex items-center">
                  <Receipt className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="break-words">Finansal Bilgiler</span>
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  {selectedTahsilat.hesaplamaYapilanTutar !== undefined && (
                    <div>
                      <p className="text-sm text-muted-foreground">Hesaplama Yapılan Tutar</p>
                      <p className="font-medium break-words">{formatCurrency(selectedTahsilat.hesaplamaYapilanTutar)}</p>
                    </div>
                  )}
                  {selectedTahsilat.odenebilirMiktar !== undefined && (
                    <div>
                      <p className="text-sm text-muted-foreground">Ödenebilir Miktar</p>
                      <p className="font-medium break-words">{formatCurrency(selectedTahsilat.odenebilirMiktar)}</p>
                    </div>
                  )}
                  {selectedTahsilat.kalanMiktar !== undefined && (
                    <div>
                      <p className="text-sm text-muted-foreground">Kalan Miktar</p>
                      <p className="font-medium break-words">{formatCurrency(selectedTahsilat.kalanMiktar)}</p>
                    </div>
                  )}
                  {selectedTahsilat.tahsilatTutari !== undefined && (
                    <div>
                      <p className="text-sm text-muted-foreground">Tahsilat Tutarı</p>
                      <p className="font-medium break-words">{formatCurrency(selectedTahsilat.tahsilatTutari)}</p>
                    </div>
                  )}
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setTahsilatDialogOpen(false)} className="w-full sm:w-auto">
                  Kapat
                </Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Reddiyat Details Dialog */}
      <Dialog open={reddiyatDialogOpen} onOpenChange={setReddiyatDialogOpen}>
        <DialogContent className="sm:max-w-[600px] w-full max-h-[85vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 flex-wrap">
              <span className="break-words">Reddiyat Detayları</span>
              {data.isIcraMi && (
                <Badge variant="destructive" className="ml-2">İcra</Badge>
              )}
            </DialogTitle>
            <DialogDescription className="break-words">
              {selectedReddiyat?.makbuzNo} - {selectedReddiyat?.reddiyatNedeni}
            </DialogDescription>
          </DialogHeader>
          {selectedReddiyat && (
            <div className="space-y-4">
              <div className="bg-muted/30 p-3 sm:p-4 rounded-lg">
                <h3 className="text-base font-semibold mb-2 sm:mb-3 flex items-center">
                  <FileText className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="break-words">Temel Bilgiler</span>
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Makbuz No</p>
                    <p className="font-medium break-words">{selectedReddiyat.makbuzNo}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Dosya No</p>
                    <p className="font-medium break-words">{selectedReddiyat.dosyaNo}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Dosya Türü</p>
                    <p className="font-medium break-words">{selectedReddiyat.dosyaTurAciklama}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Birim Adı</p>
                    <p className="font-medium break-words">{selectedReddiyat.birimAdi}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Ödeyen Kişi</p>
                    <p className="font-medium break-words">{selectedReddiyat.odeyenKisi}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Reddiyat Nedeni</p>
                    <p className="font-medium break-words">{selectedReddiyat.reddiyatNedeni}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Reddiyat Tarihi</p>
                    <p className="font-medium break-words">{formatDate(selectedReddiyat.reddiyatTarihi)}</p>
                  </div>
                  {selectedReddiyat.kayitId !== undefined && (
                    <div>
                      <p className="text-sm text-muted-foreground">Kayıt ID</p>
                      <p className="font-medium break-words">{selectedReddiyat.kayitId}</p>
                    </div>
                  )}
                  {selectedReddiyat.durumAciklama && (
                    <div>
                      <p className="text-sm text-muted-foreground">Durum</p>
                      <Badge variant="outline" className="break-words">{selectedReddiyat.durumAciklama}</Badge>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-muted/30 p-3 sm:p-4 rounded-lg">
                <h3 className="text-base font-semibold mb-2 sm:mb-3 flex items-center">
                  <Receipt className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="break-words">Finansal Bilgiler</span>
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Miktar</p>
                    <p className="font-medium break-words">{formatCurrency(selectedReddiyat.miktar)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Ödenecek Miktar</p>
                    <p className="font-medium break-words">{formatCurrency(selectedReddiyat.odenecekMiktar)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Gelir Vergisi</p>
                    <p className="font-medium break-words">{formatCurrency(selectedReddiyat.gelirVergisi)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Damga Vergisi</p>
                    <p className="font-medium break-words">{formatCurrency(selectedReddiyat.damgaVergisi)}</p>
                  </div>
                  {selectedReddiyat.cezaeviHarcTutari !== undefined && (
                    <div>
                      <p className="text-sm text-muted-foreground">Cezaevi Harç Tutarı</p>
                      <p className="font-medium break-words">{formatCurrency(selectedReddiyat.cezaeviHarcTutari)}</p>
                    </div>
                  )}
                  {selectedReddiyat.tahsilHarci !== undefined && (
                    <div>
                      <p className="text-sm text-muted-foreground">Tahsil Harcı</p>
                      <p className="font-medium break-words">{formatCurrency(selectedReddiyat.tahsilHarci)}</p>
                    </div>
                  )}
                </div>
              </div>

              {selectedReddiyat.kapattigiTahsilatIDler && (
                <div className="bg-muted/30 p-3 sm:p-4 rounded-lg">
                  <h3 className="text-base font-semibold mb-2 sm:mb-3 flex items-center">
                    <ArrowLeftRight className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span className="break-words">İlişkili Tahsilatlar</span>
                  </h3>
                  <div>
                    <p className="text-sm text-muted-foreground">Kapattığı Tahsilat ID'leri</p>
                    <p className="font-medium break-words">{selectedReddiyat.kapattigiTahsilatIDler}</p>
                  </div>
                </div>
              )}

              <DialogFooter>
                <Button variant="outline" onClick={() => setReddiyatDialogOpen(false)} className="w-full sm:w-auto">
                  Kapat
                </Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Harc Details Dialog */}
      <Dialog open={harcDialogOpen} onOpenChange={setHarcDialogOpen}>
        <DialogContent className="sm:max-w-[600px] w-full max-h-[85vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 flex-wrap">
              <span className="break-words">Harç Detayları</span>
              {data.isIcraMi && (
                <Badge variant="destructive" className="ml-2">İcra</Badge>
              )}
            </DialogTitle>
            <DialogDescription className="break-words">
              {selectedHarc?.makbuzNo} - {selectedHarc?.tahsilatTuru}
            </DialogDescription>
          </DialogHeader>
          {selectedHarc && (
            <div className="space-y-4">
              <div className="bg-muted/30 p-3 sm:p-4 rounded-lg">
                <h3 className="text-base font-semibold mb-2 sm:mb-3 flex items-center">
                  <FileSignature className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="break-words">Temel Bilgiler</span>
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Makbuz No</p>
                    <p className="font-medium break-words">{selectedHarc.makbuzNo}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Dosya No</p>
                    <p className="font-medium break-words">{selectedHarc.dosyaNo}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Dosya Türü</p>
                    <p className="font-medium break-words">{selectedHarc.dosyaTurAciklama}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Birim Adı</p>
                    <p className="font-medium break-words">{selectedHarc.birimAdi}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Ödeyen Kişi</p>
                    <p className="font-medium break-words">{selectedHarc.odeyenKisi}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Harç Türü</p>
                    <p className="font-medium break-words">{selectedHarc.tahsilatTuru}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Tahsilat Tarihi</p>
                    <p className="font-medium break-words">{formatDate(selectedHarc.tahsilatTarihi)}</p>
                  </div>
                  {selectedHarc.kayitId !== undefined && (
                    <div>
                      <p className="text-sm text-muted-foreground">Kayıt ID</p>
                      <p className="font-medium break-words">{selectedHarc.kayitId}</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-muted/30 p-3 sm:p-4 rounded-lg">
                <h3 className="text-base font-semibold mb-2 sm:mb-3 flex items-center">
                  <Receipt className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="break-words">Finansal Bilgiler</span>
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  {selectedHarc.yatirilanMiktar !== undefined && (
                    <div>
                      <p className="text-sm text-muted-foreground">Yatırılan Miktar</p>
                      <p className="font-medium break-words">{formatCurrency(selectedHarc.yatirilanMiktar)}</p>
                    </div>
                  )}
                  {selectedHarc.miktar !== undefined && (
                    <div>
                      <p className="text-sm text-muted-foreground">Miktar</p>
                      <p className="font-medium break-words">{formatCurrency(selectedHarc.miktar)}</p>
                    </div>
                  )}
                  {selectedHarc.hesaplamaYapilanTutar !== undefined && (
                    <div>
                      <p className="text-sm text-muted-foreground">Hesaplama Yapılan Tutar</p>
                      <p className="font-medium break-words">{formatCurrency(selectedHarc.hesaplamaYapilanTutar)}</p>
                    </div>
                  )}
                  {selectedHarc.odenebilirMiktar !== undefined && (
                    <div>
                      <p className="text-sm text-muted-foreground">Ödenebilir Miktar</p>
                      <p className="font-medium break-words">{formatCurrency(selectedHarc.odenebilirMiktar)}</p>
                    </div>
                  )}
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setHarcDialogOpen(false)} className="w-full sm:w-auto">
                  Kapat
                </Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
