'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import {
  caseDetailsSchema,
  type CaseDetailsFormValues,
  type CaseDetails,
  type CaseType,
  type CrimeType,
  caseTypeDisplayMap,
  crimeTypeDisplayMap,
  CaseTypeEnum,
  CrimeTypeEnum,
} from '@/lib/schemas/case-details';
import { caseDetailsService } from '@/lib/api';

interface CaseDetailsFormProps {
  readonly caseNumber: string;
  readonly caseDetails?: CaseDetails;
  readonly onSuccess: (caseDetails: CaseDetails) => void;
  readonly onCancel?: () => void;
  readonly inDialog?: boolean;
}

export function CaseDetailsForm({
  caseNumber,
  caseDetails,
  onSuccess,
  onCancel,
  inDialog = false,
}: CaseDetailsFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isEditing = !!caseDetails;

  // Initialize form with react-hook-form and zod validation
  const form = useForm<CaseDetailsFormValues>({
    resolver: zodResolver(caseDetailsSchema),
    defaultValues: {
      caseNumber: caseNumber,
      // Set a default case type if none is provided
      caseType: (caseDetails?.caseType as CaseType) ?? 'HUKUK_DAVASI',
      crimeType: (caseDetails?.crimeType as CrimeType) ?? undefined,
      derdest: caseDetails?.derdest ?? true,
      caseValue: caseDetails?.caseValue ?? undefined,
      caseReason: caseDetails?.caseReason ?? '',
      caseTitle: caseDetails?.caseTitle ?? '',
    },
    mode: "onChange",
  });

  // Log form state for debugging
  useEffect(() => {
    console.log("Form state:", form.formState);
    console.log("Form values:", form.getValues());
  }, [form.formState, form]);

  // Watch caseType to conditionally render crimeType field
  const caseType = form.watch('caseType');

  // Effect to validate crimeType when caseType changes
  useEffect(() => {
    // Clear any existing errors on crimeType
    form.clearErrors('crimeType');

    // If caseType is CEZA_DAVASI and crimeType is not set, show error
    if (caseType === 'CEZA_DAVASI' && !form.getValues('crimeType')) {
      form.setError('crimeType', {
        type: 'manual',
        message: 'Ceza davası için suç türü gereklidir'
      });
    }

    // If caseType is not CEZA_DAVASI, clear crimeType
    if (caseType && caseType !== 'CEZA_DAVASI') {
      form.setValue('crimeType', undefined);
    }
  }, [caseType, form]);

  // Handle form submission
  const onSubmit = async (data: CaseDetailsFormValues) => {
    try {
      // Validate all required fields
      let hasErrors = false;

      // Validate caseType
      if (!data.caseType) {
        form.setError('caseType', {
          type: 'manual',
          message: 'Dosya türü gereklidir'
        });
        hasErrors = true;
      }

      // Validate crimeType for CEZA_DAVASI
      if (data.caseType === 'CEZA_DAVASI' && !data.crimeType) {
        form.setError('crimeType', {
          type: 'manual',
          message: 'Ceza davası için suç türü gereklidir'
        });
        hasErrors = true;
      }

      // Validate caseValue
      if (data.caseValue === undefined || data.caseValue === null) {
        form.setError('caseValue', {
          type: 'manual',
          message: 'Dava değeri gereklidir'
        });
        hasErrors = true;
      }

      // Validate caseTitle
      if (!data.caseTitle) {
        form.setError('caseTitle', {
          type: 'manual',
          message: 'Dava başlığı gereklidir'
        });
        hasErrors = true;
      }

      if (hasErrors) {
        return;
      }

      setIsLoading(true);
      setError(null);

      console.log("Submitting form with data:", data);

      // Create a copy of the data to avoid modifying the original
      const formData = { ...data };

      // Convert caseValue to number if it's a string
      if (typeof formData.caseValue === 'string' && formData.caseValue !== '') {
        formData.caseValue = parseFloat(formData.caseValue);
      }

      // If caseType is not CEZA_DAVASI, remove crimeType
      if (formData.caseType !== 'CEZA_DAVASI') {
        formData.crimeType = undefined;
      }

      let result: CaseDetails;

      if (isEditing && caseDetails) {
        console.log("Updating case details with ID:", caseDetails.id);
        // Update existing case details
        result = await caseDetailsService.updateCaseDetails(caseDetails.id, formData);
      } else {
        console.log("Creating new case details");
        // Create new case details
        result = await caseDetailsService.createCaseDetails(formData);
      }

      console.log("API response:", result);
      onSuccess(result);
    } catch (err) {
      console.error('Case details form error:', err);
      setError(err instanceof Error ? err.message : 'Bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {error && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Case Number Field - Read Only */}
          <FormField
            control={form.control}
            name="caseNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Dosya Numarası</FormLabel>
                <FormControl>
                  <Input {...field} disabled={true} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Case Type Field */}
          <FormField
            control={form.control}
            name="caseType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Dosya Türü <span className="text-destructive">*</span></FormLabel>
                <Select
                  disabled={isLoading}
                  onValueChange={(value) => {
                    field.onChange(value);
                    // If changing from CEZA_DAVASI to another type, clear crimeType
                    if (value !== 'CEZA_DAVASI') {
                      form.setValue('crimeType', undefined);
                    }
                  }}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Dosya türü seçin" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(caseTypeDisplayMap).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Dosya türünü seçin
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Crime Type Field - Only show if Case Type is CEZA_DAVASI */}
        {caseType === 'CEZA_DAVASI' && (
          <FormField
            control={form.control}
            name="crimeType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Suç Türü <span className="text-destructive">*</span></FormLabel>
                <Select
                  disabled={isLoading}
                  onValueChange={field.onChange}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Suç türü seçin" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(crimeTypeDisplayMap).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Ceza davası için suç türü seçilmelidir
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Derdest Field */}
          <FormField
            control={form.control}
            name="derdest"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isLoading}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Derdest</FormLabel>
                  <FormDescription>
                    Dosya halen devam ediyor mu?
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* Case Value Field */}
          <FormField
            control={form.control}
            name="caseValue"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Dava Değeri <span className="text-destructive">*</span></FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Dava değeri"
                    {...field}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value === '' ? undefined : parseFloat(value));
                    }}
                    value={field.value === undefined ? '' : field.value}
                    disabled={isLoading}
                  />
                </FormControl>
                <FormDescription>
                  Dava değerini sayı olarak girin
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Case Title Field */}
        <FormField
          control={form.control}
          name="caseTitle"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Dava Başlığı <span className="text-destructive">*</span></FormLabel>
              <FormControl>
                <Input
                  placeholder="Dava başlığı"
                  {...field}
                  disabled={isLoading}
                />
              </FormControl>
              <FormDescription>
                Dava başlığını girin (örn. "Ahmet Yılmaz vs. XYZ Şirketi")
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Case Reason Field */}
        <FormField
          control={form.control}
          name="caseReason"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Dava Nedeni <span className="text-muted-foreground text-sm">(Opsiyonel)</span></FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Dava nedeni (opsiyonel)"
                  className="min-h-[100px]"
                  {...field}
                  disabled={isLoading}
                />
              </FormControl>
              <FormDescription>
                Dava nedeni hakkında detaylı bilgi girin (opsiyonel)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              İptal
            </Button>
          )}
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditing ? 'Güncelleniyor...' : 'Kaydediliyor...'}
              </>
            ) : isEditing ? (
              'Güncelle'
            ) : (
              'Kaydet'
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
