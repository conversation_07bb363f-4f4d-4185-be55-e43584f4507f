'use client';

import { useState } from 'react';
import { User, Building, Users, UserCheck } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { CaseParty } from '@/lib/api';

interface CasePartiesListProps {
  parties: CaseParty[];
  isLoading?: boolean;
  error?: string | null;
}

export function CasePartiesList({ parties, isLoading = false, error = null }: CasePartiesListProps) {
  const [searchTerm, setSearchTerm] = useState('');

  // Filter parties based on search term
  const filteredParties = parties.filter(party =>
    party.adi.toLowerCase().includes(searchTerm.toLowerCase()) ||
    party.rol.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (party.vekil && party.vekil.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        {[1, 2, 3].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardContent className="p-0">
              <div className="p-4 flex items-start gap-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="bg-destructive/15 p-4 rounded-lg text-destructive">
        <p>{error}</p>
      </div>
    );
  }

  // Render empty state
  if (parties.length === 0) {
    return (
      <div className="bg-muted/30 p-6 rounded-lg flex flex-col items-center justify-center text-center">
        <Users className="h-12 w-12 text-muted-foreground mb-3" />
        <h3 className="text-lg font-medium mb-1">Taraf Bulunamadı</h3>
        <p className="text-sm text-muted-foreground">
          Bu dosya için taraf bilgisi bulunmamaktadır.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search input */}
      <div className="relative">
        <Input
          type="search"
          placeholder="Taraf ara..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
        <Users className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
      </div>

      {/* Empty search results */}
      {filteredParties.length === 0 && searchTerm !== '' && (
        <div className="bg-muted/30 p-4 rounded-lg text-center">
          <p className="text-sm text-muted-foreground">
            "{searchTerm}" ile eşleşen taraf bulunamadı.
          </p>
        </div>
      )}

      {/* Parties list */}
      <ScrollArea className="max-h-[500px]">
        <div className="space-y-3">
          {filteredParties.map((party, index) => (
            <Card key={index} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="p-4 flex items-start gap-3 w-full overflow-hidden">
                  {/* Avatar with icon based on party type */}
                  <Avatar className="h-10 w-10 flex-shrink-0">
                    <AvatarFallback className={party.kisiKurum === 'Kişi' ? 'bg-primary/10 text-primary' : 'bg-orange-500/10 text-orange-500'}>
                      {party.kisiKurum === 'Kişi'
                        ? <User className="h-5 w-5" />
                        : <Building className="h-5 w-5" />}
                    </AvatarFallback>
                  </Avatar>

                  {/* Party details */}
                  <div className="flex-1 min-w-0 overflow-hidden">
                    <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-1 mb-1">
                      <h4 className="font-medium text-sm sm:text-base break-words overflow-hidden">{party.adi}</h4>
                      <Badge variant={party.rol.includes('Başvurucu') || party.rol.includes('Davacı') ? 'default' : 'secondary'} className="whitespace-nowrap text-xs h-5 px-2 py-0 sm:ml-2 flex-shrink-0 mt-0.5">
                        {party.rol}
                      </Badge>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 text-xs text-muted-foreground">
                      <span className="flex items-center gap-1 flex-shrink-0">
                        {party.kisiKurum === 'Kişi' ? <User className="h-3 w-3" /> : <Building className="h-3 w-3" />}
                        {party.kisiKurum}
                      </span>

                      {party.vekil && (
                        <div className="flex items-center gap-1 sm:ml-3 overflow-hidden max-w-full">
                          <UserCheck className="h-3 w-3 text-blue-500 flex-shrink-0" />
                          <span className="text-blue-500 truncate break-words">Vekil: {party.vekil.replace(/[\[\]]/g, '')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
