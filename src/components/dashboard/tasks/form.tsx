'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { CalendarIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';

import { taskSchema, type TaskFormValues, type Task, TaskPriority, TaskStatus, TaskType } from '@/lib/schemas/tasks';
import { casesService, tasksService } from '@/lib/api';

interface TaskFormProps {
  readonly task?: Task;
  readonly onSuccess: (task: Task) => void;
  readonly onCancel?: () => void;
  readonly inDialog?: boolean;
}

export function TaskForm({ task, onSuccess, onCancel, inDialog = false }: TaskFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [startDateValue, setStartDateValue] = useState<Date | undefined>(undefined);
  const [dueDateValue, setDueDateValue] = useState<Date | undefined>(undefined);
  const [caseParties, setCaseParties] = useState<Record<string, { adi: string, rol: string }[]>>({});
  const [casesLoading, setCasesLoading] = useState(false);

  const isEditing = !!task;

  // Initialize form with react-hook-form and zod validation
  const form = useForm<TaskFormValues>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      title: task?.title ?? '',
      description: task?.description ?? '',
      priority: (task?.priority as TaskPriority) ?? 'MEDIUM',
      startDate: task?.startDate ?? '',
      dueDate: task?.dueDate ?? '',
      caseNumber: task?.caseNumber ?? 'none',
      status: (task?.status as TaskStatus) ?? 'OPEN',
      taskType: (task?.taskType as TaskType) ?? 'TASK',
    },
  });

  // Fetch case parties for the case number dropdown
  useEffect(() => {
    const fetchCaseParties = async () => {
      try {
        setCasesLoading(true);
        const casePartiesData = await casesService.getCaseParties();
        setCaseParties(casePartiesData);
      } catch (error) {
        console.error('Failed to fetch case parties:', error);
        setError('Dosya tarafları yüklenirken bir hata oluştu.');
      } finally {
        setCasesLoading(false);
      }
    };

    fetchCaseParties();
  }, []);

  // Set date values if editing an existing task
  useEffect(() => {
    if (task) {
      if (task.startDate) {
        const startDate = new Date(task.startDate);
        setStartDateValue(startDate);
        form.setValue('startDate', task.startDate);
      }

      if (task.dueDate) {
        const dueDate = new Date(task.dueDate);
        setDueDateValue(dueDate);
        form.setValue('dueDate', task.dueDate);
      }
    }
  }, [task, form]);

  // Handle form submission
  const onSubmit = async (data: TaskFormValues) => {
    try {
      setIsLoading(true);
      setError(null);

      // Process the form data
      const processedData = {
        ...data,
        // Convert 'none' to undefined for the backend
        caseNumber: data.caseNumber === 'none' ? undefined : data.caseNumber
      };

      let result: Task;

      if (isEditing && task) {
        // Update existing task
        result = await tasksService.updateTask(task.id, processedData);
      } else {
        // Create new task
        result = await tasksService.createTask(processedData);
      }

      onSuccess(result);
    } catch (err) {
      console.error('Task form error:', err);
      setError(err instanceof Error ? err.message : 'Bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {error && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {error}
          </div>
        )}

        {/* Title Field */}
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Başlık</FormLabel>
              <FormControl>
                <Input placeholder="Görev başlığı" {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Description Field */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Açıklama</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Görev açıklaması"
                  className="min-h-[100px]"
                  {...field}
                  disabled={isLoading}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Priority Field */}
          <FormField
            control={form.control}
            name="priority"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Öncelik</FormLabel>
                <Select
                  disabled={isLoading}
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Öncelik seçin" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="LOW">Düşük</SelectItem>
                    <SelectItem value="MEDIUM">Orta</SelectItem>
                    <SelectItem value="HIGH">Yüksek</SelectItem>
                    <SelectItem value="CRITICAL">Kritik</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Status Field */}
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Durum</FormLabel>
                <Select
                  disabled={isLoading}
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Durum seçin" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="OPEN">Açık</SelectItem>
                    <SelectItem value="IN_PROGRESS">Devam Ediyor</SelectItem>
                    <SelectItem value="COMPLETED">Tamamlandı</SelectItem>
                    <SelectItem value="CANCELLED">İptal Edildi</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Start Date Field */}
          <FormField
            control={form.control}
            name="startDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Başlangıç Tarihi</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                        disabled={isLoading}
                      >
                        {startDateValue ? (
                          format(startDateValue, "PPP", { locale: tr })
                        ) : (
                          <span>Tarih seçin</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={startDateValue}
                      onSelect={(date) => {
                        setStartDateValue(date);
                        field.onChange(date ? date.getTime() : '');
                      }}
                      disabled={isLoading}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Due Date Field */}
          <FormField
            control={form.control}
            name="dueDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Bitiş Tarihi</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                        disabled={isLoading}
                      >
                        {dueDateValue ? (
                          format(dueDateValue, "PPP", { locale: tr })
                        ) : (
                          <span>Tarih seçin</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={dueDateValue}
                      onSelect={(date) => {
                        setDueDateValue(date);
                        field.onChange(date ? date.getTime() : '');
                      }}
                      disabled={isLoading}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Case Number Field */}
          <FormField
            control={form.control}
            name="caseNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Dosya Numarası</FormLabel>
                <Select
                  disabled={isLoading || casesLoading}
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Dosya seçin (opsiyonel)" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="none">Dosya seçilmedi</SelectItem>
                    {Object.entries(caseParties).map(([caseNumber, parties]) => (
                      <SelectItem key={caseNumber} value={caseNumber}>
                        {caseNumber} - {parties.map(party => `${party.adi} (${party.rol})`).join(', ')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Task Type Field */}
          <FormField
            control={form.control}
            name="taskType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Görev Türü</FormLabel>
                <Select
                  disabled={isLoading}
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Görev türü seçin" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="TASK">Görev</SelectItem>
                    <SelectItem value="TRIAL">Duruşma</SelectItem>
                    <SelectItem value="MEETING">Toplantı</SelectItem>
                    <SelectItem value="REMINDER">Hatırlatma</SelectItem>
                    <SelectItem value="OTHER">Diğer</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end gap-2 pt-4">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              İptal
            </Button>
          )}
          <Button type="submit" disabled={isLoading}>
            {(() => {
              if (isLoading) return 'Kaydediliyor...';
              if (isEditing) return 'Güncelle';
              return 'Oluştur';
            })()}
          </Button>
        </div>
      </form>
    </Form>
  );
}
