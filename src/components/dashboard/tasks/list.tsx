'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Edit, Trash2, Clock, Calendar, MessageSquare, Plus, ChevronDown } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';

import { Task, TaskNote } from '@/lib/schemas/tasks';
import { tasksService } from '@/lib/api';
import { TaskForm } from './form';
import { TaskNoteForm } from './note-form';

interface TaskListProps {
  readonly tasks: Task[];
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly onRefresh: () => void;
}

export function TaskList({ tasks, isLoading, error, onRefresh }: TaskListProps) {
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [noteDialogOpen, setNoteDialogOpen] = useState(false);
  const [selectedNote, setSelectedNote] = useState<TaskNote | null>(null);
  const [deleteNoteDialogOpen, setDeleteNoteDialogOpen] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  // Format date from epoch time
  const formatDate = (epochTime: number, isEpochMilliseconds: boolean = true) => {
    try {
      // Convert seconds to milliseconds by multiplying by 1000 if necessary
      if (isEpochMilliseconds) {
        epochTime *= 1000;
      }
      const date = new Date(epochTime);
      return format(date, 'dd MMM yyyy HH:mm', { locale: tr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(epochTime);
    }
  };

  // Get priority badge
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return <Badge variant="outline">Düşük</Badge>;
      case 'MEDIUM':
        return <Badge variant="secondary">Orta</Badge>;
      case 'HIGH':
        return <Badge variant="default">Yüksek</Badge>;
      case 'CRITICAL':
        return <Badge variant="destructive">Kritik</Badge>;
      default:
        return null;
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'OPEN':
        return <Badge variant="outline">Açık</Badge>;
      case 'IN_PROGRESS':
        return <Badge variant="secondary">Devam Ediyor</Badge>;
      case 'COMPLETED':
        return <Badge variant="success">Tamamlandı</Badge>;
      case 'CANCELLED':
        return <Badge variant="destructive">İptal Edildi</Badge>;
      default:
        return null;
    }
  };

  // Get task type badge
  const getTaskTypeBadge = (taskType?: string) => {
    switch (taskType) {
      case 'TASK':
        return <Badge variant="outline">Görev</Badge>;
      case 'TRIAL':
        return <Badge variant="secondary">Duruşma</Badge>;
      case 'MEETING':
        return <Badge variant="default">Toplantı</Badge>;
      case 'REMINDER':
        return <Badge variant="secondary">Hatırlatma</Badge>;
      case 'OTHER':
        return <Badge variant="outline">Diğer</Badge>;
      default:
        return <Badge variant="outline">Görev</Badge>;
    }
  };

  // Handle edit
  const handleEdit = (task: Task) => {
    setSelectedTask(task);
    setEditDialogOpen(true);
  };

  // Handle delete
  const handleDelete = (task: Task) => {
    setSelectedTask(task);
    setDeleteDialogOpen(true);
  };

  // Handle add note
  const handleAddNote = (task: Task) => {
    setSelectedTask(task);
    setSelectedNote(null);
    setNoteDialogOpen(true);
  };

  // Handle edit note
  const handleEditNote = (task: Task, note: TaskNote) => {
    setSelectedTask(task);
    setSelectedNote(note);
    setNoteDialogOpen(true);
  };

  // Handle delete note
  const handleDeleteNote = (task: Task, note: TaskNote) => {
    setSelectedTask(task);
    setSelectedNote(note);
    setDeleteNoteDialogOpen(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedTask) return;

    try {
      setIsDeleting(true);
      setDeleteError(null);
      await tasksService.deleteTask(selectedTask.id);
      setDeleteDialogOpen(false);
      onRefresh();
    } catch (error) {
      console.error('Delete task error:', error);
      setDeleteError(error instanceof Error ? error.message : 'Görev silinirken bir hata oluştu.');
    } finally {
      setIsDeleting(false);
    }
  };

  // Confirm delete note
  const confirmDeleteNote = async () => {
    if (!selectedNote) return;

    try {
      setIsDeleting(true);
      setDeleteError(null);
      await tasksService.deleteTaskNote(selectedNote.id);
      setDeleteNoteDialogOpen(false);
      onRefresh();
    } catch (error) {
      console.error('Delete note error:', error);
      setDeleteError(error instanceof Error ? error.message : 'Not silinirken bir hata oluştu.');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle form success
  const handleFormSuccess = () => {
    setEditDialogOpen(false);
    onRefresh();
  };

  // Handle note form success
  const handleNoteFormSuccess = () => {
    setNoteDialogOpen(false);
    onRefresh();
  };

  // Handle status change
  const handleStatusChange = async (task: Task, newStatus: string) => {
    try {
      setIsUpdatingStatus(true);

      // Create updated task data with new status
      const updatedTaskData = {
        title: task.title,
        description: task.description,
        priority: task.priority,
        startDate: task.startDate,
        dueDate: task.dueDate,
        caseNumber: task.caseNumber ?? undefined,
        status: newStatus as any,
        taskType: task.taskType
      };

      // Update the task
      await tasksService.updateTask(task.id, updatedTaskData);

      // Show success notification
      toast.success('Görev durumu güncellendi', {
        description: `"${task.title}" görevi "${getStatusText(newStatus)}" durumuna güncellendi.`
      });

      // Refresh the task list
      onRefresh();
    } catch (error) {
      console.error('Update task status error:', error);

      // Show error notification
      toast.error('Durum güncellenirken hata oluştu', {
        description: error instanceof Error ? error.message : 'Lütfen tekrar deneyin.'
      });
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Get status text
  const getStatusText = (status: string): string => {
    switch (status) {
      case 'OPEN': return 'Açık';
      case 'IN_PROGRESS': return 'Devam Ediyor';
      case 'COMPLETED': return 'Tamamlandı';
      case 'CANCELLED': return 'İptal Edildi';
      default: return status;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="p-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <div className="w-1/2 h-6 bg-muted rounded animate-pulse" />
                <div className="w-20 h-6 bg-muted rounded animate-pulse" />
              </div>
              <div className="w-full h-4 bg-muted rounded animate-pulse" />
              <div className="w-3/4 h-4 bg-muted rounded animate-pulse" />
              <div className="flex justify-between">
                <div className="w-32 h-4 bg-muted rounded animate-pulse" />
                <div className="w-24 h-8 bg-muted rounded animate-pulse" />
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-destructive/15 text-destructive p-4 rounded-md">
        <p>{error}</p>
        <Button onClick={onRefresh} variant="outline" className="mt-2">
          Yeniden Dene
        </Button>
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <div className="text-center p-8 border rounded-md">
        <p className="text-muted-foreground mb-4">Henüz görev bulunmuyor.</p>
      </div>
    );
  }

  return (
    <>
      <ScrollArea className="h-[800px] pr-4">
        <div className="space-y-4">
          {tasks.map((task) => (
            <Card key={task.id} className="overflow-hidden">
              <div className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold text-lg">{task.title}</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      {task.description}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    {getPriorityBadge(task.priority)}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-7 gap-1 px-2 font-normal"
                          disabled={isUpdatingStatus}
                        >
                          {getStatusBadge(task.status)}
                          <ChevronDown className="h-3 w-3 opacity-50" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleStatusChange(task, 'OPEN')}
                          className={task.status === 'OPEN' ? 'bg-accent' : ''}
                        >
                          <Badge variant="outline" className="mr-2">Açık</Badge>
                          <span>Açık</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleStatusChange(task, 'IN_PROGRESS')}
                          className={task.status === 'IN_PROGRESS' ? 'bg-accent' : ''}
                        >
                          <Badge variant="secondary" className="mr-2">Devam Ediyor</Badge>
                          <span>Devam Ediyor</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleStatusChange(task, 'COMPLETED')}
                          className={task.status === 'COMPLETED' ? 'bg-accent' : ''}
                        >
                          <Badge variant="default" className="mr-2">Tamamlandı</Badge>
                          <span>Tamamlandı</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleStatusChange(task, 'CANCELLED')}
                          className={task.status === 'CANCELLED' ? 'bg-accent' : ''}
                        >
                          <Badge variant="destructive" className="mr-2">İptal Edildi</Badge>
                          <span>İptal Edildi</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                    {getTaskTypeBadge(task.taskType)}
                  </div>
                </div>

                <div className="flex flex-wrap gap-4 mt-4">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4 mr-1" />
                    <span>Başlangıç: {formatDate(task.startDate, false)}</span>
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Clock className="h-4 w-4 mr-1" />
                    <span>Bitiş: {formatDate(task.dueDate, false)}</span>
                  </div>
                  {task.caseNumber && (
                    <div className="flex items-center text-sm text-muted-foreground">
                      <span>Dosya: {task.caseNumber}</span>
                    </div>
                  )}
                </div>

                {task.notes && task.notes.length > 0 && (
                  <Accordion type="single" collapsible className="mt-4">
                    <AccordionItem value="notes">
                      <AccordionTrigger className="text-sm">
                        Notlar ({task.notes.length})
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3 mt-2">
                          {task.notes.map((note) => (
                            <div key={note.id} className="bg-muted/40 p-3 rounded-md">
                              <div className="flex justify-between items-start">
                                <p className="text-sm whitespace-pre-wrap">{note.content}</p>
                                <div className="flex space-x-1 ml-2">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-6 w-6"
                                    onClick={() => handleEditNote(task, note)}
                                  >
                                    <Edit className="h-3 w-3" />
                                    <span className="sr-only">Düzenle</span>
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-6 w-6"
                                    onClick={() => handleDeleteNote(task, note)}
                                  >
                                    <Trash2 className="h-3 w-3" />
                                    <span className="sr-only">Sil</span>
                                  </Button>
                                </div>
                              </div>
                              <p className="text-xs text-muted-foreground mt-1">
                                {formatDate(note.createdAt)}
                              </p>
                            </div>
                          ))}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                )}

                <div className="flex justify-between items-center mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-1"
                    onClick={() => handleAddNote(task)}
                  >
                    <Plus className="h-3 w-3" />
                    <MessageSquare className="h-3 w-3" />
                    <span>Not Ekle</span>
                  </Button>

                  <div className="flex space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(task)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      <span>Düzenle</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(task)}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      <span>Sil</span>
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </ScrollArea>

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Görev Düzenle</DialogTitle>
            <DialogDescription>
              Görev bilgilerini güncelleyin
            </DialogDescription>
          </DialogHeader>
          {selectedTask && (
            <TaskForm
              task={selectedTask}
              onSuccess={handleFormSuccess}
              onCancel={() => setEditDialogOpen(false)}
              inDialog={true}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Görevi Sil</DialogTitle>
            <DialogDescription>
              Bu görevi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
            </DialogDescription>
          </DialogHeader>
          {deleteError && (
            <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
              {deleteError}
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              İptal
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? 'Siliniyor...' : 'Sil'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Note Dialog */}
      <Dialog open={noteDialogOpen} onOpenChange={setNoteDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{selectedNote ? 'Notu Düzenle' : 'Not Ekle'}</DialogTitle>
            <DialogDescription>
              {selectedTask?.title} görevine {selectedNote ? 'not düzenle' : 'yeni not ekle'}
            </DialogDescription>
          </DialogHeader>
          {selectedTask && (
            <TaskNoteForm
              taskId={selectedTask.id}
              note={selectedNote || undefined}
              onSuccess={handleNoteFormSuccess}
              onCancel={() => setNoteDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Note Dialog */}
      <Dialog open={deleteNoteDialogOpen} onOpenChange={setDeleteNoteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Notu Sil</DialogTitle>
            <DialogDescription>
              Bu notu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
            </DialogDescription>
          </DialogHeader>
          {deleteError && (
            <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
              {deleteError}
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteNoteDialogOpen(false)}
              disabled={isDeleting}
            >
              İptal
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteNote}
              disabled={isDeleting}
            >
              {isDeleting ? 'Siliniyor...' : 'Sil'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
