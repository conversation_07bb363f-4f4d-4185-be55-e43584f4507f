'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';

import { taskNoteSchema, type TaskNoteFormValues, type TaskNote } from '@/lib/schemas/tasks';
import { tasksService } from '@/lib/api';

interface TaskNoteFormProps {
  readonly taskId: number;
  readonly note?: TaskNote;
  readonly onSuccess: (note: TaskNote) => void;
  readonly onCancel?: () => void;
}

export function TaskNoteForm({ taskId, note, onSuccess, onCancel }: TaskNoteFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isEditing = !!note;

  // Initialize form with react-hook-form and zod validation
  const form = useForm<TaskNoteFormValues>({
    resolver: zodResolver(taskNoteSchema),
    defaultValues: {
      content: note?.content ?? '',
    },
  });

  // Handle form submission
  const onSubmit = async (data: TaskNoteFormValues) => {
    try {
      setIsLoading(true);
      setError(null);

      let result: TaskNote;

      if (isEditing && note) {
        // Update existing note
        result = await tasksService.updateTaskNote(note.id, data);
      } else {
        // Create new note
        result = await tasksService.createTaskNote(taskId, data);
      }

      onSuccess(result);
    } catch (err) {
      console.error('Task note form error:', err);
      setError(err instanceof Error ? err.message : 'Bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {error && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {error}
          </div>
        )}

        {/* Content Field */}
        <FormField
          control={form.control}
          name="content"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea
                  placeholder="Not içeriği..."
                  className="min-h-[100px]"
                  {...field}
                  disabled={isLoading}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              İptal
            </Button>
          )}
          <Button type="submit" disabled={isLoading}>
            {(() => {
              if (isLoading) return 'Kaydediliyor...';
              if (isEditing) return 'Güncelle';
              return 'Ekle';
            })()}
          </Button>
        </div>
      </form>
    </Form>
  );
}
