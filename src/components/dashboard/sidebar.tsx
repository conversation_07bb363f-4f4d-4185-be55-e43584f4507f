'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  Users,
  FileText,
  Settings,
  Calendar,
  Calculator,
  Gavel,
  Bell,
  X,
  FolderOpen,
  Clock,
  CheckSquare,
  FileSignature,
  RefreshCw,
  Shield
} from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface SidebarProps {
  readonly className?: string;
  readonly isOpen: boolean;
  readonly onClose: () => void;
}

interface SidebarItem {
  title: string;
  href: string;
  icon: React.ReactNode;
}

export function Sidebar({ className, isOpen, onClose }: SidebarProps) {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  const sidebarItems: SidebarItem[] = [
    /*{
      title: 'Dashboard',
      href: '/dashboard',
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      title: 'Users',
      href: '/dashboard/users',
      icon: <Users className="h-5 w-5" />,
    },
    {
      title: 'Reports',
      href: '/dashboard/reports',
      icon: <FileText className="h-5 w-5" />,
    },
    {
      title: 'Calendar',
      href: '/dashboard/calendar',
      icon: <Calendar className="h-5 w-5" />,
    },
    {
      title: 'Kıdem Tazminatı',
      href: '/dashboard/severance-pay',
      icon: <Calculator className="h-5 w-5" />,
    },
    {
      title: 'Duruşmalar',
      href: '/dashboard/trials',
      icon: <Gavel className="h-5 w-5" />,
    },
    {
      title: 'Dosyalar',
      href: '/dashboard/cases',
      icon: <FolderOpen className="h-5 w-5" />,
    },
    {
      title: 'Vekaletler',
      href: '/dashboard/power-of-attorneys',
      icon: <FileSignature className="h-5 w-5" />,
    },
    {
      title: 'Hatırlatmalar',
      href: '/dashboard/reminders',
      icon: <Clock className="h-5 w-5" />,
    },
    {
      title: 'Görevler',
      href: '/dashboard/tasks',
      icon: <CheckSquare className="h-5 w-5" />,
    },
    {
      title: 'Senkronizasyon',
      href: '/dashboard/synchronization',
      icon: <RefreshCw className="h-5 w-5" />,
    },*/
    /*{
      title: 'Bildirimler',
      href: '/dashboard/notifications',
      icon: <Bell className="h-5 w-5" />,
    },*/
    {
      title: 'Admin',
      href: '/dashboard/admin',
      icon: <Shield className="h-5 w-5" />,
    },
    /*{
      title: 'Settings',
      href: '/dashboard/settings',
      icon: <Settings className="h-5 w-5" />,
    },*/
  ];

  if (!mounted) return null;

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <>
          <div className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm md:hidden" />
          <button
            className="fixed inset-0 z-40 w-full h-full cursor-default md:hidden"
            onClick={onClose}
            aria-label="Close sidebar"
          />
        </>
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed top-0 bottom-0 left-0 z-50 w-64 border-r bg-background transition-transform duration-300 md:sticky md:z-0 md:translate-x-0",
          isOpen ? "translate-x-0" : "-translate-x-full",
          className
        )}
      >
        <div className="flex h-14 items-center border-b px-4">
          <h2 className="text-lg font-semibold">AVAS</h2>
          <Button
            variant="ghost"
            size="icon"
            className="ml-auto md:hidden"
            onClick={onClose}
          >
            <X className="h-5 w-5" />
            <span className="sr-only">Close sidebar</span>
          </Button>
        </div>

        <nav className="flex flex-col gap-1 p-2">
          {sidebarItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                pathname === item.href
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-accent hover:text-accent-foreground"
              )}
            >
              {item.icon}
              <span className="flex items-center gap-2">
                {item.title}
              </span>
            </Link>
          ))}
        </nav>
      </aside>
    </>
  );
}
