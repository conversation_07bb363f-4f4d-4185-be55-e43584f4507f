'use client';

import { useState, useMemo } from 'react';
import {
  MoreH<PERSON>zon<PERSON>,
  Edit,
  Trash2,
  AlertCircle,
  RefreshCw,
  Search,
  Filter,
  X,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { transactionTypesService } from '@/lib/api';
import { 
  TransactionType, 
  TransactionTypeCategory,
  getTransactionTypeCategoryDisplayName 
} from '@/lib/schemas/transaction-types';
import { TransactionTypeForm } from './form';

interface TransactionTypeListProps {
  readonly transactionTypes: TransactionType[];
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly onRefresh: () => void;
}

export function TransactionTypeList({ 
  transactionTypes, 
  isLoading, 
  error, 
  onRefresh 
}: TransactionTypeListProps) {
  const [selectedTransactionTypes, setSelectedTransactionTypes] = useState<Set<number>>(new Set());
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedTransactionType, setSelectedTransactionType] = useState<TransactionType | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);
  const [deletingIds, setDeletingIds] = useState<Set<number>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<TransactionTypeCategory | 'ALL'>('ALL');

  // Filter and search transaction types
  const filteredTransactionTypes = useMemo(() => {
    return transactionTypes.filter(transactionType => {
      const matchesSearch = !searchQuery.trim() || 
        transactionType.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        getTransactionTypeCategoryDisplayName(transactionType.category).toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesCategory = categoryFilter === 'ALL' || transactionType.category === categoryFilter;
      
      return matchesSearch && matchesCategory;
    });
  }, [transactionTypes, searchQuery, categoryFilter]);

  // Selection handlers
  const handleSelectTransactionType = (id: number, checked: boolean) => {
    const newSelected = new Set(selectedTransactionTypes);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedTransactionTypes(newSelected);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTransactionTypes(new Set(filteredTransactionTypes.map(t => t.id)));
    } else {
      setSelectedTransactionTypes(new Set());
    }
  };

  const isAllSelected = filteredTransactionTypes.length > 0 && 
    filteredTransactionTypes.every(t => selectedTransactionTypes.has(t.id));
  const isIndeterminate = selectedTransactionTypes.size > 0 && !isAllSelected;

  // Edit handlers
  const handleEdit = (transactionType: TransactionType) => {
    setSelectedTransactionType(transactionType);
    setEditDialogOpen(true);
  };

  const handleFormSuccess = (transactionType: TransactionType) => {
    setEditDialogOpen(false);
    setSelectedTransactionType(null);
    onRefresh();
    toast.success('İşlem türü güncellendi', {
      description: `"${transactionType.name}" işlem türü başarıyla güncellendi.`
    });
  };

  // Delete handlers
  const handleDelete = (transactionType: TransactionType) => {
    setSelectedTransactionType(transactionType);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!selectedTransactionType) return;

    try {
      setDeletingIds(prev => new Set(prev).add(selectedTransactionType.id));
      await transactionTypesService.deleteTransactionType(selectedTransactionType.id);
      
      toast.success('İşlem türü silindi', {
        description: `"${selectedTransactionType.name}" işlem türü başarıyla silindi.`
      });
      
      onRefresh();
    } catch (error) {
      console.error('Delete transaction type error:', error);
      toast.error('Silme işlemi başarısız', {
        description: error instanceof Error ? error.message : 'İşlem türü silinirken hata oluştu.'
      });
    } finally {
      setDeletingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(selectedTransactionType.id);
        return newSet;
      });
      setDeleteDialogOpen(false);
      setSelectedTransactionType(null);
    }
  };

  // Bulk delete handlers
  const handleBulkDelete = () => {
    if (selectedTransactionTypes.size === 0) return;
    setBulkDeleteDialogOpen(true);
  };

  const handleConfirmBulkDelete = async () => {
    if (selectedTransactionTypes.size === 0) return;

    try {
      const idsToDelete = Array.from(selectedTransactionTypes);
      setDeletingIds(new Set(idsToDelete));
      
      const results = await transactionTypesService.bulkDeleteTransactionTypes(idsToDelete);
      
      if (results.successful.length > 0) {
        toast.success(`${results.successful.length} işlem türü silindi`, {
          description: 'Seçilen işlem türleri başarıyla silindi.'
        });
      }
      
      if (results.failed.length > 0) {
        toast.error(`${results.failed.length} işlem türü silinemedi`, {
          description: 'Bazı işlem türleri silinirken hata oluştu.'
        });
      }
      
      setSelectedTransactionTypes(new Set());
      onRefresh();
    } catch (error) {
      console.error('Bulk delete error:', error);
      toast.error('Toplu silme işlemi başarısız', {
        description: 'İşlem türleri silinirken hata oluştu.'
      });
    } finally {
      setDeletingIds(new Set());
      setBulkDeleteDialogOpen(false);
    }
  };

  // Clear filters
  const clearFilters = () => {
    setSearchQuery('');
    setCategoryFilter('ALL');
  };

  const hasActiveFilters = searchQuery.trim() !== '' || categoryFilter !== 'ALL';

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4">
        <AlertCircle className="h-12 w-12 text-muted-foreground" />
        <div className="text-center space-y-2">
          <h3 className="text-lg font-medium">Veri yüklenirken hata oluştu</h3>
          <p className="text-sm text-muted-foreground max-w-md">
            {error}
          </p>
          <Button onClick={onRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Tekrar Dene
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters and Actions */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="İşlem türü ara..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
            disabled={isLoading}
          />
        </div>
        
        <Select value={categoryFilter} onValueChange={(value) => setCategoryFilter(value as TransactionTypeCategory | 'ALL')}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Kategori filtresi" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">Tüm Kategoriler</SelectItem>
            <SelectItem value="INCOME">Gelir</SelectItem>
            <SelectItem value="EXPENSE">Gider</SelectItem>
          </SelectContent>
        </Select>

        {hasActiveFilters && (
          <Button variant="outline" onClick={clearFilters} size="sm">
            <X className="h-4 w-4 mr-2" />
            Filtreleri Temizle
          </Button>
        )}
      </div>

      {/* Bulk Actions */}
      {selectedTransactionTypes.size > 0 && (
        <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
          <span className="text-sm text-muted-foreground">
            {selectedTransactionTypes.size} öğe seçildi
          </span>
          <Button
            variant="destructive"
            size="sm"
            onClick={handleBulkDelete}
            disabled={isLoading}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Seçilenleri Sil
          </Button>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-4 flex-1" />
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && filteredTransactionTypes.length === 0 && (
        <div className="text-center py-12">
          <Filter className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">
            {hasActiveFilters ? 'Filtreye uygun işlem türü bulunamadı' : 'Henüz işlem türü yok'}
          </h3>
          <p className="text-sm text-muted-foreground mb-4">
            {hasActiveFilters
              ? 'Farklı filtreler deneyebilir veya filtreleri temizleyebilirsiniz.'
              : 'Sisteme ilk işlem türünüzü ekleyerek başlayın.'
            }
          </p>
          {hasActiveFilters && (
            <Button variant="outline" onClick={clearFilters}>
              Filtreleri Temizle
            </Button>
          )}
        </div>
      )}

      {/* Desktop Table View */}
      {!isLoading && filteredTransactionTypes.length > 0 && (
        <div className="hidden md:block rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">
                  <Checkbox
                    checked={isAllSelected}
                    onCheckedChange={handleSelectAll}
                    aria-label="Tümünü seç"
                    className={isIndeterminate ? "data-[state=checked]:bg-primary" : ""}
                  />
                </TableHead>
                <TableHead>İşlem Türü Adı</TableHead>
                <TableHead>Kategori</TableHead>
                <TableHead className="w-[70px]">İşlemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTransactionTypes.map((transactionType) => (
                <TableRow key={transactionType.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedTransactionTypes.has(transactionType.id)}
                      onCheckedChange={(checked) => handleSelectTransactionType(transactionType.id, checked as boolean)}
                      aria-label={`İşlem türü ${transactionType.name} seç`}
                    />
                  </TableCell>
                  <TableCell className="font-medium">
                    <div className="max-w-[200px] truncate" style={{ wordWrap: 'break-word' }}>
                      {transactionType.name}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={transactionType.category === 'INCOME' ? 'default' : 'secondary'}>
                      {getTransactionTypeCategoryDisplayName(transactionType.category)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          disabled={deletingIds.has(transactionType.id)}
                        >
                          {deletingIds.has(transactionType.id) ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                          ) : (
                            <MoreHorizontal className="h-4 w-4" />
                          )}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEdit(transactionType)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Düzenle
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDelete(transactionType)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Sil
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Mobile Card View */}
      {!isLoading && filteredTransactionTypes.length > 0 && (
        <div className="md:hidden space-y-4">
          {filteredTransactionTypes.map((transactionType) => (
            <div key={transactionType.id} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1 min-w-0">
                  <Checkbox
                    checked={selectedTransactionTypes.has(transactionType.id)}
                    onCheckedChange={(checked) => handleSelectTransactionType(transactionType.id, checked as boolean)}
                    aria-label={`İşlem türü ${transactionType.name} seç`}
                    className="mt-1"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm mb-1" style={{ wordWrap: 'break-word' }}>
                      {transactionType.name}
                    </div>
                    <Badge variant={transactionType.category === 'INCOME' ? 'default' : 'secondary'} className="text-xs">
                      {getTransactionTypeCategoryDisplayName(transactionType.category)}
                    </Badge>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      disabled={deletingIds.has(transactionType.id)}
                    >
                      {deletingIds.has(transactionType.id) ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <MoreHorizontal className="h-4 w-4" />
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleEdit(transactionType)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Düzenle
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleDelete(transactionType)}
                      className="text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Sil
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto mx-4">
          <DialogHeader>
            <DialogTitle>İşlem Türünü Düzenle</DialogTitle>
            <DialogDescription>
              Mevcut işlem türünün bilgilerini güncelleyin
            </DialogDescription>
          </DialogHeader>
          <div className="max-h-[60vh] overflow-y-auto">
            {selectedTransactionType && (
              <TransactionTypeForm
                transactionType={selectedTransactionType}
                onSuccess={handleFormSuccess}
                onCancel={() => setEditDialogOpen(false)}
                inDialog={true}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>İşlem türünü sil</AlertDialogTitle>
            <AlertDialogDescription>
              "{selectedTransactionType?.name}" işlem türünü silmek istediğinizden emin misiniz?
              Bu işlem geri alınamaz.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Confirmation Dialog */}
      <AlertDialog open={bulkDeleteDialogOpen} onOpenChange={setBulkDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Seçilen işlem türlerini sil</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedTransactionTypes.size} işlem türünü silmek istediğinizden emin misiniz?
              Bu işlem geri alınamaz.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmBulkDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
