'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { transactionTypesService } from '@/lib/api';
import {
  TransactionType,
  TransactionTypeFormValues,
  transactionTypeFormSchema,
  formValuesToCreateRequest,
  formValuesToUpdateRequest,
  transactionTypeToFormValues,
  TransactionTypeCategory,
  getTransactionTypeCategoryDisplayName,
} from '@/lib/schemas/transaction-types';

interface TransactionTypeFormProps {
  readonly transactionType?: TransactionType;
  readonly onSuccess: (transactionType: TransactionType) => void;
  readonly onCancel: () => void;
  readonly inDialog?: boolean;
}

export function TransactionTypeForm({ 
  transactionType, 
  onSuccess, 
  onCancel, 
  inDialog = false 
}: TransactionTypeFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isEditing = !!transactionType;

  // Initialize form with react-hook-form and zod validation
  const form = useForm<TransactionTypeFormValues>({
    resolver: zodResolver(transactionTypeFormSchema),
    defaultValues: transactionType ? transactionTypeToFormValues(transactionType) : {
      name: '',
      category: 'EXPENSE',
    },
  });

  // Handle form submission
  const onSubmit = async (data: TransactionTypeFormValues) => {
    try {
      setIsLoading(true);
      setError(null);

      let result: TransactionType;

      if (isEditing && transactionType) {
        // Update existing transaction type
        const updateData = formValuesToUpdateRequest(data);
        result = await transactionTypesService.updateTransactionType(transactionType.id, updateData);
      } else {
        // Create new transaction type
        const createData = formValuesToCreateRequest(data);
        result = await transactionTypesService.createTransactionType(createData);
      }

      onSuccess(result);
    } catch (err) {
      console.error('Transaction type form error:', err);
      setError(err instanceof Error ? err.message : 'Bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };

  // Category options
  const categoryOptions: { value: TransactionTypeCategory; label: string }[] = [
    { value: 'INCOME', label: getTransactionTypeCategoryDisplayName('INCOME') },
    { value: 'EXPENSE', label: getTransactionTypeCategoryDisplayName('EXPENSE') },
  ];

  return (
    <div className={`space-y-6 ${inDialog ? 'p-1' : 'p-6 bg-card rounded-lg border'}`}>
      {!inDialog && (
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            {isEditing ? 'İşlem Türünü Düzenle' : 'Yeni İşlem Türü Oluştur'}
          </h2>
          <p className="text-muted-foreground">
            {isEditing 
              ? 'Mevcut işlem türünün bilgilerini güncelleyin' 
              : 'Sisteme yeni bir işlem türü ekleyin'
            }
          </p>
        </div>
      )}

      {error && (
        <div className="rounded-md bg-destructive/15 p-3">
          <div className="flex">
            <div className="text-sm text-destructive">
              {error}
            </div>
          </div>
        </div>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>İşlem Türü Adı</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Örn: Elektrik Faturası"
                    {...field}
                    disabled={isLoading}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Kategori</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  disabled={isLoading}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Kategori seçin" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {categoryOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              İptal
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? 'Güncelle' : 'Oluştur'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
