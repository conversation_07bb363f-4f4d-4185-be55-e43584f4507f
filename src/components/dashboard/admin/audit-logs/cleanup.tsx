'use client';

import { useState } from 'react';
import { Trash2, AlertTriangle, Calendar, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DatePicker } from '@/components/ui/date-picker';
import { Label } from '@/components/ui/label';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { adminAuditLogsService } from '@/lib/api';

export function AuditLogsCleanup() {
  const [cutoffDate, setCutoffDate] = useState<Date>();
  const [isLoading, setIsLoading] = useState(false);

  const handleCleanup = async () => {
    if (!cutoffDate) {
      toast.error('Lütfen bir kesim tarihi seçin');
      return;
    }

    // Ensure cutoff date is not in the future
    const today = new Date();
    today.setHours(23, 59, 59, 999);
    
    if (cutoffDate > today) {
      toast.error('Kesim tarihi bugünden sonra olamaz');
      return;
    }

    // Ensure cutoff date is not too recent (at least 30 days old)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);
    
    if (cutoffDate > thirtyDaysAgo) {
      toast.error('Güvenlik nedeniyle, kesim tarihi en az 30 gün öncesi olmalıdır');
      return;
    }

    try {
      setIsLoading(true);
      const cutoffDateStr = cutoffDate.toISOString().split('T')[0];
      await adminAuditLogsService.cleanupOldAuditLogs(cutoffDateStr);
      
      toast.success('Eski denetim kayıtları başarıyla temizlendi');
      setCutoffDate(undefined);
    } catch (error) {
      console.error('Cleanup error:', error);
      toast.error(error instanceof Error ? error.message : 'Temizleme sırasında hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickCleanup = (days: number) => {
    const date = new Date();
    date.setDate(date.getDate() - days);
    setCutoffDate(date);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getDaysAgo = (date: Date) => {
    const today = new Date();
    const diffTime = today.getTime() - date.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <Card className="border-destructive/20">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-destructive">
          <Trash2 className="h-5 w-5" />
          Denetim Kayıtları Temizleme
        </CardTitle>
        <CardDescription>
          Eski denetim kayıtlarını kalıcı olarak silin. Bu işlem geri alınamaz.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Warning */}
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-destructive mt-0.5" />
            <div className="space-y-2">
              <h4 className="font-medium text-destructive">Dikkat!</h4>
              <ul className="text-sm text-destructive/80 space-y-1">
                <li>• Bu işlem geri alınamaz</li>
                <li>• Seçilen tarihten önce tüm denetim kayıtları silinecek</li>
                <li>• Güvenlik nedeniyle en az 30 gün önceki kayıtlar silinebilir</li>
                <li>• Bu işlem sadece sistem yöneticileri tarafından yapılmalıdır</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Date Selection */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Kesim Tarihi</Label>
            <DatePicker
              date={cutoffDate}
              setDate={setCutoffDate}
              placeholder="Kesim tarihi seçin"
            />
            <p className="text-sm text-muted-foreground">
              Bu tarihten önce tüm denetim kayıtları silinecek
            </p>
          </div>

          {/* Quick Selection Buttons */}
          <div className="space-y-2">
            <Label>Hızlı Seçim</Label>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickCleanup(90)}
                className="gap-2"
              >
                <Calendar className="h-4 w-4" />
                90 Gün Öncesi
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickCleanup(180)}
                className="gap-2"
              >
                <Calendar className="h-4 w-4" />
                6 Ay Öncesi
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickCleanup(365)}
                className="gap-2"
              >
                <Calendar className="h-4 w-4" />
                1 Yıl Öncesi
              </Button>
            </div>
          </div>
        </div>

        {/* Selected Date Info */}
        {cutoffDate && (
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Seçilen Tarih Bilgileri</h4>
            <div className="space-y-1 text-sm">
              <p>
                <span className="text-muted-foreground">Kesim Tarihi:</span>{' '}
                <span className="font-medium">{formatDate(cutoffDate)}</span>
              </p>
              <p>
                <span className="text-muted-foreground">Kaç Gün Önce:</span>{' '}
                <span className="font-medium">{getDaysAgo(cutoffDate)} gün</span>
              </p>
              <p className="text-destructive font-medium">
                Bu tarihten önce tüm denetim kayıtları kalıcı olarak silinecek!
              </p>
            </div>
          </div>
        )}

        {/* Cleanup Button */}
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="destructive"
              disabled={!cutoffDate || isLoading}
              className="w-full gap-2"
            >
              <Trash2 className="h-4 w-4" />
              {isLoading ? 'Temizleniyor...' : 'Eski Kayıtları Temizle'}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2 text-destructive">
                <Shield className="h-5 w-5" />
                Denetim Kayıtları Temizleme Onayı
              </AlertDialogTitle>
              <AlertDialogDescription className="space-y-2">
                <p>
                  <strong>{cutoffDate && formatDate(cutoffDate)}</strong> tarihinden önce 
                  tüm denetim kayıtları kalıcı olarak silinecek.
                </p>
                <p className="text-destructive font-medium">
                  Bu işlem geri alınamaz! Devam etmek istediğinizden emin misiniz?
                </p>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>İptal</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleCleanup}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Evet, Temizle
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Additional Info */}
        <div className="bg-muted/30 p-4 rounded-lg">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Güvenlik Bilgileri
          </h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Bu işlem sistem loglarında kaydedilir</li>
            <li>• Temizleme işlemi yalnızca admin yetkisi ile yapılabilir</li>
            <li>• Yasal gereklilikler nedeniyle bazı kayıtlar korunabilir</li>
            <li>• Düzenli temizleme sistem performansını artırır</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
