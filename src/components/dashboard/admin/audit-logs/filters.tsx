'use client';

import { useState } from 'react';
import { Search, Filter, X, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DatePicker } from '@/components/ui/date-picker';
import { Separator } from '@/components/ui/separator';

interface FilterParams {
  userId?: number;
  userEmail?: string;
  startDate?: string;
  endDate?: string;
  endpointUrl?: string;
  clientIp?: string;
}

interface AuditLogsFiltersProps {
  readonly onSearch: (filters: FilterParams) => void;
}

export function AuditLogsFilters({ onSearch }: AuditLogsFiltersProps) {
  const [filters, setFilters] = useState<FilterParams>({});
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();

  const handleInputChange = (field: keyof FilterParams, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [field]: value || undefined
    }));
  };

  const handleDateChange = (field: 'startDate' | 'endDate', date: Date | undefined) => {
    if (field === 'startDate') {
      setStartDate(date);
      setFilters(prev => ({
        ...prev,
        startDate: date ? date.toISOString().split('T')[0] : undefined
      }));
    } else {
      setEndDate(date);
      setFilters(prev => ({
        ...prev,
        endDate: date ? date.toISOString().split('T')[0] : undefined
      }));
    }
  };

  const handleSearch = () => {
    // Validate date range
    if (filters.startDate && filters.endDate) {
      const start = new Date(filters.startDate);
      const end = new Date(filters.endDate);
      if (start > end) {
        alert('Başlangıç tarihi bitiş tarihinden sonra olamaz');
        return;
      }
    }

    // Validate user ID
    if (filters.userId && (isNaN(filters.userId) || filters.userId <= 0)) {
      alert('Geçerli bir kullanıcı ID giriniz');
      return;
    }

    onSearch(filters);
  };

  const handleClear = () => {
    setFilters({});
    setStartDate(undefined);
    setEndDate(undefined);
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== undefined && value !== '');

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Gelişmiş Filtreleme
        </CardTitle>
        <CardDescription>
          Denetim kayıtlarını filtrelemek için aşağıdaki kriterleri kullanın
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* User Filters */}
        <div className="space-y-4">
          <h4 className="font-medium text-sm">Kullanıcı Filtreleri</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="userId">Kullanıcı ID</Label>
              <Input
                id="userId"
                type="number"
                placeholder="Örn: 123"
                value={filters.userId || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '') {
                    handleInputChange('userId', '');
                  } else {
                    const parsed = parseInt(value);
                    if (!isNaN(parsed)) {
                      handleInputChange('userId', parsed);
                    }
                  }
                }}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="userEmail">Kullanıcı E-posta</Label>
              <Input
                id="userEmail"
                type="email"
                placeholder="Örn: <EMAIL>"
                value={filters.userEmail || ''}
                onChange={(e) => handleInputChange('userEmail', e.target.value)}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Date Range Filters */}
        <div className="space-y-4">
          <h4 className="font-medium text-sm">Tarih Aralığı</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Başlangıç Tarihi</Label>
              <DatePicker
                date={startDate}
                setDate={(date) => handleDateChange('startDate', date)}
                placeholder="Başlangıç tarihi seçin"
              />
            </div>
            <div className="space-y-2">
              <Label>Bitiş Tarihi</Label>
              <DatePicker
                date={endDate}
                setDate={(date) => handleDateChange('endDate', date)}
                placeholder="Bitiş tarihi seçin"
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Technical Filters */}
        <div className="space-y-4">
          <h4 className="font-medium text-sm">Teknik Filtreler</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="endpointUrl">Endpoint URL</Label>
              <Input
                id="endpointUrl"
                placeholder="Örn: /api/user/profile"
                value={filters.endpointUrl || ''}
                onChange={(e) => handleInputChange('endpointUrl', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="clientIp">İstemci IP Adresi</Label>
              <Input
                id="clientIp"
                placeholder="Örn: ***********"
                value={filters.clientIp || ''}
                onChange={(e) => handleInputChange('clientIp', e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-2 pt-4">
          <Button 
            onClick={handleSearch} 
            className="flex-1 gap-2"
            disabled={!hasActiveFilters}
          >
            <Search className="h-4 w-4" />
            Filtrele
          </Button>
          <Button 
            variant="outline" 
            onClick={handleClear}
            disabled={!hasActiveFilters}
            className="gap-2"
          >
            <X className="h-4 w-4" />
            Temizle
          </Button>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="space-y-2">
            <h5 className="font-medium text-sm">Aktif Filtreler:</h5>
            <div className="flex flex-wrap gap-2">
              {filters.userId && (
                <div className="bg-primary/10 text-primary px-2 py-1 rounded text-sm">
                  Kullanıcı ID: {filters.userId}
                </div>
              )}
              {filters.userEmail && (
                <div className="bg-primary/10 text-primary px-2 py-1 rounded text-sm">
                  E-posta: {filters.userEmail}
                </div>
              )}
              {filters.startDate && (
                <div className="bg-primary/10 text-primary px-2 py-1 rounded text-sm">
                  Başlangıç: {new Date(filters.startDate).toLocaleDateString('tr-TR')}
                </div>
              )}
              {filters.endDate && (
                <div className="bg-primary/10 text-primary px-2 py-1 rounded text-sm">
                  Bitiş: {new Date(filters.endDate).toLocaleDateString('tr-TR')}
                </div>
              )}
              {filters.endpointUrl && (
                <div className="bg-primary/10 text-primary px-2 py-1 rounded text-sm">
                  Endpoint: {filters.endpointUrl}
                </div>
              )}
              {filters.clientIp && (
                <div className="bg-primary/10 text-primary px-2 py-1 rounded text-sm">
                  IP: {filters.clientIp}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Filter Tips */}
        <div className="bg-muted/50 p-4 rounded-lg">
          <h5 className="font-medium text-sm mb-2">Filtreleme İpuçları:</h5>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Kullanıcı ID ile arama yapmak için sayısal değer girin</li>
            <li>• E-posta ile arama yapmak için tarih aralığı da seçmeniz gerekir</li>
            <li>• Endpoint URL'de kısmi eşleşme desteklenir</li>
            <li>• IP adresi tam eşleşme gerektirir</li>
            <li>• Tarih aralığı seçerken başlangıç tarihi bitiş tarihinden önce olmalıdır</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
