'use client';

import { useState } from 'react';
import { BarChart3, TrendingUp, Users, Globe, AlertTriangle, Clock, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DatePicker } from '@/components/ui/date-picker';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { AuditLogStatisticsResponse, formatProcessingTime } from '@/lib/schemas/audit-logs';

interface AuditLogsStatisticsProps {
  readonly statistics: AuditLogStatisticsResponse | null;
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly onLoadStatistics: (startDate: string, endDate: string) => void;
}

export function AuditLogsStatistics({ 
  statistics, 
  isLoading, 
  error, 
  onLoadStatistics 
}: AuditLogsStatisticsProps) {
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();

  const handleLoadStatistics = () => {
    if (!startDate || !endDate) {
      alert('Lütfen başlangıç ve bitiş tarihlerini seçin');
      return;
    }

    if (startDate > endDate) {
      alert('Başlangıç tarihi bitiş tarihinden sonra olamaz');
      return;
    }

    const start = startDate.toISOString().split('T')[0];
    const end = endDate.toISOString().split('T')[0];
    onLoadStatistics(start, end);
  };

  const handleQuickDateRange = (days: number) => {
    const end = new Date();
    const start = new Date();
    start.setDate(end.getDate() - days);
    
    setStartDate(start);
    setEndDate(end);
    
    const startStr = start.toISOString().split('T')[0];
    const endStr = end.toISOString().split('T')[0];
    onLoadStatistics(startStr, endStr);
  };

  if (error) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-semibold mb-2">Hata Oluştu</h3>
          <p className="text-muted-foreground text-center mb-4">{error}</p>
          <Button onClick={() => startDate && endDate && handleLoadStatistics()} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Tekrar Dene
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Date Range Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            İstatistik Tarih Aralığı
          </CardTitle>
          <CardDescription>
            İstatistikleri görüntülemek için tarih aralığı seçin
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Başlangıç Tarihi</Label>
              <DatePicker
                date={startDate}
                setDate={setStartDate}
                placeholder="Başlangıç tarihi seçin"
              />
            </div>
            <div className="space-y-2">
              <Label>Bitiş Tarihi</Label>
              <DatePicker
                date={endDate}
                setDate={setEndDate}
                placeholder="Bitiş tarihi seçin"
              />
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickDateRange(7)}
            >
              Son 7 Gün
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickDateRange(30)}
            >
              Son 30 Gün
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickDateRange(90)}
            >
              Son 90 Gün
            </Button>
          </div>
          
          <Button 
            onClick={handleLoadStatistics}
            disabled={!startDate || !endDate || isLoading}
            className="w-full gap-2"
          >
            <BarChart3 className="h-4 w-4" />
            İstatistikleri Yükle
          </Button>
        </CardContent>
      </Card>

      {/* Statistics Display */}
      {(statistics || isLoading) && (
        <>
          {/* Summary Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Toplam İstek</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-8 w-16" />
                ) : (
                  <div className="text-2xl font-bold">{statistics?.totalRequests.toLocaleString('tr-TR')}</div>
                )}
                <p className="text-xs text-muted-foreground">
                  Seçilen tarih aralığında
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Benzersiz Kullanıcı</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-8 w-16" />
                ) : (
                  <div className="text-2xl font-bold">{statistics?.uniqueUsers.toLocaleString('tr-TR')}</div>
                )}
                <p className="text-xs text-muted-foreground">
                  Farklı kullanıcı sayısı
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Ortalama Süre</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-8 w-16" />
                ) : (
                  <div className="text-2xl font-bold">
                    {statistics ? formatProcessingTime(statistics.avgProcessingTime) : '0ms'}
                  </div>
                )}
                <p className="text-xs text-muted-foreground">
                  İşlem süresi ortalaması
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Hata Sayısı</CardTitle>
                <AlertTriangle className="h-4 w-4 text-destructive" />
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-8 w-16" />
                ) : (
                  <div className="text-2xl font-bold text-destructive">
                    {statistics?.errorCount.toLocaleString('tr-TR')}
                  </div>
                )}
                <p className="text-xs text-muted-foreground">
                  4xx ve 5xx hata kodları
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Most Accessed Endpoints */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                En Çok Erişilen Endpoint'ler
              </CardTitle>
              <CardDescription>
                En sık kullanılan API endpoint'leri
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <Skeleton className="h-4 w-48" />
                      <Skeleton className="h-4 w-16" />
                    </div>
                  ))}
                </div>
              ) : statistics?.mostAccessedEndpoints.length ? (
                <div className="space-y-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Endpoint</TableHead>
                        <TableHead className="text-right">Erişim Sayısı</TableHead>
                        <TableHead className="text-right">Yüzde</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {statistics.mostAccessedEndpoints.map((endpoint, index) => {
                        const percentage = statistics.totalRequests > 0 
                          ? ((endpoint.accessCount / statistics.totalRequests) * 100).toFixed(1)
                          : '0';
                        
                        return (
                          <TableRow key={index}>
                            <TableCell className="font-mono text-sm">
                              {endpoint.endpointUrl}
                            </TableCell>
                            <TableCell className="text-right font-medium">
                              {endpoint.accessCount.toLocaleString('tr-TR')}
                            </TableCell>
                            <TableCell className="text-right">
                              <Badge variant="secondary">
                                {percentage}%
                              </Badge>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-4">
                  Seçilen tarih aralığında endpoint verisi bulunamadı
                </p>
              )}
            </CardContent>
          </Card>

          {/* Most Active Users */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                En Aktif Kullanıcılar
              </CardTitle>
              <CardDescription>
                En çok API isteği yapan kullanıcılar
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <Skeleton className="h-4 w-48" />
                      <Skeleton className="h-4 w-16" />
                    </div>
                  ))}
                </div>
              ) : statistics?.mostActiveUsers.length ? (
                <div className="space-y-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Kullanıcı E-posta</TableHead>
                        <TableHead className="text-right">İstek Sayısı</TableHead>
                        <TableHead className="text-right">Yüzde</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {statistics.mostActiveUsers.map((user, index) => {
                        const percentage = statistics.totalRequests > 0 
                          ? ((user.requestCount / statistics.totalRequests) * 100).toFixed(1)
                          : '0';
                        
                        return (
                          <TableRow key={index}>
                            <TableCell className="font-medium">
                              {user.userEmail}
                            </TableCell>
                            <TableCell className="text-right font-medium">
                              {user.requestCount.toLocaleString('tr-TR')}
                            </TableCell>
                            <TableCell className="text-right">
                              <Badge variant="secondary">
                                {percentage}%
                              </Badge>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-4">
                  Seçilen tarih aralığında kullanıcı verisi bulunamadı
                </p>
              )}
            </CardContent>
          </Card>
        </>
      )}

      {/* No Data State */}
      {!statistics && !isLoading && !error && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <BarChart3 className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">İstatistik Verisi Yok</h3>
            <p className="text-muted-foreground text-center mb-4">
              İstatistikleri görüntülemek için yukarıdan tarih aralığı seçin
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
