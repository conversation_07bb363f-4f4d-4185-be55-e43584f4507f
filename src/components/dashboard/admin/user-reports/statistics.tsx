'use client';

import { useMemo } from 'react';
import { Users, UserCheck, DollarSign, TrendingUp, Crown, Shield } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { AdminUserReport, formatCurrency } from '@/lib/schemas/user-reports';

interface UserReportsStatisticsProps {
  readonly userReports: AdminUserReport[];
  readonly isLoading: boolean;
}

export function UserReportsStatistics({ userReports, isLoading }: UserReportsStatisticsProps) {
  const statistics = useMemo(() => {
    if (userReports.length === 0) {
      return {
        totalUsers: 0,
        newUsers: 0,
        activeUsers: 0,
        totalRevenue: 0,
        adminUsers: 0,
        subscribedUsers: 0,
        averageRevenue: 0,
        newUserPercentage: 0,
      };
    }

    const totalUsers = userReports.length;
    const newUsers = userReports.filter(user => user.newUser).length;
    const activeUsers = userReports.filter(user => !user.newUser).length;
    const totalRevenue = userReports.reduce((sum, user) => sum + user.totalPaymentAmount, 0);
    const adminUsers = userReports.filter(user => user.roles.includes('ADMIN')).length;
    const subscribedUsers = userReports.filter(user => user.subscriptionLevel !== null).length;
    const averageRevenue = totalUsers > 0 ? totalRevenue / totalUsers : 0;
    const newUserPercentage = totalUsers > 0 ? (newUsers / totalUsers) * 100 : 0;

    return {
      totalUsers,
      newUsers,
      activeUsers,
      totalRevenue,
      adminUsers,
      subscribedUsers,
      averageRevenue,
      newUserPercentage,
    };
  }, [userReports]);

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-1" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Users */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Toplam Kullanıcı</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.totalUsers.toLocaleString('tr-TR')}</div>
          <p className="text-xs text-muted-foreground">
            Sistemdeki tüm kullanıcılar
          </p>
        </CardContent>
      </Card>

      {/* New Users */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Yeni Kullanıcılar</CardTitle>
          <UserCheck className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.newUsers.toLocaleString('tr-TR')}</div>
          <p className="text-xs text-muted-foreground">
            %{statistics.newUserPercentage.toFixed(1)} yeni kullanıcı
          </p>
        </CardContent>
      </Card>

      {/* Total Revenue */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Toplam Gelir</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(statistics.totalRevenue)}</div>
          <p className="text-xs text-muted-foreground">
            Ortalama: {formatCurrency(statistics.averageRevenue)}
          </p>
        </CardContent>
      </Card>

      {/* Subscribed Users */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Aboneli Kullanıcılar</CardTitle>
          <Crown className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.subscribedUsers.toLocaleString('tr-TR')}</div>
          <p className="text-xs text-muted-foreground">
            %{statistics.totalUsers > 0 ? ((statistics.subscribedUsers / statistics.totalUsers) * 100).toFixed(1) : 0} abonelik oranı
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
