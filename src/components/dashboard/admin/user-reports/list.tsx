'use client';

import { useState } from 'react';
import {
  RefreshCw,
  AlertCircle,
  Search,
  Download,
  FileSpreadsheet
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from 'sonner';
import { exportToExcel, generateExportFilename } from '@/lib/utils/export';
import {
  AdminUserReport,
  formatEpochTime,
  formatCurrency,
  getSubscriptionLevelDisplayName,
  getSubscriptionLevelVariant,
  formatRoles,
  getUserStatusDisplay
} from '@/lib/schemas/user-reports';

interface UserReportsListProps {
  readonly userReports: AdminUserReport[];
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly onRefresh: () => void;
}

export function UserReportsList({ userReports, isLoading, error, onRefresh }: UserReportsListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [subscriptionFilter, setSubscriptionFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isExporting, setIsExporting] = useState(false);

  // Filter users based on search and filters
  const filteredUsers = userReports.filter(user => {
    const matchesSearch = searchTerm === '' || 
      user.userId.toString().includes(searchTerm.toLowerCase()) ||
      formatRoles(user.roles).toLowerCase().includes(searchTerm.toLowerCase());

    const matchesSubscription = subscriptionFilter === 'all' || 
      (subscriptionFilter === 'none' && user.subscriptionLevel === null) ||
      user.subscriptionLevel === subscriptionFilter;

    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'new' && user.newUser) ||
      (statusFilter === 'active' && !user.newUser);

    return matchesSearch && matchesSubscription && matchesStatus;
  });

  // Handle export functionality
  const handleExport = async () => {
    if (filteredUsers.length === 0) {
      toast.error('Dışa aktarılacak veri bulunamadı');
      return;
    }

    setIsExporting(true);

    try {
      // Prepare data for export
      const exportData = filteredUsers.map((user, index) => ({
        'Sıra': index + 1,
        'Kullanıcı ID': user.userId,
        'Kayıt Tarihi': formatEpochTime(user.createdAt),
        'Durum': getUserStatusDisplay(user.newUser).text,
        'Roller': formatRoles(user.roles),
        'Abonelik Seviyesi': getSubscriptionLevelDisplayName(user.subscriptionLevel),
        'Toplam Ödeme': formatCurrency(user.totalPaymentAmount)
      }));

      // Set column widths for better formatting
      const columnWidths = [
        { wch: 8 },  // Sıra
        { wch: 15 }, // Kullanıcı ID
        { wch: 25 }, // Kayıt Tarihi
        { wch: 15 }, // Durum
        { wch: 20 }, // Roller
        { wch: 20 }, // Abonelik Seviyesi
        { wch: 18 }  // Toplam Ödeme
      ];

      // Generate filename and export
      const filename = generateExportFilename('kullanici-raporlari') + '.xlsx';

      exportToExcel(
        exportData,
        filename,
        'Kullanıcı Raporları',
        columnWidths
      );

      toast.success(`${filteredUsers.length} kullanıcı raporu başarıyla dışa aktarıldı`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Dışa aktarma sırasında hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsExporting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <Skeleton className="h-10 flex-1" />
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="space-y-2">
          {Array.from({ length: 5 }).map((_, index) => (
            <Skeleton key={index} className="h-16 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4">
        <AlertCircle className="h-12 w-12 text-muted-foreground" />
        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold">Veri Yüklenemedi</h3>
          <p className="text-sm text-muted-foreground max-w-md">
            {error}
          </p>
          <Button onClick={onRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Tekrar Dene
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Kullanıcı ID veya rol ara..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={subscriptionFilter} onValueChange={setSubscriptionFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Abonelik Filtresi" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tüm Abonelikler</SelectItem>
            <SelectItem value="none">Abonelik Yok</SelectItem>
            <SelectItem value="BASIC">Temel</SelectItem>
            <SelectItem value="PREMIUM">Premium</SelectItem>
            <SelectItem value="ENTERPRISE">Kurumsal</SelectItem>
          </SelectContent>
        </Select>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Durum Filtresi" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tüm Durumlar</SelectItem>
            <SelectItem value="new">Yeni Kullanıcılar</SelectItem>
            <SelectItem value="active">Aktif Kullanıcılar</SelectItem>
          </SelectContent>
        </Select>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={isExporting || filteredUsers.length === 0}
            >
              {isExporting ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Download className="h-4 w-4 mr-2" />
              )}
              {isExporting ? 'Dışa Aktarılıyor...' : 'Dışa Aktar'}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleExport} disabled={isExporting}>
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              Excel (.xlsx)
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Results count */}
      <div className="text-sm text-muted-foreground">
        {filteredUsers.length} kullanıcı gösteriliyor (toplam {userReports.length})
      </div>

      {/* Table */}
      <div className="border rounded-lg">
        <ScrollArea className="h-[600px]">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Kullanıcı ID</TableHead>
                <TableHead>Kayıt Tarihi</TableHead>
                <TableHead>Durum</TableHead>
                <TableHead>Roller</TableHead>
                <TableHead>Abonelik</TableHead>
                <TableHead className="text-right">Toplam Ödeme</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex flex-col items-center space-y-2">
                      <AlertCircle className="h-8 w-8 text-muted-foreground" />
                      <p className="text-muted-foreground">Kullanıcı bulunamadı</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredUsers.map((user) => {
                  const statusDisplay = getUserStatusDisplay(user.newUser);
                  return (
                    <TableRow key={user.userId}>
                      <TableCell className="font-medium">
                        #{user.userId}
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {formatEpochTime(user.createdAt)}
                      </TableCell>
                      <TableCell>
                        <Badge variant={statusDisplay.variant}>
                          {statusDisplay.text}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">
                          {formatRoles(user.roles)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getSubscriptionLevelVariant(user.subscriptionLevel)}>
                          {getSubscriptionLevelDisplayName(user.subscriptionLevel)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {formatCurrency(user.totalPaymentAmount)}
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </ScrollArea>
      </div>
    </div>
  );
}
