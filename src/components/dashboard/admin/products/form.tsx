'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { productsService } from '@/lib/api';
import {
  Product,
  ProductFormValues,
  productFormSchema,
  formValuesToCreateRequest,
  formValuesToUpdateRequest,
  productToFormValues,
  ProductType,
  getProductTypeDisplayName,
} from '@/lib/schemas/products';

interface ProductFormProps {
  readonly product?: Product;
  readonly onSuccess: (product: Product) => void;
  readonly onCancel: () => void;
  readonly inDialog?: boolean;
}

export function ProductForm({ product, onSuccess, onCancel, inDialog = false }: ProductFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isEditing = !!product;

  // Initialize form with react-hook-form and zod validation
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productFormSchema),
    defaultValues: product ? productToFormValues(product) : {
      name: '',
      price: '',
      validityPeriodInMonths: '1',
      type: 'BASIC',
    },
  });

  // Handle form submission
  const onSubmit = async (data: ProductFormValues) => {
    try {
      setIsLoading(true);
      setError(null);

      let result: Product;

      if (isEditing && product) {
        // Update existing product
        const updateData = formValuesToUpdateRequest(data);
        result = await productsService.updateProduct(product.id, updateData);
      } else {
        // Create new product
        const createData = formValuesToCreateRequest(data);
        result = await productsService.createProduct(createData);
      }

      onSuccess(result);
    } catch (err) {
      console.error('Product form error:', err);
      setError(err instanceof Error ? err.message : 'Bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {error && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {error}
          </div>
        )}

        {/* Product Name Field */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Ürün Adı</FormLabel>
              <FormControl>
                <Input placeholder="Ürün adını girin" {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Price Field */}
        <FormField
          control={form.control}
          name="price"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Fiyat (TL)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="0.01"
                  min="0.01"
                  placeholder="0.00"
                  {...field}
                  disabled={isLoading}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Validity Period Field */}
        <FormField
          control={form.control}
          name="validityPeriodInMonths"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Geçerlilik Süresi (Ay)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="1"
                  max="12"
                  placeholder="1"
                  {...field}
                  disabled={isLoading}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Product Type Field */}
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Ürün Türü</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Ürün türünü seçin" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.values(ProductType.enum).map((type) => (
                    <SelectItem key={type} value={type}>
                      {getProductTypeDisplayName(type)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Form Actions */}
        <div className={`flex gap-2 ${inDialog ? 'justify-end' : 'justify-start'} pt-4`}>
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            İptal
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isEditing ? 'Güncelle' : 'Oluştur'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
