'use client';

import { useMemo } from 'react';
import { 
  Files, 
  HardDrive, 
  Users, 
  TrendingUp, 
  FileText, 
  Image, 
  Video, 
  Music,
  Archive,
  Presentation,
  FileSpreadsheet,
  FolderOpen
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  FileStatistics, 
  FileType, 
  formatFileSize, 
  getFileTypeDisplayName 
} from '@/lib/schemas/files';

interface FileStatisticsDashboardProps {
  readonly statistics: FileStatistics | null;
  readonly isLoading: boolean;
  readonly error: string | null;
}

export function FileStatisticsDashboard({ 
  statistics, 
  isLoading, 
  error 
}: FileStatisticsDashboardProps) {
  const fileTypeIcons: Record<FileType, React.ReactNode> = {
    IMAGE: <Image className="h-4 w-4" />,
    DOCUMENT: <FileText className="h-4 w-4" />,
    SPREADSHEET: <FileSpreadsheet className="h-4 w-4" />,
    PRESENTATION: <Presentation className="h-4 w-4" />,
    ARCHIVE: <Archive className="h-4 w-4" />,
    VIDEO: <Video className="h-4 w-4" />,
    AUDIO: <Music className="h-4 w-4" />,
    OTHER: <FolderOpen className="h-4 w-4" />,
  };

  const processedStats = useMemo(() => {
    if (!statistics) return null;

    // Process file type statistics
    const fileTypeStats = Object.entries(statistics.fileCountByType).map(([type, count]) => ({
      type: type as FileType,
      count,
      percentage: statistics.totalFiles > 0 ? (count / statistics.totalFiles) * 100 : 0,
    })).sort((a, b) => b.count - a.count);

    // Process uploader statistics
    const uploaderStats = Object.entries(statistics.fileCountByUploader).map(([uploader, count]) => ({
      uploader,
      count,
      percentage: statistics.totalFiles > 0 ? (count / statistics.totalFiles) * 100 : 0,
    })).sort((a, b) => b.count - a.count).slice(0, 5); // Top 5 uploaders

    return {
      fileTypeStats,
      uploaderStats,
    };
  }, [statistics]);

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (!statistics || !processedStats) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Dosya</CardTitle>
            <Files className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.totalFiles.toLocaleString('tr-TR')}</div>
            <p className="text-xs text-muted-foreground">
              Sistemdeki toplam dosya sayısı
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Boyut</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatFileSize(statistics.totalSize)}</div>
            <p className="text-xs text-muted-foreground">
              Kullanılan depolama alanı
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktif Kullanıcı</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.keys(statistics.fileCountByUploader).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Dosya yükleyen kullanıcı sayısı
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ortalama Boyut</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statistics.totalFiles > 0 
                ? formatFileSize(Math.round(statistics.totalSize / statistics.totalFiles))
                : '0 B'
              }
            </div>
            <p className="text-xs text-muted-foreground">
              Dosya başına ortalama boyut
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Statistics */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* File Type Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Dosya Türü Dağılımı</CardTitle>
            <CardDescription>
              Dosya türlerine göre dağılım
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {processedStats.fileTypeStats.length > 0 ? (
              processedStats.fileTypeStats.map(({ type, count, percentage }) => (
                <div key={type} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {fileTypeIcons[type]}
                      <span className="text-sm font-medium">
                        {getFileTypeDisplayName(type)}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {count}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        %{percentage.toFixed(1)}
                      </span>
                    </div>
                  </div>
                  <Progress value={percentage} className="h-2" />
                </div>
              ))
            ) : (
              <p className="text-sm text-muted-foreground text-center py-4">
                Henüz dosya yüklenmemiş
              </p>
            )}
          </CardContent>
        </Card>

        {/* Top Uploaders */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">En Aktif Kullanıcılar</CardTitle>
            <CardDescription>
              En çok dosya yükleyen kullanıcılar
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {processedStats.uploaderStats.length > 0 ? (
              processedStats.uploaderStats.map(({ uploader, count, percentage }, index) => (
                <div key={uploader} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs font-medium">
                        {index + 1}
                      </div>
                      <span className="text-sm font-medium truncate" style={{ wordWrap: 'break-word' }}>
                        {uploader}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {count}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        %{percentage.toFixed(1)}
                      </span>
                    </div>
                  </div>
                  <Progress value={percentage} className="h-2" />
                </div>
              ))
            ) : (
              <p className="text-sm text-muted-foreground text-center py-4">
                Henüz dosya yüklenmemiş
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Storage Usage Breakdown */}
      {statistics.totalSize > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Depolama Kullanımı</CardTitle>
            <CardDescription>
              Dosya türlerine göre depolama alanı kullanımı
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {processedStats.fileTypeStats.map(({ type, count }) => {
                // Calculate estimated storage usage (this is an approximation since we don't have size by type)
                const estimatedSize = (count / statistics.totalFiles) * statistics.totalSize;
                const percentage = (estimatedSize / statistics.totalSize) * 100;
                
                return (
                  <div key={type} className="text-center p-4 border rounded-lg">
                    <div className="flex justify-center mb-2">
                      {fileTypeIcons[type]}
                    </div>
                    <p className="text-sm font-medium mb-1">
                      {getFileTypeDisplayName(type)}
                    </p>
                    <p className="text-lg font-bold text-primary">
                      {formatFileSize(estimatedSize)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      %{percentage.toFixed(1)} • {count} dosya
                    </p>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
