'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Upload, X, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { filesService } from '@/lib/api';
import { 
  FileUploadFormValues, 
  fileUploadFormSchema, 
  validateFile, 
  formatFileSize, 
  getFileTypeFromFilename,
  getFileIcon,
  MAX_FILE_SIZE 
} from '@/lib/schemas/files';

interface FileUploadProps {
  readonly onSuccess?: (file: unknown) => void;
  readonly onCancel?: () => void;
  readonly inDialog?: boolean;
}

interface UploadingFile {
  file: File;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
}

export function FileUpload({ onSuccess, onCancel }: FileUploadProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const form = useForm<FileUploadFormValues>({
    resolver: zodResolver(fileUploadFormSchema),
    defaultValues: {
      description: '',
      tags: '',
      isPublic: false,
    },
  });

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const validFiles: File[] = [];
    const invalidFiles: string[] = [];

    acceptedFiles.forEach(file => {
      const validation = validateFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        invalidFiles.push(`${file.name}: ${validation.error}`);
      }
    });

    if (invalidFiles.length > 0) {
      toast.error('Bazı dosyalar geçersiz', {
        description: invalidFiles.join(', '),
      });
    }

    setSelectedFiles(prev => [...prev, ...validFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
    maxSize: MAX_FILE_SIZE,
    accept: {
      'image/*': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'application/rtf': ['.rtf'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'text/csv': ['.csv'],
      'application/vnd.ms-powerpoint': ['.ppt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
      'video/*': ['.mp4', '.avi', '.mov', '.wmv', '.flv'],
      'audio/*': ['.mp3', '.wav', '.flac', '.aac', '.ogg'],
      'application/zip': ['.zip'],
      'application/x-rar-compressed': ['.rar'],
      'application/x-7z-compressed': ['.7z'],
      'application/x-tar': ['.tar'],
      'application/gzip': ['.gz'],
    },
  });

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const uploadFiles = async (values: FileUploadFormValues) => {
    if (selectedFiles.length === 0) {
      toast.error('Lütfen en az bir dosya seçin');
      return;
    }

    setIsUploading(true);
    const uploadingFilesData: UploadingFile[] = selectedFiles.map(file => ({
      file,
      progress: 0,
      status: 'uploading' as const,
    }));
    setUploadingFiles(uploadingFilesData);

    try {
      const uploadPromises = selectedFiles.map(async (file, index) => {
        try {
          // Simulate progress for better UX
          const progressInterval = setInterval(() => {
            setUploadingFiles(prev => 
              prev.map((item, i) => 
                i === index && item.status === 'uploading'
                  ? { ...item, progress: Math.min(item.progress + 10, 90) }
                  : item
              )
            );
          }, 200);

          const result = await filesService.uploadFile(file, values);

          clearInterval(progressInterval);
          setUploadingFiles(prev => 
            prev.map((item, i) => 
              i === index 
                ? { ...item, progress: 100, status: 'success' as const }
                : item
            )
          );

          return result;
        } catch (error) {
          setUploadingFiles(prev => 
            prev.map((item, i) => 
              i === index 
                ? { 
                    ...item, 
                    status: 'error' as const, 
                    error: error instanceof Error ? error.message : 'Yükleme hatası'
                  }
                : item
            )
          );
          throw error;
        }
      });

      const results = await Promise.allSettled(uploadPromises);
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const errorCount = results.filter(r => r.status === 'rejected').length;

      if (successCount > 0) {
        toast.success(`${successCount} dosya başarıyla yüklendi`);
        if (onSuccess) {
          onSuccess(results);
        }
      }

      if (errorCount > 0) {
        toast.error(`${errorCount} dosya yüklenemedi`);
      }

      // Reset form if all files uploaded successfully
      if (errorCount === 0) {
        setSelectedFiles([]);
        setUploadingFiles([]);
        form.reset();
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Dosyalar yüklenirken hata oluştu');
    } finally {
      setIsUploading(false);
    }
  };

  const onSubmit = (values: FileUploadFormValues) => {
    uploadFiles(values);
  };

  return (
    <div className="space-y-6">
      {/* File Drop Zone */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Dosya Yükle
          </CardTitle>
          <CardDescription>
            Dosyaları sürükleyip bırakın veya seçmek için tıklayın. Maksimum dosya boyutu: {formatFileSize(MAX_FILE_SIZE)}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",
              isDragActive 
                ? "border-primary bg-primary/5" 
                : "border-muted-foreground/25 hover:border-primary/50"
            )}
          >
            <input {...getInputProps()} />
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            {isDragActive ? (
              <p className="text-lg font-medium">Dosyaları buraya bırakın...</p>
            ) : (
              <div>
                <p className="text-lg font-medium mb-2">
                  Dosyaları sürükleyip bırakın veya seçmek için tıklayın
                </p>
                <p className="text-sm text-muted-foreground">
                  PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, RTF, CSV, resim, video, ses ve arşiv dosyaları desteklenir
                </p>
              </div>
            )}
          </div>

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <div className="mt-4 space-y-2">
              <h4 className="font-medium">Seçilen Dosyalar ({selectedFiles.length})</h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {selectedFiles.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-muted rounded-lg">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <span className="text-lg">
                        {getFileIcon(getFileTypeFromFilename(file.name))}
                      </span>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{file.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {formatFileSize(file.size)}
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                      className="h-8 w-8 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Açıklama</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Dosya açıklaması (isteğe bağlı)"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Dosya hakkında kısa bir açıklama ekleyebilirsiniz
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="tags"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Etiketler</FormLabel>
                <FormControl>
                  <Input
                    placeholder="etiket1, etiket2, etiket3"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Dosyayı kategorize etmek için virgülle ayrılmış etiketler ekleyebilirsiniz
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex gap-2 pt-4">
            <Button
              type="submit"
              disabled={isUploading || selectedFiles.length === 0}
              className="flex-1"
            >
              {isUploading ? 'Yükleniyor...' : `${selectedFiles.length} Dosyayı Yükle`}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isUploading}
              >
                İptal
              </Button>
            )}
          </div>
        </form>
      </Form>

      {/* Upload Progress */}
      {uploadingFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Yükleme Durumu</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {uploadingFiles.map((uploadingFile, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <span className="text-lg">
                      {getFileIcon(getFileTypeFromFilename(uploadingFile.file.name))}
                    </span>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {uploadingFile.file.name}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatFileSize(uploadingFile.file.size)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {uploadingFile.status === 'uploading' && (
                      <span className="text-xs text-muted-foreground">
                        %{uploadingFile.progress}
                      </span>
                    )}
                    {uploadingFile.status === 'success' && (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    )}
                    {uploadingFile.status === 'error' && (
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    )}
                  </div>
                </div>
                
                {uploadingFile.status === 'uploading' && (
                  <Progress value={uploadingFile.progress} className="h-2" />
                )}
                
                {uploadingFile.status === 'error' && uploadingFile.error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-xs">
                      {uploadingFile.error}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
