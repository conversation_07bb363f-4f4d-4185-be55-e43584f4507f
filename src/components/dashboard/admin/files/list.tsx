'use client';

import { useState, useMemo } from 'react';
import {
  Search,
  Download,
  Trash2,
  Eye,
  MoreHorizontal,
  Calendar,
  User,
  FileText,
  Globe,
  Lock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  FileListItem, 
  FileType, 
  getFileTypeDisplayName, 
  getFileIcon, 
  formatFileSize 
} from '@/lib/schemas/files';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';

interface FileListProps {
  readonly files: FileListItem[];
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly onRefresh: () => void;
  readonly onDownload: (file: FileListItem) => void;
  readonly onDelete: (file: FileListItem) => void;
  readonly onView: (file: FileListItem) => void;
  readonly onBulkDelete: (files: FileListItem[]) => void;
}

export function FileList({
  files,
  isLoading,
  error,
  onRefresh,
  onDownload,
  onDelete,
  onView,
  onBulkDelete,
}: FileListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<number[]>([]);
  const [filterType, setFilterType] = useState<FileType | 'ALL'>('ALL');
  const [filterPublic, setFilterPublic] = useState<'ALL' | 'PUBLIC' | 'PRIVATE'>('ALL');


  // Filter and search files
  const filteredFiles = useMemo(() => {
    return files.filter(file => {
      // Search filter
      const matchesSearch = searchQuery === '' || 
        file.originalFilename.toLowerCase().includes(searchQuery.toLowerCase()) ||
        file.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        file.tags?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        file.uploaderName?.toLowerCase().includes(searchQuery.toLowerCase());

      // Type filter
      const matchesType = filterType === 'ALL' || file.fileType === filterType;

      // Public filter
      const matchesPublic = filterPublic === 'ALL' || 
        (filterPublic === 'PUBLIC' && file.isPublic) ||
        (filterPublic === 'PRIVATE' && !file.isPublic);

      return matchesSearch && matchesType && matchesPublic;
    });
  }, [files, searchQuery, filterType, filterPublic]);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedFiles(filteredFiles.map(file => file.id));
    } else {
      setSelectedFiles([]);
    }
  };

  const handleSelectFile = (fileId: number, checked: boolean) => {
    if (checked) {
      setSelectedFiles(prev => [...prev, fileId]);
    } else {
      setSelectedFiles(prev => prev.filter(id => id !== fileId));
    }
  };

  const handleBulkDelete = () => {
    const filesToDelete = files.filter(file => selectedFiles.includes(file.id));
    onBulkDelete(filesToDelete);
    setSelectedFiles([]);
  };

  
  const formatDate = (epochTime: number) => {
    try {
      // Convert to milliseconds (epoch time is in seconds with decimal precision)
      const date = new Date(epochTime * 1000);
      return format(date, 'dd MMM yyyy HH:mm', { locale: tr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(epochTime);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
                <Skeleton className="h-8 w-20" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={onRefresh} variant="outline">
            Tekrar Dene
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters and Search */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Dosya ara..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Select value={filterType} onValueChange={(value) => setFilterType(value as FileType | 'ALL')}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Tür" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">Tüm Türler</SelectItem>
                  <SelectItem value="IMAGE">Resim</SelectItem>
                  <SelectItem value="DOCUMENT">Belge</SelectItem>
                  <SelectItem value="SPREADSHEET">Tablo</SelectItem>
                  <SelectItem value="PRESENTATION">Sunum</SelectItem>
                  <SelectItem value="ARCHIVE">Arşiv</SelectItem>
                  <SelectItem value="VIDEO">Video</SelectItem>
                  <SelectItem value="AUDIO">Ses</SelectItem>
                  <SelectItem value="OTHER">Diğer</SelectItem>
                </SelectContent>
              </Select>

              {selectedFiles.length > 0 && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBulkDelete}
                  className="gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Seçilenleri Sil ({selectedFiles.length})
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Files List */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Dosyalar</CardTitle>
              <CardDescription>
                {filteredFiles.length} dosya bulundu
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {filteredFiles.length === 0 ? (
            <div className="p-8 text-center">
              <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-lg font-medium mb-2">Dosya bulunamadı</p>
              <p className="text-muted-foreground">
                {searchQuery || filterType !== 'ALL' || filterPublic !== 'ALL'
                  ? 'Arama kriterlerinizi değiştirmeyi deneyin'
                  : 'Henüz hiç dosya yüklenmemiş'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedFiles.length === filteredFiles.length && filteredFiles.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead className="w-12">ID</TableHead>
                    <TableHead>Dosya</TableHead>
                    <TableHead className="hidden md:table-cell">İndirme Sayısı</TableHead>
                    <TableHead className="hidden md:table-cell">Tür</TableHead>
                    <TableHead className="hidden md:table-cell">Boyut</TableHead>
                    <TableHead className="hidden lg:table-cell">Yükleyen</TableHead>
                    <TableHead className="hidden lg:table-cell">Yüklenme Tarihi</TableHead>
                    <TableHead className="w-12">İşlem</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredFiles.map((file) => (
                    <TableRow key={file.id}>
                      
                      <TableCell>
                        <Checkbox
                          checked={selectedFiles.includes(file.id)}
                          onCheckedChange={(checked) => handleSelectFile(file.id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell className="w-12">
                        {file.id}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <span className="text-2xl">
                            {getFileIcon(file.fileType)}
                          </span>
                          <div className="min-w-0 flex-1">
                            <p className="font-medium truncate" style={{ wordWrap: 'break-word' }}>
                              {file.originalFilename}
                            </p>
                            {file.description && (
                              <p className="text-sm text-muted-foreground truncate" style={{ wordWrap: 'break-word' }}>
                                {file.description}
                              </p>
                            )}
                            {file.tags && (
                              <div className="flex flex-wrap gap-1 mt-1">
                                {file.tags.split(',').slice(0, 2).map((tag, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    {tag.trim()}
                                  </Badge>
                                ))}
                                {file.tags.split(',').length > 2 && (
                                  <Badge variant="secondary" className="text-xs">
                                    +{file.tags.split(',').length - 2}
                                  </Badge>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {file.downloadCount}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        <Badge variant="outline">
                          {getFileTypeDisplayName(file.fileType)}
                        </Badge>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {file.formattedFileSize || formatFileSize(file.fileSize)}
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{file.uploaderName || 'Bilinmiyor'}</span>
                        </div>
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{formatDate(file.createdAt)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => onView(file)}>
                              <Eye className="h-4 w-4 mr-2" />
                              Görüntüle
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onDownload(file)}>
                              <Download className="h-4 w-4 mr-2" />
                              İndir
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => onDelete(file)}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Sil
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
