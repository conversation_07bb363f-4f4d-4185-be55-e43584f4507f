'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import {
  Download,
  Trash2,
  Eye,
  AlertTriangle,
  FileText,
  Calendar,
  User,
  Hash,
  Globe,
  Lock,
  Tag
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { filesService } from '@/lib/api';
import {
  FileListItem,
  FileResponse,
  getFileTypeDisplayName,
  getFileIcon,
  formatFileSize
} from '@/lib/schemas/files';

interface FilePreviewDialogProps {
  readonly file: FileListItem | null;
  readonly open: boolean;
  readonly onOpenChange: (open: boolean) => void;
  readonly onDownload: (file: FileListItem) => void;
  readonly onDelete: (file: FileListItem) => void;
}

export function FilePreviewDialog({ 
  file, 
  open, 
  onOpenChange, 
  onDownload, 
  onDelete 
}: FilePreviewDialogProps) {
  const [fileDetails, setFileDetails] = useState<FileResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadFileDetails = async (fileId: number) => {
    setLoading(true);
    setError(null);
    try {
      const details = await filesService.getFileById(fileId);
      setFileDetails(details);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Dosya detayları yüklenemedi');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen && file) {
      loadFileDetails(file.id);
    } else {
      setFileDetails(null);
      setError(null);
    }
    onOpenChange(newOpen);
  };

  const formatDate = (epochTime: number) => {
    try {
      // Convert to milliseconds (epoch time is in seconds with decimal precision)
      const date = new Date(epochTime * 1000);
      return format(date, 'dd MMM yyyy HH:mm', { locale: tr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(epochTime);
    }
  };

  if (!file) return null;

  const displayFile = fileDetails || file;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span className="text-2xl">
              {getFileIcon(file.fileType)}
            </span>
            <div className="flex-1 min-w-0">
              <p className="truncate" style={{ wordWrap: 'break-word' }}>
                {file.originalFilename}
              </p>
              <p className="text-sm text-muted-foreground font-normal">
                {getFileTypeDisplayName(file.fileType)} • {formatFileSize(file.fileSize)}
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {!loading && !error && (
            <>
              {/* File Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Dosya Bilgileri</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        <span className="font-medium">Tür:</span> {getFileTypeDisplayName(file.fileType)}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Hash className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        <span className="font-medium">Boyut:</span> {formatFileSize(file.fileSize)}
                      </span>
                    </div>

                    

                    {displayFile.downloadCount !== undefined && (
                      <div className="flex items-center gap-2">
                        <Download className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          <span className="font-medium">İndirme:</span> {displayFile.downloadCount} kez
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Yükleme Bilgileri</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        <span className="font-medium">Yükleyen:</span> {file.uploaderName || 'Bilinmiyor'}
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        <span className="font-medium">Yüklenme:</span> {formatDate(file.createdAt)}
                      </span>
                    </div>

                    {displayFile.lastAccessedAt && (
                      <div className="flex items-center gap-2">
                        <Eye className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          <span className="font-medium">Son Erişim:</span> {formatDate(displayFile.lastAccessedAt)}
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Description */}
              {file.description && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Açıklama</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm" style={{ wordWrap: 'break-word' }}>
                      {file.description}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Tags */}
              {file.tags && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Tag className="h-4 w-4" />
                      Etiketler
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {file.tags.split(',').map((tag, index) => (
                        <Badge key={index} variant="secondary">
                          {tag.trim()}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* MD5 Hash */}
              {'md5Hash' in displayFile && displayFile.md5Hash && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Dosya Doğrulama</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">MD5:</span>
                      <code className="text-xs bg-muted px-2 py-1 rounded font-mono break-all">
                        {displayFile.md5Hash}
                      </code>
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={() => onDownload(file)}
            className="gap-2"
          >
            <Download className="h-4 w-4" />
            İndir
          </Button>
          <Button
            variant="destructive"
            onClick={() => onDelete(file)}
            className="gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Sil
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

interface DeleteConfirmationDialogProps {
  readonly file: FileListItem | null;
  readonly open: boolean;
  readonly onOpenChange: (open: boolean) => void;
  readonly onConfirm: () => void;
  readonly isDeleting: boolean;
}

export function DeleteConfirmationDialog({
  file,
  open,
  onOpenChange,
  onConfirm,
  isDeleting,
}: DeleteConfirmationDialogProps) {
  if (!file) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Dosyayı Sil
          </DialogTitle>
          <DialogDescription>
            Bu işlem geri alınamaz. Dosya kalıcı olarak silinecektir.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
            <span className="text-2xl">
              {getFileIcon(file.fileType)}
            </span>
            <div className="flex-1 min-w-0">
              <p className="font-medium truncate" style={{ wordWrap: 'break-word' }}>
                {file.originalFilename}
              </p>
              <p className="text-sm text-muted-foreground">
                {getFileTypeDisplayName(file.fileType)} • {formatFileSize(file.fileSize)}
              </p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
          >
            İptal
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? 'Siliniyor...' : 'Sil'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

interface BulkDeleteConfirmationDialogProps {
  readonly files: FileListItem[];
  readonly open: boolean;
  readonly onOpenChange: (open: boolean) => void;
  readonly onConfirm: () => void;
  readonly isDeleting: boolean;
}

export function BulkDeleteConfirmationDialog({
  files,
  open,
  onOpenChange,
  onConfirm,
  isDeleting,
}: BulkDeleteConfirmationDialogProps) {
  const totalSize = files.reduce((sum, file) => sum + file.fileSize, 0);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Çoklu Dosya Silme
          </DialogTitle>
          <DialogDescription>
            Bu işlem geri alınamaz. Seçilen {files.length} dosya kalıcı olarak silinecektir.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 space-y-4">
          <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
            <div>
              <p className="font-medium">{files.length} dosya seçildi</p>
              <p className="text-sm text-muted-foreground">
                Toplam boyut: {formatFileSize(totalSize)}
              </p>
            </div>
          </div>

          {files.length <= 5 ? (
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {files.map((file) => (
                <div key={file.id} className="flex items-center gap-3 p-2 bg-background rounded border">
                  <span className="text-lg">
                    {getFileIcon(file.fileType)}
                  </span>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate" style={{ wordWrap: 'break-word' }}>
                      {file.originalFilename}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(file.fileSize)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {files.length} dosya silinecek. İlk 5 dosya: {files.slice(0, 5).map(f => f.originalFilename).join(', ')}...
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
          >
            İptal
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? 'Siliniyor...' : `${files.length} Dosyayı Sil`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
