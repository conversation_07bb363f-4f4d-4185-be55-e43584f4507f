'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Calendar } from '@/components/ui/calendar';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { couponsService } from '@/lib/api';
import {
  Coupon,
  CouponFormValues,
  couponFormSchema,
  formValuesToCreateRequest,
  formValuesToUpdateRequest,
  couponToFormValues,
  DiscountType,
  getDiscountTypeDisplayName,
} from '@/lib/schemas/coupons';

interface CouponFormProps {
  readonly coupon?: Coupon;
  readonly onSuccess: (coupon: Coupon) => void;
  readonly onCancel: () => void;
  readonly inDialog?: boolean;
}

export function CouponForm({ coupon, onSuccess, onCancel, inDialog = false }: CouponFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expirationDate, setExpirationDate] = useState<Date | undefined>(undefined);

  const isEditing = !!coupon;

  // Initialize form with react-hook-form and zod validation
  const form = useForm<CouponFormValues>({
    resolver: zodResolver(couponFormSchema),
    defaultValues: coupon ? couponToFormValues(coupon) : {
      code: '',
      description: '',
      discountType: 'PERCENTAGE',
      discountValue: '',
      usageLimit: '100',
      expirationDate: '',
      expirationHour: '23',
      expirationMinute: '59',
      active: true,
    },
  });

  // Function to update the combined date-time when hour or minute changes
  const updateDateTime = () => {
    const currentDate = form.getValues('expirationDate');
    const currentHour = form.getValues('expirationHour');
    const currentMinute = form.getValues('expirationMinute');

    if (currentDate && currentHour && currentMinute) {
      const date = new Date(currentDate);
      date.setHours(parseInt(currentHour), parseInt(currentMinute), 0, 0);
      form.setValue('expirationDate', date.toISOString());
    }
  };

  // Handle form submission
  const onSubmit = async (data: CouponFormValues) => {
    try {
      setIsLoading(true);
      setError(null);

      let result: Coupon;

      if (isEditing && coupon) {
        // Update existing coupon
        const updateData = formValuesToUpdateRequest(data);
        result = await couponsService.updateCoupon(coupon.id, updateData);
      } else {
        // Create new coupon
        const createData = formValuesToCreateRequest(data);
        result = await couponsService.createCoupon(createData);
      }

      onSuccess(result);
    } catch (err) {
      console.error('Coupon form error:', err);
      setError(err instanceof Error ? err.message : 'Bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {error && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {error}
          </div>
        )}

        {/* Coupon Code Field */}
        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Kupon Kodu</FormLabel>
              <FormControl>
                <Input 
                  placeholder="SUMMER2024" 
                  {...field} 
                  disabled={isLoading || isEditing} // Code cannot be changed when editing
                  className="uppercase"
                  onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                />
              </FormControl>
              <FormDescription>
                {isEditing ? 'Kupon kodu düzenlenemez' : '3-50 karakter arası, benzersiz olmalıdır'}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Description Field */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Açıklama (İsteğe Bağlı)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Kupon açıklaması..."
                  {...field}
                  disabled={isLoading}
                  rows={3}
                />
              </FormControl>
              <FormDescription>
                Maksimum 255 karakter
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Discount Type and Value */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="discountType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>İndirim Türü</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="İndirim türünü seçin" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.values(DiscountType.enum).map((type) => (
                      <SelectItem key={type} value={type}>
                        {getDiscountTypeDisplayName(type)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="discountValue"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  İndirim Değeri {form.watch('discountType') === 'PERCENTAGE' ? '(%)' : '(TL)'}
                </FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    min="0.01"
                    max={form.watch('discountType') === 'PERCENTAGE' ? '100' : '999999.99'}
                    placeholder={form.watch('discountType') === 'PERCENTAGE' ? '10' : '50.00'}
                    {...field}
                    disabled={isLoading}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Usage Limit Field */}
        <FormField
          control={form.control}
          name="usageLimit"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Kullanım Limiti</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="1"
                  placeholder="100"
                  {...field}
                  disabled={isLoading}
                />
              </FormControl>
              <FormDescription>
                Bu kuponun kaç kez kullanılabileceğini belirler
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Expiration Date and Time Fields */}
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="expirationDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Son Kullanma Tarihi (İsteğe Bağlı)</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                        disabled={isLoading}
                      >
                        {field.value ? (
                          format(new Date(field.value), "PPP", { locale: tr })
                        ) : (
                          <span>Tarih seçin</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value ? new Date(field.value) : undefined}
                      onSelect={(date) => {
                        if (date) {
                          // Preserve existing time if any, otherwise set to current time
                          const existingDate = field.value ? new Date(field.value) : new Date();
                          date.setHours(existingDate.getHours());
                          date.setMinutes(existingDate.getMinutes());
                          field.onChange(date.toISOString());
                          setExpirationDate(date);
                        } else {
                          field.onChange('');
                          setExpirationDate(undefined);
                        }
                      }}
                      disabled={(date) => {
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);
                        return date < today;
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  Belirtilmezse kupon süresiz olur
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Time Fields - Only show if date is selected */}
          {form.watch('expirationDate') && (
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="expirationHour"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Saat</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        updateDateTime();
                      }}
                      value={field.value || '23'}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Saat" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Array.from({ length: 24 }, (_, i) => (
                          <SelectItem key={i} value={i.toString().padStart(2, '0')}>
                            {i.toString().padStart(2, '0')}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expirationMinute"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Dakika</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        updateDateTime();
                      }}
                      value={field.value || '59'}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Dakika" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Array.from({ length: 60 }, (_, i) => (
                          <SelectItem key={i} value={i.toString().padStart(2, '0')}>
                            {i.toString().padStart(2, '0')}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          )}
        </div>

        {/* Active Switch */}
        <FormField
          control={form.control}
          name="active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Aktif Durum</FormLabel>
                <FormDescription>
                  Kuponun kullanılabilir olup olmadığını belirler
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  disabled={isLoading}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {/* Form Actions */}
        <div className={`flex gap-2 ${inDialog ? 'justify-end' : 'justify-start'} pt-4`}>
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            İptal
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isEditing ? 'Güncelle' : 'Oluştur'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
