'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Ticket,
  AlertCircle,
  RefreshCw,
  Co<PERSON>,
  Clock,
  Users,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { couponsService } from '@/lib/api';
import { Coupon, getDiscountTypeDisplayName, formatDiscountValue } from '@/lib/schemas/coupons';
import { CouponForm } from './form';

interface CouponListProps {
  readonly coupons: Coupon[];
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly onRefresh: () => void;
}

export function CouponList({ coupons, isLoading, error, onRefresh }: CouponListProps) {
  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Bulk selection state
  const [selectedCoupons, setSelectedCoupons] = useState<Set<number>>(new Set());
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);
  const [bulkDeleteError, setBulkDeleteError] = useState<string | null>(null);
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);

  // Handle edit coupon
  const handleEdit = (coupon: Coupon) => {
    setSelectedCoupon(coupon);
    setEditDialogOpen(true);
  };

  // Handle delete coupon
  const handleDelete = (coupon: Coupon) => {
    setSelectedCoupon(coupon);
    setDeleteDialogOpen(true);
    setDeleteError(null);
  };

  // Handle bulk selection
  const handleSelectCoupon = (couponId: number, checked: boolean) => {
    const newSelected = new Set(selectedCoupons);
    if (checked) {
      newSelected.add(couponId);
    } else {
      newSelected.delete(couponId);
    }
    setSelectedCoupons(newSelected);
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCoupons(new Set(coupons.map(c => c.id)));
    } else {
      setSelectedCoupons(new Set());
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedCoupons.size === 0) return;
    setBulkDeleteDialogOpen(true);
    setBulkDeleteError(null);
  };

  // Handle copy coupon code
  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      toast.success('Kupon kodu kopyalandı', {
        description: `"${code}" panoya kopyalandı.`
      });
    } catch (err) {
      toast.error('Kopyalama başarısız', {
        description: 'Kupon kodu kopyalanamadı.'
      });
    }
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedCoupon) return;

    try {
      setIsDeleting(true);
      setDeleteError(null);

      await couponsService.deleteCoupon(selectedCoupon.id);

      toast.success('Kupon pasifleştirildi', {
        description: `"${selectedCoupon.code}" kuponu başarıyla pasifleştirildi.`
      });

      setDeleteDialogOpen(false);
      setSelectedCoupon(null);
      onRefresh();
    } catch (err) {
      console.error('Delete coupon error:', err);
      setDeleteError(err instanceof Error ? err.message : 'Kupon pasifleştirilirken hata oluştu');
    } finally {
      setIsDeleting(false);
    }
  };

  // Confirm bulk delete
  const confirmBulkDelete = async () => {
    if (selectedCoupons.size === 0) return;

    try {
      setIsBulkDeleting(true);
      setBulkDeleteError(null);

      const idsToDelete = Array.from(selectedCoupons);
      const results = await couponsService.bulkDeleteCoupons(idsToDelete);

      if (results.successful.length > 0) {
        toast.success('Kuponlar pasifleştirildi', {
          description: `${results.successful.length} kupon başarıyla pasifleştirildi.`
        });
      }

      if (results.failed.length > 0) {
        toast.error('Bazı kuponlar pasifleştirilemedi', {
          description: `${results.failed.length} kupon pasifleştirilirken hata oluştu.`
        });
        setBulkDeleteError(`${results.failed.length} kupon pasifleştirilirken hata oluştu. Lütfen tekrar deneyin.`);
      } else {
        setBulkDeleteDialogOpen(false);
        setSelectedCoupons(new Set());
      }

      onRefresh();
    } catch (err) {
      setBulkDeleteError(err instanceof Error ? err.message : 'Kuponlar pasifleştirilirken hata oluştu');
    } finally {
      setIsBulkDeleting(false);
    }
  };

  // Handle form success
  const handleFormSuccess = (coupon: Coupon) => {
    setEditDialogOpen(false);
    setSelectedCoupon(null);
    onRefresh();
    toast.success('Kupon güncellendi', {
      description: `"${coupon.code}" kuponu başarıyla güncellendi.`
    });
  };

  // Format date from epoch time (seconds with decimal precision)
  const formatDate = (epochTime: number) => {
    try {
      // Convert seconds to milliseconds by multiplying by 1000
      const date = new Date(epochTime * 1000);
      return format(date, 'dd MMM yyyy HH:mm', { locale: tr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(epochTime);
    }
  };

  // Format expiration date from array format or ISO string
  const formatExpirationDate = (expirationDate?: string | number[]) => {
    if (!expirationDate) return 'Süresiz';

    try {
      let date: Date;

      if (Array.isArray(expirationDate)) {
        // Handle array format: [year, month, day, hour, minute, second, nanosecond]
        const [year, month, day, hour = 0, minute = 0, second = 0] = expirationDate;
        // Note: JavaScript Date month is 0-based, but API month appears to be 1-based
        date = new Date(year, month - 1, day, hour, minute, second);
      } else {
        // Handle ISO string format
        date = new Date(expirationDate);
      }

      return format(date, 'dd MMM yyyy HH:mm', { locale: tr });
    } catch (error) {
      console.error('Error formatting expiration date:', error, expirationDate);
      return 'Geçersiz tarih';
    }
  };

  // Get status badge
  const getStatusBadge = (coupon: Coupon) => {
    if (!coupon.active) {
      return <Badge variant="secondary">Pasif</Badge>;
    }
    if (coupon.isExpired) {
      return <Badge variant="destructive">Süresi Dolmuş</Badge>;
    }
    if (coupon.isUsageLimitExceeded) {
      return <Badge variant="destructive">Limit Aşıldı</Badge>;
    }
    if (coupon.isValid) {
      return <Badge variant="default">Aktif</Badge>;
    }
    return <Badge variant="outline">Geçersiz</Badge>;
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">Kuponlar yüklenemedi</h3>
        <p className="text-muted-foreground mb-4 max-w-md">
          {error}
        </p>
        <Button onClick={onRefresh} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Tekrar Dene
        </Button>
      </div>
    );
  }

  // Empty state
  if (coupons.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <Ticket className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">Kupon bulunamadı</h3>
        <p className="text-muted-foreground">
          Henüz hiç kupon eklenmemiş veya arama kriterlerinize uygun kupon bulunamadı.
        </p>
      </div>
    );
  }

  // Check if all coupons are selected
  const isAllSelected = coupons.length > 0 && selectedCoupons.size === coupons.length;
  const isIndeterminate = selectedCoupons.size > 0 && selectedCoupons.size < coupons.length;

  return (
    <>
      {/* Bulk Actions Bar */}
      {selectedCoupons.size > 0 && (
        <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg mb-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">
              {selectedCoupons.size} kupon seçildi
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedCoupons(new Set())}
            >
              Seçimi Temizle
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleBulkDelete}
              disabled={isBulkDeleting}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {isBulkDeleting ? 'İşleniyor...' : 'Seçilenleri Pasifleştir'}
            </Button>
          </div>
        </div>
      )}

      {/* Desktop Table View */}
      <div className="hidden md:block rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox
                  checked={isAllSelected}
                  onCheckedChange={handleSelectAll}
                  aria-label="Tümünü seç"
                  className={isIndeterminate ? "data-[state=checked]:bg-primary" : ""}
                />
              </TableHead>
              <TableHead>Kupon Kodu</TableHead>
              <TableHead>İndirim</TableHead>
              <TableHead>Kullanım</TableHead>
              <TableHead>Son Kullanma</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>Oluşturulma</TableHead>
              <TableHead className="w-[70px]">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {coupons.map((coupon) => (
              <TableRow key={coupon.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedCoupons.has(coupon.id)}
                    onCheckedChange={(checked) => handleSelectCoupon(coupon.id, checked as boolean)}
                    aria-label={`Kupon ${coupon.code} seç`}
                  />
                </TableCell>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <span className="font-mono">{coupon.code}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => handleCopyCode(coupon.code)}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                  {coupon.description && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {coupon.description}
                    </p>
                  )}
                </TableCell>
                <TableCell>
                  <div>
                    <span className="font-medium">
                      {formatDiscountValue(coupon.discountValue, coupon.discountType)}
                    </span>
                    <p className="text-xs text-muted-foreground">
                      {getDiscountTypeDisplayName(coupon.discountType)}
                    </p>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3 text-muted-foreground" />
                    <span className="text-sm">
                      {coupon.currentUsageCount} / {coupon.usageLimit}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3 text-muted-foreground" />
                    <span className="text-sm">
                      {formatExpirationDate(coupon.expirationDate)}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(coupon)}
                </TableCell>
                <TableCell className="text-muted-foreground text-sm">
                  {formatDate(coupon.createdAt)}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleEdit(coupon)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Düzenle
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDelete(coupon)}
                        className="text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Pasifleştir
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden space-y-4">
        {coupons.map((coupon) => (
          <div key={coupon.id} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3 flex-1 min-w-0">
                <Checkbox
                  checked={selectedCoupons.has(coupon.id)}
                  onCheckedChange={(checked) => handleSelectCoupon(coupon.id, checked as boolean)}
                  aria-label={`Kupon ${coupon.code} seç`}
                  className="mt-1"
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-mono font-medium text-sm">{coupon.code}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => handleCopyCode(coupon.code)}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                  {coupon.description && (
                    <p className="text-xs text-muted-foreground mb-2 break-words">
                      {coupon.description}
                    </p>
                  )}
                  <div className="flex items-center gap-2">
                    {getStatusBadge(coupon)}
                  </div>
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0 flex-shrink-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleEdit(coupon)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Düzenle
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleDelete(coupon)}
                    className="text-destructive"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Pasifleştir
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">İndirim:</span>
                <div className="font-medium">
                  {formatDiscountValue(coupon.discountValue, coupon.discountType)}
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">Kullanım:</span>
                <div className="font-medium">
                  {coupon.currentUsageCount} / {coupon.usageLimit}
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">Son Kullanma:</span>
                <div className="font-medium">
                  {formatExpirationDate(coupon.expirationDate)}
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">Oluşturulma:</span>
                <div className="font-medium">
                  {formatDate(coupon.createdAt)}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Edit Coupon Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto mx-4">
          <DialogHeader>
            <DialogTitle>Kuponu Düzenle</DialogTitle>
            <DialogDescription>
              Kupon bilgilerini güncelleyin
            </DialogDescription>
          </DialogHeader>
          <div className="max-h-[60vh] overflow-y-auto">
            {selectedCoupon && (
              <CouponForm
                coupon={selectedCoupon}
                onSuccess={handleFormSuccess}
                onCancel={() => setEditDialogOpen(false)}
                inDialog={true}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Coupon Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Kuponu Pasifleştir</AlertDialogTitle>
            <AlertDialogDescription>
              "{selectedCoupon?.code}" kupon pasifleştirilecek.
              Devam etmek istediğinizden emin misiniz?
            </AlertDialogDescription>
          </AlertDialogHeader>
          {deleteError && (
            <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
              {deleteError}
            </div>
          )}
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Pasifleştiriliyor...' : 'Pasifleştir'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Confirmation Dialog */}
      <AlertDialog open={bulkDeleteDialogOpen} onOpenChange={setBulkDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Seçili Kuponları Pasifleştir</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedCoupons.size} kupon pasifleştirilecek.
              Devam etmek istediğinizden emin misiniz?
            </AlertDialogDescription>
          </AlertDialogHeader>
          {bulkDeleteError && (
            <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
              {bulkDeleteError}
            </div>
          )}
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isBulkDeleting}>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmBulkDelete}
              disabled={isBulkDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isBulkDeleting ? 'İşleniyor...' : `${selectedCoupons.size} Kuponu Pasifleştir`}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
