'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import {
  MoreHorizontal,
  Eye,
  CreditCard,
  AlertCircle,
  RefreshCw,
  ExternalLink,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Payment, getPaymentStatusDisplayName, getPaymentStatusVariant, formatCurrency } from '@/lib/schemas/payments';

interface PaymentListProps {
  readonly payments: Payment[];
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly onRefresh: () => void;
}

export function PaymentList({ payments, isLoading, error, onRefresh }: PaymentListProps) {
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);

  // Handle view details
  const handleViewDetails = (payment: Payment) => {
    setSelectedPayment(payment);
    setDetailsOpen(true);
  };

  // Format date from epoch time (with microseconds)
  const formatDate = (epochTime: number) => {
    try {
      // Convert to milliseconds (epoch time is in seconds with decimal precision)
      const date = new Date(epochTime * 1000);
      return format(date, 'dd MMM yyyy HH:mm', { locale: tr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(epochTime);
    }
  };

  // Format validity date
  const formatValidityDate = (epochTime: number) => {
    try {
      const date = new Date(epochTime * 1000);
      return format(date, 'dd MMM yyyy', { locale: tr });
    } catch (error) {
      console.error('Error formatting validity date:', error);
      return 'Geçersiz tarih';
    }
  };

  // Show loading skeleton
  if (isLoading) {
    return (
      <div className="p-6 space-y-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="p-6 text-center">
        <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">Hata Oluştu</h3>
        <p className="text-muted-foreground mb-4">{error}</p>
        <Button onClick={onRefresh} variant="outline">
          <RefreshCw className="mr-2 h-4 w-4" />
          Tekrar Dene
        </Button>
      </div>
    );
  }

  // Show empty state
  if (payments.length === 0) {
    return (
      <div className="p-6 text-center">
        <CreditCard className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">Ödeme Bulunamadı</h3>
        <p className="text-muted-foreground mb-4">
          Henüz hiç ödeme işlemi bulunmuyor.
        </p>
        <Button onClick={onRefresh} variant="outline">
          <RefreshCw className="mr-2 h-4 w-4" />
          Yenile
        </Button>
      </div>
    );
  }

  return (
    <>
      {/* Desktop Table View */}
      <div className="hidden md:block rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Ödeme ID</TableHead>
              <TableHead>Ürün</TableHead>
              <TableHead>Tutar</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>Kupon</TableHead>
              <TableHead>Ödeme Tarihi</TableHead>
              <TableHead>Geçerlilik</TableHead>
              <TableHead className="w-[70px]">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {payments.map((payment) => (
              <TableRow key={payment.id}>
                <TableCell className="font-mono text-sm">
                  {payment.paymentId.slice(0, 8)}...
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{payment.product.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {payment.product.type}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{formatCurrency(payment.paidPrice)}</div>
                    {payment.originalPrice && payment.originalPrice !== payment.paidPrice && (
                      <div className="text-sm text-muted-foreground line-through">
                        {formatCurrency(payment.originalPrice)}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={getPaymentStatusVariant(payment.status)}>
                    {getPaymentStatusDisplayName(payment.status)}
                  </Badge>
                </TableCell>
                <TableCell>
                  {payment.appliedCouponCode ? (
                    <div>
                      <div className="font-medium text-green-600">
                        {payment.appliedCouponCode}
                      </div>
                      {payment.discountAmount && (
                        <div className="text-sm text-muted-foreground">
                          -{formatCurrency(payment.discountAmount)}
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">-</span>
                  )}
                </TableCell>
                <TableCell className="text-muted-foreground">
                  {formatDate(payment.paymentDate)}
                </TableCell>
                <TableCell className="text-muted-foreground">
                  {formatValidityDate(payment.validUntil)}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewDetails(payment)}>
                        <Eye className="mr-2 h-4 w-4" />
                        Detayları Görüntüle
                      </DropdownMenuItem>
                      {payment.paymentPageUrl && (
                        <DropdownMenuItem asChild>
                          <a 
                            href={payment.paymentPageUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="flex items-center"
                          >
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Ödeme Sayfası
                          </a>
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden space-y-4 p-4">
        {payments.map((payment) => (
          <div key={payment.id} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="font-medium">{payment.product.name}</div>
                <div className="text-sm text-muted-foreground font-mono">
                  {payment.paymentId.slice(0, 12)}...
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleViewDetails(payment)}>
                    <Eye className="mr-2 h-4 w-4" />
                    Detayları Görüntüle
                  </DropdownMenuItem>
                  {payment.paymentPageUrl && (
                    <DropdownMenuItem asChild>
                      <a 
                        href={payment.paymentPageUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center"
                      >
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Ödeme Sayfası
                      </a>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant={getPaymentStatusVariant(payment.status)}>
                {getPaymentStatusDisplayName(payment.status)}
              </Badge>
              {payment.appliedCouponCode && (
                <Badge variant="outline" className="text-green-600">
                  {payment.appliedCouponCode}
                </Badge>
              )}
            </div>
            
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">Tutar:</span>
                <div className="font-medium">{formatCurrency(payment.paidPrice)}</div>
                {payment.originalPrice && payment.originalPrice !== payment.paidPrice && (
                  <div className="text-xs text-muted-foreground line-through">
                    {formatCurrency(payment.originalPrice)}
                  </div>
                )}
              </div>
              <div>
                <span className="text-muted-foreground">Geçerlilik:</span>
                <div className="font-medium">{formatValidityDate(payment.validUntil)}</div>
              </div>
            </div>
            
            <div className="text-xs text-muted-foreground">
              Ödeme: {formatDate(payment.paymentDate)}
            </div>
          </div>
        ))}
      </div>

      {/* Payment Details Dialog */}
      <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Ödeme Detayları</DialogTitle>
            <DialogDescription>
              Ödeme işlemi hakkında detaylı bilgiler
            </DialogDescription>
          </DialogHeader>
          
          {selectedPayment && (
            <ScrollArea className="max-h-[60vh] pr-4">
              <div className="space-y-6">
                {/* Payment Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Ödeme ID</label>
                    <div className="font-mono text-sm break-all">{selectedPayment.paymentId}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">İşlem ID</label>
                    <div className="font-mono text-sm">{selectedPayment.paymentTransactionId}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Sepet ID</label>
                    <div className="font-mono text-sm break-all">{selectedPayment.basketId}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Konuşma ID</label>
                    <div className="font-mono text-sm break-all">{selectedPayment.conversationId}</div>
                  </div>
                </div>

                {/* Product Info */}
                <div>
                  <h4 className="font-medium mb-2">Ürün Bilgileri</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Ürün Adı</label>
                      <div>{selectedPayment.product.name}</div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Ürün Tipi</label>
                      <div>{selectedPayment.product.type}</div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Geçerlilik Süresi</label>
                      <div>{selectedPayment.product.validityPeriodInMonths} ay</div>
                    </div>
                  </div>
                </div>

                {/* Payment Amount */}
                <div>
                  <h4 className="font-medium mb-2">Tutar Bilgileri</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Ödenen Tutar</label>
                      <div className="text-lg font-semibold">{formatCurrency(selectedPayment.paidPrice)}</div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Para Birimi</label>
                      <div>{selectedPayment.currency}</div>
                    </div>
                    {selectedPayment.originalPrice && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Orijinal Fiyat</label>
                        <div>{formatCurrency(selectedPayment.originalPrice)}</div>
                      </div>
                    )}
                    {selectedPayment.discountAmount && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">İndirim Tutarı</label>
                        <div className="text-green-600">-{formatCurrency(selectedPayment.discountAmount)}</div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Coupon Info */}
                {selectedPayment.appliedCouponCode && (
                  <div>
                    <h4 className="font-medium mb-2">Kupon Bilgileri</h4>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Kullanılan Kupon</label>
                      <div className="text-green-600 font-medium">{selectedPayment.appliedCouponCode}</div>
                    </div>
                  </div>
                )}

                {/* Status and Dates */}
                <div>
                  <h4 className="font-medium mb-2">Durum ve Tarihler</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Durum</label>
                      <div>
                        <Badge variant={getPaymentStatusVariant(selectedPayment.status)}>
                          {getPaymentStatusDisplayName(selectedPayment.status)}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Ödeme Tarihi</label>
                      <div>{formatDate(selectedPayment.paymentDate)}</div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Oluşturulma</label>
                      <div>{formatDate(selectedPayment.createdAt)}</div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Geçerlilik Sonu</label>
                      <div>{formatValidityDate(selectedPayment.validUntil)}</div>
                    </div>
                  </div>
                </div>

                {/* Error Info */}
                {(selectedPayment.errorCode || selectedPayment.errorMessage) && (
                  <div>
                    <h4 className="font-medium mb-2 text-destructive">Hata Bilgileri</h4>
                    <div className="grid grid-cols-1 gap-2">
                      {selectedPayment.errorCode && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Hata Kodu</label>
                          <div className="text-destructive">{selectedPayment.errorCode}</div>
                        </div>
                      )}
                      {selectedPayment.errorMessage && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Hata Mesajı</label>
                          <div className="text-destructive">{selectedPayment.errorMessage}</div>
                        </div>
                      )}
                      {selectedPayment.errorGroup && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Hata Grubu</label>
                          <div className="text-destructive">{selectedPayment.errorGroup}</div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Payment Page URL */}
                {selectedPayment.paymentPageUrl && (
                  <div>
                    <h4 className="font-medium mb-2">Ödeme Sayfası</h4>
                    <Button asChild variant="outline" className="w-full">
                      <a 
                        href={selectedPayment.paymentPageUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center justify-center"
                      >
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Ödeme Sayfasını Aç
                      </a>
                    </Button>
                  </div>
                )}
              </div>
            </ScrollArea>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
