'use client';

import { TrendingUp, CreditCard } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatCurrency, PaymentAnalytics } from '@/lib/schemas/payments';
import { cn } from '@/lib/utils';

interface PaymentAnalyticsSummaryProps {
  readonly analytics: PaymentAnalytics;
  readonly isLoading: boolean;
  readonly onClick: () => void;
}

export function PaymentAnalyticsSummary({ 
  analytics, 
  isLoading, 
  onClick 
}: PaymentAnalyticsSummaryProps) {
  if (isLoading) {
    return (
      <Card className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02]">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-4 w-32 bg-muted animate-pulse rounded" />
              <div className="h-8 w-24 bg-muted animate-pulse rounded" />
              <div className="h-3 w-40 bg-muted animate-pulse rounded" />
            </div>
            <div className="h-12 w-12 bg-muted animate-pulse rounded-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show empty state if no successful payments
  if (analytics.totalSuccessfulCount === 0) {
    return (
      <Card className="border-dashed">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                Başarılı Ödemeler
              </div>
              <div className="text-2xl font-bold text-muted-foreground">
                {formatCurrency(0)}
              </div>
              <div className="text-sm text-muted-foreground">
                Henüz başarılı ödeme bulunmuyor
              </div>
            </div>
            <div className="rounded-full bg-muted p-3 text-muted-foreground">
              <CreditCard className="h-6 w-6" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Button
      variant="ghost"
      className="h-auto p-0 w-full"
      onClick={onClick}
      aria-label="Ödeme analitiği detaylarını görüntüle"
    >
      <Card className={cn(
        "w-full cursor-pointer transition-all duration-200",
        "hover:shadow-lg hover:scale-[1.02] hover:border-primary/50",
        "focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
      )}>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-muted-foreground text-left">
            Başarılı Ödemeler
          </CardTitle>
          <CardDescription className="text-left">
            Toplam başarılı ödeme tutarı
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(analytics.totalSuccessfulAmount)}
              </div>
              <div className="text-sm text-muted-foreground">
                {analytics.totalSuccessfulCount} ödemeden
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                Detaylar için tıklayın
              </div>
            </div>
            <div className="rounded-full bg-green-500/10 p-3 text-green-500">
              <CreditCard className="h-6 w-6" />
            </div>
          </div>
        </CardContent>
      </Card>
    </Button>
  );
}
