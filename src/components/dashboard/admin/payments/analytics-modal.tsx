'use client';

import { useState } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { TrendingUp, Calendar, DollarSign, CreditCard } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { formatCurrency, PaymentAnalytics } from '@/lib/schemas/payments';

interface PaymentAnalyticsModalProps {
  readonly analytics: PaymentAnalytics;
  readonly isOpen: boolean;
  readonly onClose: () => void;
}

export function PaymentAnalyticsModal({ 
  analytics, 
  isOpen, 
  onClose 
}: PaymentAnalyticsModalProps) {
  const [activeTab, setActiveTab] = useState('overview');

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{label}</p>
          <p className="text-green-600">
            Tutar: {formatCurrency(data.amount)}
          </p>
          <p className="text-muted-foreground">
            Ödeme Sayısı: {data.count}
          </p>
        </div>
      );
    }
    return null;
  };

  // Prepare chart data
  const monthlyChartData = analytics.monthlyBreakdown.map(item => ({
    ...item,
    displayName: item.displayName.length > 10 
      ? item.displayName.substring(0, 10) + '...' 
      : item.displayName
  }));

  const yearlyChartData = analytics.yearlyBreakdown.map(item => ({
    ...item,
    displayName: item.year.toString()
  }));

  // Check if there's no data
  const hasData = analytics.totalSuccessfulCount > 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Ödeme Analitiği
          </DialogTitle>
          <DialogDescription>
            Başarılı ödemelerin detaylı analizi ve istatistikleri
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[75vh] pr-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
              <TabsTrigger value="monthly">Aylık Analiz</TabsTrigger>
              <TabsTrigger value="yearly">Yıllık Analiz</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6 mt-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      Toplam Tutar
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5 text-green-500" />
                      <span className="text-2xl font-bold text-green-600">
                        {formatCurrency(analytics.totalSuccessfulAmount)}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      Toplam Ödeme
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-5 w-5 text-blue-500" />
                      <span className="text-2xl font-bold text-blue-600">
                        {analytics.totalSuccessfulCount}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      Ortalama Tutar
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-purple-500" />
                      <span className="text-2xl font-bold text-purple-600">
                        {analytics.totalSuccessfulCount > 0 
                          ? formatCurrency(analytics.totalSuccessfulAmount / analytics.totalSuccessfulCount)
                          : formatCurrency(0)
                        }
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle>Son Dönem Aktivitesi</CardTitle>
                  <CardDescription>
                    Son 6 ayın ödeme performansı
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px]">
                    {hasData ? (
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={monthlyChartData.slice(-6)}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis
                            dataKey="displayName"
                            fontSize={12}
                            angle={-45}
                            textAnchor="end"
                            height={60}
                          />
                          <YAxis
                            fontSize={12}
                            tickFormatter={(value) => formatCurrency(value)}
                          />
                          <Tooltip content={<CustomTooltip />} />
                          <Line
                            type="monotone"
                            dataKey="amount"
                            stroke="#10b981"
                            strokeWidth={2}
                            dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    ) : (
                      <div className="flex items-center justify-center h-full text-muted-foreground">
                        <div className="text-center">
                          <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>Henüz ödeme verisi bulunmuyor</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="monthly" className="space-y-6 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Aylık Ödeme Analizi
                  </CardTitle>
                  <CardDescription>
                    Aylık bazda başarılı ödeme tutarları ve sayıları
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={monthlyChartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="displayName" 
                          fontSize={12}
                          angle={-45}
                          textAnchor="end"
                          height={80}
                        />
                        <YAxis 
                          fontSize={12}
                          tickFormatter={(value) => formatCurrency(value)}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Bar 
                          dataKey="amount" 
                          fill="#10b981" 
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* Monthly Summary Table */}
              <Card>
                <CardHeader>
                  <CardTitle>Aylık Özet</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {analytics.monthlyBreakdown.map((month) => (
                      <div 
                        key={month.month} 
                        className="flex items-center justify-between p-3 rounded-lg border"
                      >
                        <div>
                          <div className="font-medium">{month.displayName}</div>
                          <div className="text-sm text-muted-foreground">
                            {month.count} ödeme
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold text-green-600">
                            {formatCurrency(month.amount)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Ort: {formatCurrency(month.amount / month.count)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="yearly" className="space-y-6 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Yıllık Ödeme Analizi
                  </CardTitle>
                  <CardDescription>
                    Yıllık bazda başarılı ödeme tutarları ve sayıları
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={yearlyChartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="displayName" 
                          fontSize={12}
                        />
                        <YAxis 
                          fontSize={12}
                          tickFormatter={(value) => formatCurrency(value)}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Bar 
                          dataKey="amount" 
                          fill="#3b82f6" 
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* Yearly Summary Table */}
              <Card>
                <CardHeader>
                  <CardTitle>Yıllık Özet</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {analytics.yearlyBreakdown.map((year) => (
                      <div 
                        key={year.year} 
                        className="flex items-center justify-between p-3 rounded-lg border"
                      >
                        <div>
                          <div className="font-medium">{year.year}</div>
                          <div className="text-sm text-muted-foreground">
                            {year.count} ödeme
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold text-blue-600">
                            {formatCurrency(year.amount)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Ort: {formatCurrency(year.amount / year.count)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
