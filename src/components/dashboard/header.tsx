'use client';

import { useRouter } from 'next/navigation';
import { LogOut, Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Container } from '@/components/ui/container';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { UserAvatar } from '@/components/user/user-avatar';

import { authService } from '@/lib/api';
import { cn } from '@/lib/utils';

interface HeaderProps {
  readonly userName?: string;
  readonly userSurname?: string;
  readonly className?: string;
  readonly onToggleSidebar?: () => void;
}

export function DashboardHeader({
  userName,
  userSurname,
  className,
  onToggleSidebar
}: HeaderProps) {
  const router = useRouter();

  const handleLogout = () => {
    authService.logout();
    // Force a hard navigation to ensure the page is fully reloaded
    window.location.href = '/login';
  };

  return (
    <header className={cn(
      "sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
      className
    )}>
      <Container>
        <div className="flex h-14 items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={onToggleSidebar}
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle sidebar</span>
            </Button>
            <h1 className="text-lg font-semibold">AVAS Dashboard</h1>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <UserAvatar
                userName={userName}
                userSurname={userSurname}
                size="sm"
              />
              <span className="text-sm font-medium hidden sm:inline-block">
                {userName} {userSurname}
              </span>
            </div>
            <ThemeToggle />
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              className="gap-1"
            >
              <LogOut className="h-4 w-4" />
              <span className="hidden sm:inline-block">Logout</span>
            </Button>
          </div>
        </div>
      </Container>
    </header>
  );
}
