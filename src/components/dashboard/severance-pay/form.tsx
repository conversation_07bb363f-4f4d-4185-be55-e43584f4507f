'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { format, parse } from 'date-fns';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { severancePaySchema, type SeverancePayFormValues, type SeverancePayResponse } from '@/lib/schemas/severance-pay';
import { severancePayService } from '@/lib/api';

interface SeverancePayFormProps {
  readonly onCalculationComplete: (result: SeverancePayResponse) => void;
}

export function SeverancePayForm({ onCalculationComplete }: SeverancePayFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for date pickers
  const [startDateValue, setStartDateValue] = useState<Date | undefined>(undefined);
  const [endDateValue, setEndDateValue] = useState<Date | undefined>(undefined);

  // Initialize form with react-hook-form and zod validation
  const form = useForm<SeverancePayFormValues>({
    resolver: zodResolver(severancePaySchema),
    defaultValues: {
      startDate: '',
      endDate: '',
      grossSalary: undefined,
      cumulativeIncomeTaxBasis: undefined,
    },
  });

  // Effect to update date pickers when form values change
  useEffect(() => {
    const startDateStr = form.getValues('startDate');
    const endDateStr = form.getValues('endDate');

    if (startDateStr && !startDateValue) {
      try {
        const parsedDate = parse(startDateStr, 'yyyy-MM-dd', new Date());
        setStartDateValue(parsedDate);
      } catch (e) {
        console.error('Error parsing start date:', e);
      }
    }

    if (endDateStr && !endDateValue) {
      try {
        const parsedDate = parse(endDateStr, 'yyyy-MM-dd', new Date());
        setEndDateValue(parsedDate);
      } catch (e) {
        console.error('Error parsing end date:', e);
      }
    }
  }, [form, startDateValue, endDateValue]);

  // Handle form submission
  const onSubmit = async (values: SeverancePayFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Call API to calculate severance pay
      const result = await severancePayService.calculate(values);

      // Pass result to parent component
      onCalculationComplete(result);
    } catch (err) {
      console.error('Calculation error:', err);
      // Handle error
      setError(err instanceof Error ? err.message : 'Calculation failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kıdem Tazminatı Hesaplama</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Start Date Field */}
            <FormField
              control={form.control}
              name="startDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>İşe Başlama Tarihi</FormLabel>
                  <FormControl>
                    <DatePicker
                      date={startDateValue}
                      setDate={(date) => {
                        setStartDateValue(date);
                        field.onChange(date ? format(date, 'yyyy-MM-dd') : '');
                      }}
                      disabled={isLoading}
                      placeholder="YYYY-MM-DD"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* End Date Field */}
            <FormField
              control={form.control}
              name="endDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>İşten Ayrılma Tarihi</FormLabel>
                  <FormControl>
                    <DatePicker
                      date={endDateValue}
                      setDate={(date) => {
                        setEndDateValue(date);
                        field.onChange(date ? format(date, 'yyyy-MM-dd') : '');
                      }}
                      disabled={isLoading}
                      placeholder="YYYY-MM-DD"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Gross Salary Field */}
            <FormField
              control={form.control}
              name="grossSalary"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Brüt Maaş (TL)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="30000"
                      disabled={isLoading}
                      {...field}
                      onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(value === '' ? undefined : parseFloat(value));
                      }}
                      value={field.value ?? ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Cumulative Income Tax Basis Field */}
            <FormField
              control={form.control}
              name="cumulativeIncomeTaxBasis"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Kümülatif Gelir Vergisi Matrahı (TL)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="100000"
                      disabled={isLoading}
                      {...field}
                      onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(value === '' ? undefined : parseFloat(value));
                      }}
                      value={field.value ?? ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Error Message */}
            {error && (
              <div className="text-sm font-medium text-destructive">{error}</div>
            )}

            {/* Submit Button */}
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Hesaplanıyor...
                </>
              ) : (
                "Hesapla"
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
