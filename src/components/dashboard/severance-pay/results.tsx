import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { type SeverancePayResponse } from '@/lib/schemas/severance-pay';

interface ResultItemProps {
  readonly label: string;
  readonly value: string | number;
  readonly className?: string;
}

function ResultItem({ label, value, className }: ResultItemProps) {
  return (
    <div className={`flex justify-between py-2 ${className}`}>
      <span className="text-sm font-medium text-muted-foreground">{label}</span>
      <span className="text-sm font-semibold">{value}</span>
    </div>
  );
}

interface SeverancePayResultsProps {
  readonly result: SeverancePayResponse;
  readonly onReset: () => void;
}

export function SeverancePayResults({ result, onReset }: SeverancePayResultsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Hesa<PERSON><PERSON><PERSON> Sonuçları</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Severance Pay Section */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Kıdem Tazminatı</h3>
            <div className="space-y-1 border-b pb-3">
              <ResultItem label="Toplam Gün" value={result.totalDays} />
              <ResultItem label="Brüt Kıdem Tazminatı" value={result.grossSeverancePay} />
              <ResultItem label="Damga Vergisi" value={result.severancePayStampTax} />
              <ResultItem
                label="Net Kıdem Tazminatı"
                value={result.netSeverancePay}
                className="font-bold"
              />
            </div>
          </div>

          {/* Notice Pay Section */}
          <div>
            <h3 className="text-lg font-semibold mb-2">İhbar Tazminatı</h3>
            <div className="space-y-1 border-b pb-3">
              <ResultItem label="İhbar Süresi (Gün)" value={result.noticePeriodInDays} />
              <ResultItem label="İş Arama İzni (Saat)" value={result.jobSearchLeaveHours} />
              <ResultItem label="Brüt İhbar Tazminatı" value={result.grossNoticePay} />
              <ResultItem label="Damga Vergisi" value={result.noticePayStampTax} />
              <ResultItem label="Gelir Vergisi" value={result.noticePayIncomeTax} />
              <ResultItem
                label="Net İhbar Tazminatı"
                value={result.netNoticePay}
                className="font-bold"
              />
            </div>
          </div>

          {/* Total Compensation */}
          <div className="pt-2">
            <ResultItem
              label="Toplam Tazminat"
              value={result.totalCompensation}
              className="text-base font-bold"
            />
          </div>

          {/* Notice Text */}
          {result.severanceNoticeText && (
            <div className="mt-4 p-3 bg-muted rounded-md text-sm">
              <p>{result.severanceNoticeText}</p>
            </div>
          )}

          {/* Reset Button */}
          <Button
            onClick={onReset}
            className="w-full mt-4"
            variant="outline"
          >
            Yeni Hesaplama
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
