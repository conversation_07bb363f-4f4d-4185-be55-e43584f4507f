'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import {
  Edit,
  Trash2,
  FileSignature,
  CalendarIcon,
  Users,
  Gavel,
  CheckSquare,
  User
} from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';

import { PowerOfAttorney } from '@/lib/schemas/power-of-attorney';
import { powerOfAttorneyService } from '@/lib/api';
import { PowerOfAttorneyForm } from './form';

interface PowerOfAttorneyListProps {
  readonly powerOfAttorneys: PowerOfAttorney[];
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly onRefresh: () => void;
}

export function PowerOfAttorneyList({
  powerOfAttorneys,
  isLoading,
  error,
  onRefresh
}: PowerOfAttorneyListProps) {
  const [selectedPowerOfAttorney, setSelectedPowerOfAttorney] = useState<PowerOfAttorney | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Format date from API response
  const formatDate = (date: number[] | number | string | undefined): string => {
    if (!date) return 'Belirtilmemiş';

    try {
      if (Array.isArray(date)) {
        // Format [year, month, day]
        return format(new Date(date[0], date[1] - 1, date[2]), 'PPP', { locale: tr });
      } else if (typeof date === 'number') {
        // Format epoch time
        return format(new Date(date), 'PPP', { locale: tr });
      } else if (typeof date === 'string') {
        // Format string date (YYYY-MM-DD)
        return format(new Date(date), 'PPP', { locale: tr });
      } else {
        return 'Belirtilmemiş';
      }
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'Geçersiz tarih';
    }
  };

  // Handle edit button click
  const handleEdit = (powerOfAttorney: PowerOfAttorney) => {
    setSelectedPowerOfAttorney(powerOfAttorney);
    setEditDialogOpen(true);
  };

  // Handle delete button click
  const handleDelete = (powerOfAttorney: PowerOfAttorney) => {
    setSelectedPowerOfAttorney(powerOfAttorney);
    setDeleteDialogOpen(true);
  };

  // Handle view details click
  const handleViewDetails = (powerOfAttorney: PowerOfAttorney) => {
    setSelectedPowerOfAttorney(powerOfAttorney);
    setDetailsDialogOpen(true);
  };

  // Handle form success (edit)
  const handleFormSuccess = () => {
    setEditDialogOpen(false);
    onRefresh();
    toast.success('Vekaletname güncellendi', {
      description: 'Vekaletname başarıyla güncellendi.'
    });
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!selectedPowerOfAttorney) return;

    try {
      setIsDeleting(true);
      setDeleteError(null);

      await powerOfAttorneyService.deletePowerOfAttorney(selectedPowerOfAttorney.id);

      setDeleteDialogOpen(false);
      onRefresh();

      toast.success('Vekaletname silindi', {
        description: 'Vekaletname başarıyla silindi.'
      });
    } catch (err) {
      console.error('Delete power of attorney error:', err);
      setDeleteError(err instanceof Error ? err.message : 'Vekaletname silinirken bir hata oluştu.');
    } finally {
      setIsDeleting(false);
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-pulse text-muted-foreground">Yükleniyor...</div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="bg-destructive/15 text-destructive p-4 rounded-md">
        <p className="font-medium">Hata</p>
        <p className="text-sm">{error}</p>
        <Button
          variant="outline"
          size="sm"
          onClick={onRefresh}
          className="mt-2"
        >
          Tekrar Dene
        </Button>
      </div>
    );
  }

  // Render empty state
  if (powerOfAttorneys.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <FileSignature className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">Vekaletname bulunamadı</h3>
        <p className="text-muted-foreground mt-1">
          Henüz hiç vekaletname eklenmemiş. Yeni bir vekaletname eklemek için yukarıdaki "Yeni Vekaletname" butonunu kullanabilirsiniz.
        </p>
      </div>
    );
  }

  return (
    <>
      <ScrollArea className="h-[800px] pr-4">
        <div className="space-y-4">
          {powerOfAttorneys.map((poa) => (
            <Card
              key={poa.id}
              className="overflow-hidden cursor-pointer hover:shadow-md transition-all"
              onClick={() => handleViewDetails(poa)}
            >
              <div className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold text-lg">{poa.powerOfAttorneyNumber}</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      {poa.notaryName}
                    </p>
                    <div className="flex flex-wrap gap-2 mt-2">
                      <Badge variant="outline">
                        Dosya: {poa.caseNumber}
                      </Badge>
                      <Badge variant="outline">
                        Başlangıç: {formatDate(poa.startDate)}
                      </Badge>
                      <Badge variant="outline">
                        Bitiş: {formatDate(poa.endDate)}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex space-x-2" onClick={(e) => e.stopPropagation()}>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEdit(poa);
                      }}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      <span>Düzenle</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(poa);
                      }}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      <span>Sil</span>
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </ScrollArea>

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-auto p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle>Vekaletname Düzenle</DialogTitle>
            <DialogDescription>
              Vekaletname bilgilerini güncelleyin
            </DialogDescription>
          </DialogHeader>
          {selectedPowerOfAttorney && (
            <PowerOfAttorneyForm
              powerOfAttorney={selectedPowerOfAttorney}
              onSuccess={handleFormSuccess}
              onCancel={() => setEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Vekaletname Sil</DialogTitle>
            <DialogDescription>
              Bu vekaletnameyi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
            </DialogDescription>
          </DialogHeader>
          {deleteError && (
            <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
              {deleteError}
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              İptal
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? 'Siliniyor...' : 'Sil'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Details Dialog */}
      <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>Vekaletname Detayları</DialogTitle>
            <DialogDescription>
              {selectedPowerOfAttorney?.powerOfAttorneyNumber} - {selectedPowerOfAttorney?.notaryName}
            </DialogDescription>
          </DialogHeader>
          {selectedPowerOfAttorney && (
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="bg-muted/40 p-4 rounded-lg">
                <h3 className="text-base font-semibold mb-3 flex items-center">
                  <FileSignature className="h-4 w-4 mr-2" />
                  Temel Bilgiler
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Vekaletname No</h4>
                    <p className="text-base">{selectedPowerOfAttorney.powerOfAttorneyNumber}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Noter</h4>
                    <p className="text-base">{selectedPowerOfAttorney.notaryName}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Yevmiye No</h4>
                    <p className="text-base">{selectedPowerOfAttorney.yevmiyeNo}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Dosya No</h4>
                    <p className="text-base">{selectedPowerOfAttorney.caseNumber}</p>
                  </div>
                </div>
              </div>

              {/* Dates */}
              <div className="bg-muted/40 p-4 rounded-lg">
                <h3 className="text-base font-semibold mb-3 flex items-center">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  Tarih Bilgileri
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Başlangıç Tarihi</h4>
                    <p className="text-base">{formatDate(selectedPowerOfAttorney.startDate)}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Bitiş Tarihi</h4>
                    <p className="text-base">{formatDate(selectedPowerOfAttorney.endDate)}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Oluşturulma Tarihi</h4>
                    <p className="text-base">
                      {selectedPowerOfAttorney.createdAt
                        ? format(new Date(selectedPowerOfAttorney.createdAt * 1000), 'PPP HH:mm', { locale: tr })
                        : 'Belirtilmemiş'
                      }
                    </p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Son Güncelleme</h4>
                    <p className="text-base">
                      {selectedPowerOfAttorney.updatedAt
                        ? format(new Date(selectedPowerOfAttorney.updatedAt * 1000), 'PPP HH:mm', { locale: tr })
                        : 'Belirtilmemiş'
                      }
                    </p>
                  </div>
                </div>
              </div>

              {/* Client Names */}
              <div className="bg-muted/40 p-4 rounded-lg">
                <h3 className="text-base font-semibold mb-3 flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  Müvekkiller
                </h3>
                <div className="flex flex-wrap gap-2">
                  {selectedPowerOfAttorney.clientNameList.length > 0 ? (
                    selectedPowerOfAttorney.clientNameList.map((client, index) => (
                      <Badge key={index} variant="secondary" className="px-3 py-1 text-sm">
                        {client}
                      </Badge>
                    ))
                  ) : (
                    <p className="text-muted-foreground text-sm">Müvekkil bilgisi bulunamadı</p>
                  )}
                </div>
              </div>

              {/* Lawyer Names */}
              <div className="bg-muted/40 p-4 rounded-lg">
                <h3 className="text-base font-semibold mb-3 flex items-center">
                  <Gavel className="h-4 w-4 mr-2" />
                  Avukatlar
                </h3>
                <div className="flex flex-wrap gap-2">
                  {selectedPowerOfAttorney.lawyerList.length > 0 ? (
                    selectedPowerOfAttorney.lawyerList.map((lawyer, index) => (
                      <Badge key={index} variant="secondary" className="px-3 py-1 text-sm">
                        {lawyer}
                      </Badge>
                    ))
                  ) : (
                    <p className="text-muted-foreground text-sm">Avukat bilgisi bulunamadı</p>
                  )}
                </div>
              </div>

              {/* Powers */}
              <div className="bg-muted/40 p-4 rounded-lg">
                <h3 className="text-base font-semibold mb-3 flex items-center">
                  <CheckSquare className="h-4 w-4 mr-2" />
                  Yetkiler
                </h3>
                <div className="flex flex-wrap gap-2">
                  {selectedPowerOfAttorney.powerList.length > 0 ? (
                    selectedPowerOfAttorney.powerList.map((power, index) => (
                      <Badge key={index} variant="secondary" className="px-3 py-1 text-sm">
                        {power}
                      </Badge>
                    ))
                  ) : (
                    <p className="text-muted-foreground text-sm">Yetki bilgisi bulunamadı</p>
                  )}
                </div>
              </div>

              {/* Owner Information */}
              <div className="bg-muted/40 p-4 rounded-lg">
                <h3 className="text-base font-semibold mb-3 flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  Oluşturan Bilgileri
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Oluşturan</h4>
                    <p className="text-base">{selectedPowerOfAttorney.ownerName}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">E-posta</h4>
                    <p className="text-base">{selectedPowerOfAttorney.ownerEmail}</p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-2 pt-2">
                <Button
                  variant="outline"
                  onClick={() => handleEdit(selectedPowerOfAttorney)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Düzenle
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => {
                    setDetailsDialogOpen(false);
                    handleDelete(selectedPowerOfAttorney);
                  }}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Sil
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
