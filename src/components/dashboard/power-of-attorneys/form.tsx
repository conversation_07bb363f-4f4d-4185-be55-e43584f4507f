'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { CalendarIcon, Plus, X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';

import { powerOfAttorneySchema, type PowerOfAttorneyFormValues, type PowerOfAttorney } from '@/lib/schemas/power-of-attorney';
import { casesService, powerOfAttorneyService } from '@/lib/api';

interface PowerOfAttorneyFormProps {
  readonly powerOfAttorney?: PowerOfAttorney;
  readonly onSuccess: (powerOfAttorney: PowerOfAttorney) => void;
  readonly onCancel?: () => void;
}

export function PowerOfAttorneyForm({
  powerOfAttorney,
  onSuccess,
  onCancel
}: PowerOfAttorneyFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [startDateValue, setStartDateValue] = useState<Date | undefined>(undefined);
  const [endDateValue, setEndDateValue] = useState<Date | undefined>(undefined);
  const [caseParties, setCaseParties] = useState<Record<string, { adi: string, rol: string }[]>>({});
  const [casesLoading, setCasesLoading] = useState(false);

  // For dynamic lists
  const [clientNames, setClientNames] = useState<string[]>([]);
  const [lawyerNames, setLawyerNames] = useState<string[]>([]);
  const [powers, setPowers] = useState<string[]>([]);

  // New input values
  const [newClientName, setNewClientName] = useState('');
  const [newLawyerName, setNewLawyerName] = useState('');
  const [newPower, setNewPower] = useState('');

  const isEditing = !!powerOfAttorney;

  // Initialize form with react-hook-form and zod validation
  const form = useForm<PowerOfAttorneyFormValues>({
    resolver: zodResolver(powerOfAttorneySchema),
    defaultValues: {
      powerOfAttorneyNumber: powerOfAttorney?.powerOfAttorneyNumber ?? '',
      notaryName: powerOfAttorney?.notaryName ?? '',
      clientNameList: powerOfAttorney?.clientNameList ?? [],
      lawyerList: powerOfAttorney?.lawyerList ?? [],
      powerList: powerOfAttorney?.powerList ?? [],
      yevmiyeNo: powerOfAttorney?.yevmiyeNo ?? '',
      startDate: '', // We'll handle this with the date picker
      endDate: '', // We'll handle this with the date picker
      caseNumber: powerOfAttorney?.caseNumber ?? '',
    },
  });

  // Set up dynamic lists from existing data if editing
  useEffect(() => {
    if (powerOfAttorney) {
      setClientNames(powerOfAttorney.clientNameList || []);
      setLawyerNames(powerOfAttorney.lawyerList || []);
      setPowers(powerOfAttorney.powerList || []);

      // Set date values for the date pickers
      if (powerOfAttorney.startDate) {
        let startDate: Date;
        if (Array.isArray(powerOfAttorney.startDate)) {
          // Handle [year, month, day] format
          startDate = new Date(powerOfAttorney.startDate[0], powerOfAttorney.startDate[1] - 1, powerOfAttorney.startDate[2]);
        } else if (typeof powerOfAttorney.startDate === 'number') {
          // Handle epoch time
          startDate = new Date(powerOfAttorney.startDate);
        } else {
          // Handle string format (YYYY-MM-DD)
          startDate = new Date(powerOfAttorney.startDate);
        }
        setStartDateValue(startDate);
        // Set the form value for startDate
        form.setValue('startDate', startDate.getTime());
      }

      if (powerOfAttorney.endDate) {
        let endDate: Date;
        if (Array.isArray(powerOfAttorney.endDate)) {
          // Handle [year, month, day] format
          endDate = new Date(powerOfAttorney.endDate[0], powerOfAttorney.endDate[1] - 1, powerOfAttorney.endDate[2]);
        } else if (typeof powerOfAttorney.endDate === 'number') {
          // Handle epoch time
          endDate = new Date(powerOfAttorney.endDate);
        } else {
          // Handle string format (YYYY-MM-DD)
          endDate = new Date(powerOfAttorney.endDate);
        }
        setEndDateValue(endDate);
        // Set the form value for endDate
        form.setValue('endDate', endDate.getTime());
      }
    }
  }, [powerOfAttorney, form]);

  // Fetch case parties for the case number dropdown
  useEffect(() => {
    const fetchCaseParties = async () => {
      try {
        setCasesLoading(true);
        const casePartiesData = await casesService.getCaseParties();
        setCaseParties(casePartiesData);
      } catch (error) {
        console.error('Failed to fetch case parties:', error);
        setError('Dosya tarafları yüklenirken bir hata oluştu.');
      } finally {
        setCasesLoading(false);
      }
    };

    fetchCaseParties();
  }, []);

  // Add client name to the list
  const addClientName = () => {
    if (newClientName.trim()) {
      const updatedClientNames = [...clientNames, newClientName.trim()];
      setClientNames(updatedClientNames);
      form.setValue('clientNameList', updatedClientNames);
      setNewClientName('');
    }
  };

  // Remove client name from the list
  const removeClientName = (index: number) => {
    const updatedClientNames = clientNames.filter((_, i) => i !== index);
    setClientNames(updatedClientNames);
    form.setValue('clientNameList', updatedClientNames);
  };

  // Add lawyer name to the list
  const addLawyerName = () => {
    if (newLawyerName.trim()) {
      const updatedLawyerNames = [...lawyerNames, newLawyerName.trim()];
      setLawyerNames(updatedLawyerNames);
      form.setValue('lawyerList', updatedLawyerNames);
      setNewLawyerName('');
    }
  };

  // Remove lawyer name from the list
  const removeLawyerName = (index: number) => {
    const updatedLawyerNames = lawyerNames.filter((_, i) => i !== index);
    setLawyerNames(updatedLawyerNames);
    form.setValue('lawyerList', updatedLawyerNames);
  };

  // Add power to the list
  const addPower = () => {
    if (newPower.trim()) {
      const updatedPowers = [...powers, newPower.trim()];
      setPowers(updatedPowers);
      form.setValue('powerList', updatedPowers);
      setNewPower('');
    }
  };

  // Remove power from the list
  const removePower = (index: number) => {
    const updatedPowers = powers.filter((_, i) => i !== index);
    setPowers(updatedPowers);
    form.setValue('powerList', updatedPowers);
  };

  // Handle form submission
  const onSubmit = async (data: PowerOfAttorneyFormValues) => {
    try {
      setIsLoading(true);
      setError(null);

      // Format dates to YYYY-MM-DD string format for API request
      const formattedData = {
        ...data,
        startDate: startDateValue ? format(startDateValue, 'yyyy-MM-dd') : '',
        endDate: endDateValue ? format(endDateValue, 'yyyy-MM-dd') : ''
      };

      let result: PowerOfAttorney;

      if (isEditing && powerOfAttorney) {
        // Update existing power of attorney
        result = await powerOfAttorneyService.updatePowerOfAttorney(powerOfAttorney.id, formattedData);
      } else {
        // Create new power of attorney
        result = await powerOfAttorneyService.createPowerOfAttorney(formattedData);
      }

      onSuccess(result);
    } catch (err) {
      console.error('Power of attorney form error:', err);
      setError(err instanceof Error ? err.message : 'Bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {error && (
          <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
            {error}
          </div>
        )}

        {/* Power of Attorney Number Field */}
        <FormField
          control={form.control}
          name="powerOfAttorneyNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Vekaletname Numarası</FormLabel>
              <FormControl>
                <Input placeholder="Vekaletname numarası" {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Notary Name Field */}
        <FormField
          control={form.control}
          name="notaryName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Noter Adı</FormLabel>
              <FormControl>
                <Input placeholder="Noter adı" {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Start Date Field */}
        <FormField
          control={form.control}
          name="startDate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Başlangıç Tarihi</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                      disabled={isLoading}
                    >
                      {startDateValue ? (
                        format(startDateValue, "PPP", { locale: tr })
                      ) : (
                        <span>Tarih seçin</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={startDateValue}
                    onSelect={(date) => {
                      setStartDateValue(date);
                      field.onChange(date ? date.getTime() : '');
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* End Date Field */}
        <FormField
          control={form.control}
          name="endDate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Bitiş Tarihi</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                      disabled={isLoading}
                    >
                      {endDateValue ? (
                        format(endDateValue, "PPP", { locale: tr })
                      ) : (
                        <span>Tarih seçin</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={endDateValue}
                    onSelect={(date) => {
                      setEndDateValue(date);
                      field.onChange(date ? date.getTime() : '');
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Yevmiye No Field */}
        <FormField
          control={form.control}
          name="yevmiyeNo"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Yevmiye No</FormLabel>
              <FormControl>
                <Input placeholder="Yevmiye numarası" {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Case Number Field */}
        <FormField
          control={form.control}
          name="caseNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Dosya Numarası</FormLabel>
              <Select
                disabled={isLoading || casesLoading}
                onValueChange={field.onChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Dosya seçin" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent position="popper" className="max-h-[200px]">
                  {Object.entries(caseParties).map(([caseNumber, parties]) => (
                    <SelectItem key={caseNumber} value={caseNumber}>
                      {caseNumber} - {parties.map(party => `${party.adi} (${party.rol})`).join(', ')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Client Names List */}
        <FormField
          control={form.control}
          name="clientNameList"
          render={() => (
            <FormItem>
              <FormLabel>Müvekkil Listesi</FormLabel>
              <div className="space-y-3">
                <div className="flex flex-col sm:flex-row gap-2">
                  <Input
                    placeholder="Müvekkil adı"
                    value={newClientName}
                    onChange={(e) => setNewClientName(e.target.value)}
                    disabled={isLoading}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    onClick={addClientName}
                    disabled={isLoading || !newClientName.trim()}
                    className="w-full sm:w-auto"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Ekle
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {clientNames.length > 0 ? (
                    clientNames.map((name, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-1 bg-muted px-2 py-1 rounded-md"
                      >
                        <span className="text-sm">{name}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="h-5 w-5"
                          onClick={() => removeClientName(index)}
                          disabled={isLoading}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground">Henüz müvekkil eklenmedi</p>
                  )}
                </div>
                <FormMessage />
              </div>
            </FormItem>
          )}
        />

        {/* Lawyer Names List */}
        <FormField
          control={form.control}
          name="lawyerList"
          render={() => (
            <FormItem>
              <FormLabel>Avukat Listesi</FormLabel>
              <div className="space-y-3">
                <div className="flex flex-col sm:flex-row gap-2">
                  <Input
                    placeholder="Avukat adı"
                    value={newLawyerName}
                    onChange={(e) => setNewLawyerName(e.target.value)}
                    disabled={isLoading}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    onClick={addLawyerName}
                    disabled={isLoading || !newLawyerName.trim()}
                    className="w-full sm:w-auto"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Ekle
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {lawyerNames.length > 0 ? (
                    lawyerNames.map((name, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-1 bg-muted px-2 py-1 rounded-md"
                      >
                        <span className="text-sm">{name}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="h-5 w-5"
                          onClick={() => removeLawyerName(index)}
                          disabled={isLoading}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground">Henüz avukat eklenmedi</p>
                  )}
                </div>
                <FormMessage />
              </div>
            </FormItem>
          )}
        />

        {/* Powers List */}
        <FormField
          control={form.control}
          name="powerList"
          render={() => (
            <FormItem>
              <FormLabel>Yetkiler</FormLabel>
              <div className="space-y-3">
                <div className="flex flex-col sm:flex-row gap-2">
                  <Input
                    placeholder="Yetki"
                    value={newPower}
                    onChange={(e) => setNewPower(e.target.value)}
                    disabled={isLoading}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    onClick={addPower}
                    disabled={isLoading || !newPower.trim()}
                    className="w-full sm:w-auto"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Ekle
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {powers.length > 0 ? (
                    powers.map((power, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-1 bg-muted px-2 py-1 rounded-md"
                      >
                        <span className="text-sm">{power}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="h-5 w-5"
                          onClick={() => removePower(index)}
                          disabled={isLoading}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground">Henüz yetki eklenmedi</p>
                  )}
                </div>
                <FormMessage />
              </div>
            </FormItem>
          )}
        />

        <div className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2 pt-4">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="w-full sm:w-auto"
            >
              İptal
            </Button>
          )}
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            {isLoading ? 'Kaydediliyor...' : isEditing ? 'Güncelle' : 'Kaydet'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
