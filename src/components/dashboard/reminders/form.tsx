'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Clock, AlertCircle } from 'lucide-react';


import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { reminderSchema, type ReminderFormValues, type Reminder, ReminderPriority } from '@/lib/schemas/reminders';
import { remindersService } from '@/lib/api';

interface ReminderFormProps {
  readonly reminder?: Reminder;
  readonly onSuccess: (reminder: Reminder) => void;
  readonly onCancel?: () => void;
  readonly inDialog?: boolean;
}

export function ReminderForm({ reminder, onSuccess, onCancel, inDialog = false }: ReminderFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dueDateValue, setDueDateValue] = useState<Date | undefined>(undefined);

  const isEditing = !!reminder;

  // Initialize form with react-hook-form and zod validation
  const form = useForm<ReminderFormValues>({
    resolver: zodResolver(reminderSchema),
    defaultValues: {
      title: reminder?.title ?? '',
      description: reminder?.description ?? '',
      dueDate: reminder?.dueDate ?? '',
      repeatIntervalInHours: reminder?.repeatIntervalInHours ?? 0,
      priority: (reminder?.priority as ReminderPriority) ?? 'MEDIUM',
    },
  });

  // Effect to update date picker when form values change
  useEffect(() => {
    const dueDate = form.getValues('dueDate');

    if (dueDate && !dueDateValue) {
      try {
        // Handle both string and number formats
        const parsedDate = typeof dueDate === 'number'
          ? new Date(dueDate) // Epoch time in milliseconds
          : new Date(String(dueDate)); // ISO string

        if (!isNaN(parsedDate.getTime())) {
          setDueDateValue(parsedDate);
        }
      } catch (e) {
        console.error('Error parsing due date:', e);
      }
    }
  }, [form, dueDateValue]);

  // Handle form submission
  const onSubmit = async (values: ReminderFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      let result: Reminder;

      if (isEditing && reminder) {
        // Update existing reminder
        result = await remindersService.updateReminder(reminder.id, values);
      } else {
        // Create new reminder
        result = await remindersService.createReminder(values);
      }

      // Pass result to parent component
      onSuccess(result);
      form.reset();
    } catch (err) {
      console.error('Reminder form error:', err);
      // Handle error
      setError(err instanceof Error ? err.message : 'İşlem başarısız oldu. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };

  const formContent = (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {/* Title Field */}
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Başlık</FormLabel>
              <FormControl>
                <Input
                  placeholder="Hatırlatma başlığı"
                  disabled={isLoading}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Description Field */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Açıklama</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Hatırlatma detayları"
                  disabled={isLoading}
                  {...field}
                  value={field.value ?? ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Due Date Field */}
        <FormField
          control={form.control}
          name="dueDate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Tarih</FormLabel>
              <FormControl>
                <DatePicker
                  date={dueDateValue}
                  setDate={(date) => {
                    setDueDateValue(date);
                    // Convert to epoch time (milliseconds)
                    field.onChange(date ? date.getTime() : '');
                  }}
                  disabled={isLoading}
                  placeholder="Tarih seçin"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Repeat Interval Field */}
        <FormField
          control={form.control}
          name="repeatIntervalInHours"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tekrar Aralığı (Saat)</FormLabel>
              <FormControl>
                <div className="flex items-center">
                  <Input
                    type="number"
                    placeholder="0"
                    disabled={isLoading}
                    {...field}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value === '' ? '' : parseInt(value, 10));
                    }}
                    value={field.value ?? 1}
                  />
                  <Clock className="ml-2 h-4 w-4 text-muted-foreground" />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Priority Field */}
        <FormField
          control={form.control}
          name="priority"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Öncelik</FormLabel>
              <Select
                disabled={isLoading}
                onValueChange={field.onChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Öncelik seçin" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="LOW">Düşük</SelectItem>
                  <SelectItem value="MEDIUM">Orta</SelectItem>
                  <SelectItem value="HIGH">Yüksek</SelectItem>
                  <SelectItem value="CRITICAL">Kritik</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Error Message */}
        {error && (
          <div className="flex items-center text-sm font-medium text-destructive">
            <AlertCircle className="mr-2 h-4 w-4" />
            {error}
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-end space-x-2">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              İptal
            </Button>
          )}
          <Button type="submit" disabled={isLoading}>
            {isLoading && (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditing ? 'Güncelleniyor...' : 'Oluşturuluyor...'}
              </>
            )}
            {!isLoading && (isEditing ? 'Güncelle' : 'Oluştur')}
          </Button>
        </div>
      </form>
    </Form>
  );

  // If used in a dialog, return just the form content
  if (inDialog) {
    return formContent;
  }

  // Otherwise, wrap in a card
  return (
    <Card>
      <CardHeader>
        <CardTitle>{isEditing ? 'Hatırlatma Düzenle' : 'Yeni Hatırlatma'}</CardTitle>
      </CardHeader>
      <CardContent>
        {formContent}
      </CardContent>
    </Card>
  );
}
