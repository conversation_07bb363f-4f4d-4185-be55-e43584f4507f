'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Edit, Trash2, Clock, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { ScrollArea } from '@/components/ui/scroll-area';

import { <PERSON>minder } from '@/lib/schemas/reminders';
import { remindersService } from '@/lib/api';
import { ReminderForm } from './form';

interface ReminderListProps {
  readonly reminders: Reminder[];
  readonly isLoading: boolean;
  readonly error: string | null;
  readonly onRefresh: () => void;
}

export function ReminderList({ reminders, isLoading, error, onRefresh }: ReminderListProps) {
  const [selectedReminder, setSelectedReminder] = useState<Reminder | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Format date from epoch time
  const formatDate = (epochTime: number) => {
    try {
      const date = new Date(epochTime);
      return format(date, 'dd MMM yyyy HH:mm', { locale: tr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return String(epochTime);
    }
  };

  // Get priority badge color
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'CRITICAL':
        return <Badge variant="destructive" className="bg-red-600">Kritik</Badge>;
      case 'HIGH':
        return <Badge variant="destructive">Yüksek</Badge>;
      case 'MEDIUM':
        return <Badge variant="default">Orta</Badge>;
      case 'LOW':
        return <Badge variant="outline">Düşük</Badge>;
      default:
        return <Badge variant="secondary">{priority}</Badge>;
    }
  };

  // Handle edit button click
  const handleEdit = (reminder: Reminder) => {
    setSelectedReminder(reminder);
    setEditDialogOpen(true);
  };

  // Handle delete button click
  const handleDelete = (reminder: Reminder) => {
    setSelectedReminder(reminder);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const confirmDelete = async () => {
    if (!selectedReminder) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      await remindersService.deleteReminder(selectedReminder.id);
      setDeleteDialogOpen(false);
      onRefresh();
    } catch (error) {
      console.error('Delete error:', error);
      setDeleteError(error instanceof Error ? error.message : 'Silme işlemi başarısız oldu.');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle edit success
  const handleEditSuccess = () => {
    setEditDialogOpen(false);
    onRefresh();
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-20 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center text-center p-4">
            <AlertCircle className="h-10 w-10 text-destructive mb-2" />
            <h3 className="font-medium">Hata Oluştu</h3>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-4"
              onClick={onRefresh}
            >
              Yeniden Dene
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (reminders.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center text-center p-4">
            <Clock className="h-10 w-10 text-muted-foreground mb-2" />
            <h3 className="font-medium">Hatırlatma Bulunamadı</h3>
            <p className="text-sm text-muted-foreground mt-1">
              Henüz hiç hatırlatma oluşturmadınız.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Hatırlatmalarım</CardTitle>
          <CardDescription>
            Tüm hatırlatmalarınızın listesi
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[800px] pr-4">
            <div className="space-y-4">
              {reminders.map((reminder) => (
                <Card key={reminder.id} className="overflow-hidden">
                  <div className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold text-lg">{reminder.title}</h3>
                        {reminder.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {reminder.description}
                          </p>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        {getPriorityBadge(reminder.priority)}
                      </div>
                    </div>

                    <div className="flex justify-between items-center mt-4">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="h-4 w-4 mr-1" />
                        {formatDate(reminder.dueDate)}
                        {reminder.repeatIntervalInHours && reminder.repeatIntervalInHours > 0 && (
                          <span className="ml-2">
                            (Her {reminder.repeatIntervalInHours} saat)
                          </span>
                        )}
                      </div>

                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(reminder)}
                        >
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Düzenle</span>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(reminder)}
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Sil</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Hatırlatma Düzenle</DialogTitle>
            <DialogDescription>
              Hatırlatma bilgilerini güncelleyin
            </DialogDescription>
          </DialogHeader>
          {selectedReminder && (
            <ReminderForm
              reminder={selectedReminder}
              onSuccess={handleEditSuccess}
              onCancel={() => setEditDialogOpen(false)}
              inDialog={true}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hatırlatmayı Sil</AlertDialogTitle>
            <AlertDialogDescription>
              Bu hatırlatmayı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
            </AlertDialogDescription>
          </AlertDialogHeader>
          {deleteError && (
            <div className="flex items-center text-sm font-medium text-destructive mb-4">
              <AlertCircle className="mr-2 h-4 w-4" />
              {deleteError}
            </div>
          )}
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                confirmDelete();
              }}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Siliniyor...' : 'Sil'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
