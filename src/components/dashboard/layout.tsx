'use client';

import { useState } from 'react';
import { DashboardHeader } from './header';
import { Sidebar } from './sidebar';
import { cn } from '@/lib/utils';

interface DashboardLayoutProps {
  readonly children: React.ReactNode;
  readonly userName?: string;
  readonly userSurname?: string;
}

export function DashboardLayout({ 
  children, 
  userName, 
  userSurname 
}: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);
  const closeSidebar = () => setSidebarOpen(false);

  return (
    <div className="flex min-h-screen flex-col">
      <DashboardHeader 
        userName={userName} 
        userSurname={userSurname} 
        onToggleSidebar={toggleSidebar}
      />
      
      <div className="flex flex-1 flex-col md:flex-row">
        <Sidebar 
          isOpen={sidebarOpen} 
          onClose={closeSidebar} 
          className="h-[calc(100vh-3.5rem)]"
        />
        
        <main className={cn(
          "flex-1 overflow-auto p-4 md:p-6",
        )}>
          {children}
        </main>
      </div>
    </div>
  );
}
