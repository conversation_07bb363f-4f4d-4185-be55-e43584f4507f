'use client';

import { ReactNode } from 'react';
import { ThemeProvider } from 'next-themes';
import { Toaster } from '@/components/ui/sonner';
import { NotificationsProvider } from '@/contexts/notifications-context';

export function Providers({ children }: { readonly children: ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <NotificationsProvider>
        <Toaster />
        {children}
      </NotificationsProvider>
    </ThemeProvider>
  );
}
