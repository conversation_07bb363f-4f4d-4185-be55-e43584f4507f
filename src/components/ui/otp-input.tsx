"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"

interface OTPInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  maxLength: number
  value: string
  onChange: (value: string) => void
  disabled?: boolean
}

export function OTPInput({
  maxLength,
  value,
  onChange,
  disabled = false,
  className,
  ...props
}: OTPInputProps) {
  const inputRefs = React.useRef<(HTMLInputElement | null)[]>([])
  const [otp, setOtp] = React.useState<string[]>(
    value.split("").length ? value.split("") : Array(maxLength).fill("")
  )

  // Update the internal OTP state when the value prop changes
  React.useEffect(() => {
    const newOtp = value.split("").length ? value.split("") : Array(maxLength).fill("")
    setOtp(newOtp)
  }, [value, maxLength])

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newValue = e.target.value.slice(-1) // Take only the last character entered

    // Only allow digits
    if (newValue && !/^\d+$/.test(newValue)) {
      return
    }

    // Handle single digit input
    const newOtp = [...otp]
    newOtp[index] = newValue
    setOtp(newOtp)
    onChange(newOtp.join(""))

    // Auto-focus next input if current input is filled
    if (newValue && index < maxLength - 1) {
      // Use setTimeout to ensure the focus happens after the current execution context
      setTimeout(() => {
        inputRefs.current[index + 1]?.focus()
      }, 0)
    }
  }

  // Handle key events
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    // Handle backspace
    if (e.key === "Backspace") {
      if (!otp[index]) {
        // If current input is empty and backspace is pressed, focus previous input
        if (index > 0) {
          inputRefs.current[index - 1]?.focus()
        }
      } else {
        // If current input has a value, clear it but keep focus
        const newOtp = [...otp]
        newOtp[index] = ""
        setOtp(newOtp)
        onChange(newOtp.join(""))
      }
    }
    // Handle arrow keys
    else if (e.key === "ArrowLeft" && index > 0) {
      inputRefs.current[index - 1]?.focus()
    } else if (e.key === "ArrowRight" && index < maxLength - 1) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  // Handle focus
  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    e.target.select()
  }

  // Handle paste
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>, index: number) => {
    e.preventDefault()
    const pastedData = e.clipboardData.getData("text")

    // Only allow digits
    if (!/^\d+$/.test(pastedData)) {
      return
    }

    const pastedValue = pastedData.slice(0, maxLength)
    const newOtp = [...Array(maxLength).fill("")]

    for (let i = 0; i < pastedValue.length; i++) {
      newOtp[i] = pastedValue[i]
    }

    setOtp(newOtp)
    onChange(newOtp.join(""))

    // Focus the next empty input or the last input
    const nextIndex = Math.min(index + pastedValue.length, maxLength - 1)
    inputRefs.current[nextIndex]?.focus()
  }

  return (
    <div className={cn("flex gap-2 items-center justify-center", className)}>
      {Array.from({ length: maxLength }).map((_, index) => (
        <Input
          key={index}
          ref={(el) => {
            inputRefs.current[index] = el;
          }}
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          maxLength={1}
          value={otp[index] || ""}
          onChange={(e) => handleChange(e, index)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          onFocus={handleFocus}
          onPaste={(e) => handlePaste(e, index)}
          className="w-10 h-10 text-center text-lg font-medium"
          disabled={disabled}
          {...props}
        />
      ))}
    </div>
  )
}
