'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { notificationsService, UyapNotification, AppNotification } from '@/lib/api';

interface NotificationsContextType {
  uyapNotifications: UyapNotification[];
  appNotifications: AppNotification[];
  uyapUnreadCount: number;
  appUnreadCount: number;
  isLoading: boolean;
  error: string | null;
  refreshNotifications: () => Promise<void>;
  markUyapNotificationAsRead: (notificationId: number) => Promise<void>;
}

const NotificationsContext = createContext<NotificationsContextType | undefined>(undefined);

export function useNotifications() {
  const context = useContext(NotificationsContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationsProvider');
  }
  return context;
}

interface NotificationsProviderProps {
  children: ReactNode;
}

export function NotificationsProvider({ children }: NotificationsProviderProps) {
  const [uyapNotifications, setUyapNotifications] = useState<UyapNotification[]>([]);
  const [appNotifications, setAppNotifications] = useState<AppNotification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastFetchTime, setLastFetchTime] = useState(0);

  // Calculate unread counts
  const uyapUnreadCount = uyapNotifications.filter(n => !n.okunduMu).length;
  const appUnreadCount = appNotifications.filter(n => !n.read).length;

  // Fetch all notifications
  const refreshNotifications = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Fetch UYAP notifications
      const uyapData = await notificationsService.getUyapNotifications();
      
      // Sort notifications by date (newest first)
      const sortedUyapData = [...uyapData].sort((a, b) => {
        const dateA = new Date(a.gonderilmeTarihi).getTime();
        const dateB = new Date(b.gonderilmeTarihi).getTime();
        return dateB - dateA;
      });
      
      setUyapNotifications(sortedUyapData);
      
      // Fetch app notifications (currently returns empty array)
      const appData = await notificationsService.getAppNotifications();
      setAppNotifications(appData);
      
      setLastFetchTime(Date.now());
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
      setError('Failed to load notifications');
    } finally {
      setIsLoading(false);
    }
  };

  // Mark UYAP notification as read
  const markUyapNotificationAsRead = async (notificationId: number) => {
    try {
      await notificationsService.markUyapNotificationAsRead(notificationId);
      
      // Update local state
      setUyapNotifications(prev => 
        prev.map(notification => 
          notification.bildirimId === notificationId 
            ? { ...notification, okunduMu: true } 
            : notification
        )
      );
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  // Fetch notifications on mount and set up periodic refresh
  useEffect(() => {
    refreshNotifications();
    
    // Refresh notifications every 5 minutes
    const intervalId = setInterval(() => {
      refreshNotifications();
    }, 5 * 60 * 1000);
    
    return () => clearInterval(intervalId);
  }, []);

  const value = {
    uyapNotifications,
    appNotifications,
    uyapUnreadCount,
    appUnreadCount,
    isLoading,
    error,
    refreshNotifications,
    markUyapNotificationAsRead,
  };

  return (
    <NotificationsContext.Provider value={value}>
      {children}
    </NotificationsContext.Provider>
  );
}
