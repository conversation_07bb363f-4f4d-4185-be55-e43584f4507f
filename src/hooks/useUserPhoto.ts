'use client';

import { useState, useEffect } from 'react';
import { userPhotoService } from '@/lib/api';

// Cache for storing the user photo to avoid unnecessary API calls
const photoCache = {
  photo: null as string | null,
  timestamp: 0,
  // Cache expiration time in milliseconds (5 minutes)
  expirationTime: 5 * 60 * 1000,
};

export function useUserPhoto() {
  const [photoSrc, setPhotoSrc] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchUserPhoto = async () => {
      try {
        setIsLoading(true);
        
        // Check if we have a cached photo that's not expired
        const now = Date.now();
        if (photoCache.photo && now - photoCache.timestamp < photoCache.expirationTime) {
          setPhotoSrc(photoCache.photo);
          setIsLoading(false);
          return;
        }
        
        // Fetch photo from API
        const response = await userPhotoService.getUserPhoto();
        
        if (response.photo) {
          // Convert base64 to data URL
          const photoDataUrl = `data:image/jpeg;base64,${response.photo}`;
          
          // Update cache
          photoCache.photo = photoDataUrl;
          photoCache.timestamp = now;
          
          setPhotoSrc(photoDataUrl);
        } else {
          setPhotoSrc(null);
        }
      } catch (err) {
        console.error('Error fetching user photo:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch user photo'));
        setPhotoSrc(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserPhoto();
  }, []);

  // Function to manually refresh the photo
  const refreshPhoto = async () => {
    try {
      setIsLoading(true);
      
      // Clear cache
      photoCache.photo = null;
      photoCache.timestamp = 0;
      
      // Fetch photo from API
      const response = await userPhotoService.getUserPhoto();
      
      if (response.photo) {
        // Convert base64 to data URL
        const photoDataUrl = `data:image/jpeg;base64,${response.photo}`;
        
        // Update cache
        photoCache.photo = photoDataUrl;
        photoCache.timestamp = Date.now();
        
        setPhotoSrc(photoDataUrl);
      } else {
        setPhotoSrc(null);
      }
      
      setError(null);
    } catch (err) {
      console.error('Error refreshing user photo:', err);
      setError(err instanceof Error ? err : new Error('Failed to refresh user photo'));
    } finally {
      setIsLoading(false);
    }
  };

  return { photoSrc, isLoading, error, refreshPhoto };
}
