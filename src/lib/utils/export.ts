import * as XLSX from 'xlsx';

/**
 * Export data to Excel format
 * @param data - Array of objects to export
 * @param filename - Name of the file (without extension)
 * @param sheetName - Name of the worksheet
 * @param columnWidths - Optional array of column widths
 */
export function exportToExcel<T extends Record<string, any>>(
  data: T[],
  filename: string,
  sheetName: string = 'Sheet1',
  columnWidths?: Array<{ wch: number }>
): void {
  if (data.length === 0) {
    throw new Error('No data to export');
  }

  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet(data);

  // Set column widths if provided
  if (columnWidths) {
    worksheet['!cols'] = columnWidths;
  }

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

  // Generate filename with timestamp if not provided
  const timestamp = new Date().toISOString().split('T')[0];
  const finalFilename = filename.includes('.xlsx') 
    ? filename 
    : `${filename}-${timestamp}.xlsx`;

  // Export file
  XLSX.writeFile(workbook, finalFilename);
}

/**
 * Generate a filename with current date and time
 * @param baseName - Base name for the file
 * @param includeTime - Whether to include time in the filename
 * @returns Formatted filename
 */
export function generateExportFilename(
  baseName: string, 
  includeTime: boolean = true
): string {
  const now = new Date();
  const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
  
  if (includeTime) {
    const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, ''); // HHMMSS
    return `${baseName}-${dateStr}-${timeStr}`;
  }
  
  return `${baseName}-${dateStr}`;
}

/**
 * Format Turkish currency for export
 * @param amount - Amount to format
 * @returns Formatted currency string
 */
export function formatCurrencyForExport(amount: number): string {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

/**
 * Format date for export (Turkish locale)
 * @param epochTime - Epoch time in seconds
 * @returns Formatted date string
 */
export function formatDateForExport(epochTime: number): string {
  const date = new Date(epochTime * 1000);
  return date.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}
