export interface AuditLogResponse {
  id: number;
  endpointUrl: string;
  httpMethod: string;
  queryParameters?: string;
  pathParameters?: string;
  responseStatusCode: number;
  processingTimeMs: number;
  userEmail?: string;
  userRoles?: string;
  clientIpAddress: string;
  userAgent?: string;
  requestTimestamp: string;
  responseTimestamp: string;
  requestHeaders?: string;
  requestBodySize?: number;
  responseBodySize?: number;
  sessionId?: string;
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

export interface AuditLogStatisticsResponse {
  totalRequests: number;
  uniqueUsers: number;
  avgProcessingTime: number;
  errorCount: number;
  mostAccessedEndpoints: EndpointStatistic[];
  mostActiveUsers: UserStatistic[];
}

export interface EndpointStatistic {
  endpointUrl: string;
  accessCount: number;
}

export interface UserStatistic {
  userEmail: string;
  requestCount: number;
}

export interface AuditLogSearchParams {
  userId?: number;
  userEmail?: string;
  startDate?: string;
  endDate?: string;
  endpointUrl?: string;
  clientIp?: string;
  thresholdMs?: number;
  cutoffDate?: string;
}

// Helper functions for formatting
export const formatProcessingTime = (timeMs: number): string => {
  if (timeMs < 1000) {
    return `${timeMs}ms`;
  }
  return `${(timeMs / 1000).toFixed(2)}s`;
};

export const formatBytes = (bytes?: number): string => {
  if (!bytes) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

export const getStatusColor = (statusCode: number): string => {
  if (statusCode >= 200 && statusCode < 300) return 'text-green-600';
  if (statusCode >= 300 && statusCode < 400) return 'text-yellow-600';
  if (statusCode >= 400 && statusCode < 500) return 'text-orange-600';
  if (statusCode >= 500) return 'text-red-600';
  return 'text-gray-600';
};

export const getStatusBadgeVariant = (statusCode: number): 'default' | 'secondary' | 'destructive' | 'outline' => {
  if (statusCode >= 200 && statusCode < 300) return 'default';
  if (statusCode >= 300 && statusCode < 400) return 'secondary';
  if (statusCode >= 400) return 'destructive';
  return 'outline';
};

export const getMethodColor = (method: string): string => {
  switch (method.toUpperCase()) {
    case 'GET': return 'text-blue-600';
    case 'POST': return 'text-green-600';
    case 'PUT': return 'text-yellow-600';
    case 'PATCH': return 'text-orange-600';
    case 'DELETE': return 'text-red-600';
    default: return 'text-gray-600';
  }
};

export const parseJsonSafely = (jsonString?: string): any => {
  if (!jsonString) return null;
  try {
    return JSON.parse(jsonString);
  } catch {
    return jsonString;
  }
};
