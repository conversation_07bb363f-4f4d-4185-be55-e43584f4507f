import { z } from 'zod';

// Schema for severance pay calculation form
export const severancePaySchema = z.object({
  startDate: z
    .string()
    .min(1, { message: 'Start date is required' })
    .regex(/^\d{4}-\d{2}-\d{2}$/, { message: 'Date must be in YYYY-MM-DD format' }),
  
  endDate: z
    .string()
    .min(1, { message: 'End date is required' })
    .regex(/^\d{4}-\d{2}-\d{2}$/, { message: 'Date must be in YYYY-MM-DD format' }),
  
  grossSalary: z
    .number({ 
      required_error: 'Gross salary is required',
      invalid_type_error: 'Gross salary must be a number'
    })
    .positive({ message: 'Gross salary must be a positive number' }),
  
  cumulativeIncomeTaxBasis: z
    .number({ 
      required_error: 'Cumulative income tax basis is required',
      invalid_type_error: 'Cumulative income tax basis must be a number'
    })
    .nonnegative({ message: 'Cumulative income tax basis must be a non-negative number' }),
})
.refine(
  (data) => {
    const start = new Date(data.startDate);
    const end = new Date(data.endDate);
    return start <= end;
  },
  {
    message: 'End date must be after start date',
    path: ['endDate'],
  }
);

// Type for the form values
export type SeverancePayFormValues = z.infer<typeof severancePaySchema>;

// Type for the API response
export interface SeverancePayResponse {
  totalDays: number;
  grossSeverancePay: string;
  severancePayStampTax: string;
  netSeverancePay: string;
  noticePeriodInDays: number;
  jobSearchLeaveHours: number;
  grossNoticePay: string;
  noticePayStampTax: string;
  noticePayIncomeTax: string;
  netNoticePay: string;
  totalCompensation: string;
  severanceNoticeText: string;
}
