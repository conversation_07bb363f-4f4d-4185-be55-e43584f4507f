import { z } from 'zod';

// Schema for power of attorney form
export const powerOfAttorneySchema = z.object({
  powerOfAttorneyNumber: z
    .string()
    .min(1, { message: 'Vekaletname numarası gereklidir' })
    .max(100, { message: 'Vekaletname numarası en fazla 100 karakter olabilir' }),

  notaryName: z
    .string()
    .min(1, { message: 'Noter adı gereklidir' })
    .max(200, { message: 'Noter adı en fazla 200 karakter olabilir' }),

  clientNameList: z
    .array(z.string().min(1, { message: 'Müvekkil adı gereklidir' }))
    .min(1, { message: 'En az bir müvekkil adı gereklidir' }),

  lawyerList: z
    .array(z.string().min(1, { message: 'Avukat adı gereklidir' }))
    .min(1, { message: 'En az bir avukat adı gereklidir' }),

  powerList: z
    .array(z.string().min(1, { message: 'Yetki gereklidir' }))
    .min(1, { message: 'En az bir yetki gereklidir' }),

  yevmiyeNo: z
    .string()
    .min(1, { message: 'Yevmiye numarası gereklidir' })
    .max(50, { message: 'Yevmiye numarası en fazla 50 karakter olabilir' }),

  startDate: z
    .union([
      z.string().min(1, { message: 'Başlangıç tarihi gereklidir' }),
      z.number().int().positive({ message: 'Geçerli bir başlangıç tarihi gereklidir' })
    ]),

  endDate: z
    .union([
      z.string().min(1, { message: 'Bitiş tarihi gereklidir' }),
      z.number().int().positive({ message: 'Geçerli bir bitiş tarihi gereklidir' })
    ]),

  caseNumber: z
    .string()
    .min(1, { message: 'Dosya numarası gereklidir' }),
});

// Type for the form values
export type PowerOfAttorneyFormValues = z.infer<typeof powerOfAttorneySchema>;

// Type for the API response
export interface PowerOfAttorney {
  id: number;
  powerOfAttorneyNumber: string;
  notaryName: string;
  clientNameList: string[];
  lawyerList: string[];
  powerList: string[];
  yevmiyeNo: string;
  startDate: number[] | number | string; // API returns [year, month, day], epoch time, or string
  endDate: number[] | number | string; // API returns [year, month, day], epoch time, or string
  caseNumber: string;
  createdAt: number; // Epoch time in milliseconds
  updatedAt: number; // Epoch time in milliseconds
  ownerId: number;
  ownerName: string;
  ownerEmail: string;
}
