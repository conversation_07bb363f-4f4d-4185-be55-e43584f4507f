import { z } from 'zod';

// Schema for case note form
export const caseNoteSchema = z.object({
  content: z
    .string()
    .min(1, { message: 'Not içeriği gereklidir' })
    .max(1000, { message: 'Not içeriği en fazla 1000 karakter olabilir' }),
  caseNumber: z
    .string()
    .min(1, { message: 'Dosya numarası gereklidir' }),
});

// Type for the case note form values
export type CaseNoteFormValues = z.infer<typeof caseNoteSchema>;

// Type for the API response
export interface CaseNote {
  id: number;
  content: string;
  caseNumber: string;
  createdAt: number; // Epoch time in seconds with decimal precision
  updatedAt: number; // Epoch time in seconds with decimal precision
  ownerId: number;
  ownerName: string;
  ownerEmail: string;
}
