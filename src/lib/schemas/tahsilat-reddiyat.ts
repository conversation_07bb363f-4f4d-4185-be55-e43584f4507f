// TypeScript interfaces for Tahsilat-Reddiyat (Collections/Refunds) API response

// Collection item interface
export interface Tahsilat {
  makbuzNo: string;
  dosyaNo: string;
  dosyaTurAciklama: string;
  birimAdi: string;
  odeyenKisi: string;
  tahsilatTuru: string;
  tahsilatTarihi: string;
  hesaplamaYapilanTutar?: number;
  odenebilirMiktar?: number;
  kalanMiktar?: number;
  tahsilatTutari?: number;
  kayitId: number;
}

// Refund item interface
export interface Reddiyat {
  makbuzNo: string;
  dosyaNo: string;
  dosyaTurAciklama: string;
  durumAciklama?: string;
  miktar: number;
  birimAdi: string;
  odeyenKisi: string;
  reddiyatNedeni: string;
  reddiyatTarihi: string;
  gelirVergisi: number;
  damgaVergisi: number;
  cezaeviHarcTutari?: number;
  tahsilHarci?: number;
  odenecekMiktar: number;
  kapattigiTahsilatIDler?: string;
  kayitId?: number;
}

// Fee item interface
export interface Harc {
  makbuzNo: string;
  dosyaNo: string;
  dosyaTurAciklama: string;
  miktar?: number;
  yatirilanMiktar?: number;
  hesaplamaYapilanTutar?: number;
  odenebilirMiktar?: number;
  birimAdi: string;
  odeyenKisi: string;
  tahsilatTuru: string;
  tahsilatTarihi: string;
  kayitId?: number;
}

// Error response interface
export interface ErrorResponse {
  error: string;
  errorCode?: string;
}

// Complete API response interface
export interface TahsilatReddiyatResponse {
  tahsilatList: Tahsilat[];
  reddiyatList: Reddiyat[];
  harcList: Harc[];
  toplamTahsilat: number;
  toplamreddiyat: number;
  haricen: number;
  toplamTeminat: number;
  toplamTahsilHarci: number;
  toplamKalan: number;
  isIcraMi: boolean;
  error?: string; // For error responses with 200 status
}
