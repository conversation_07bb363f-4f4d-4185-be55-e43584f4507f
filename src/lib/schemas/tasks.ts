import { z } from 'zod';

// Define the priority enum
export const TaskPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL',
} as const;

// Create a type from the enum
export type TaskPriority = keyof typeof TaskPriority;

// Define the status enum
export const TaskStatus = {
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
} as const;

// Create a type from the enum
export type TaskStatus = keyof typeof TaskStatus;

// Define the task type enum
export const TaskType = {
  TASK: 'TASK',
  TRIAL: 'TRIAL',
  MEETING: 'MEETING',
  REMINDER: 'REMINDER',
  OTHER: 'OTHER',
} as const;

// Create a type from the enum
export type TaskType = keyof typeof TaskType;

// Schema for task form
export const taskSchema = z.object({
  title: z
    .string()
    .min(1, { message: '<PERSON>şlık gereklidir' })
    .max(100, { message: 'Başlık en fazla 100 karakter olabilir' }),

  description: z
    .string()
    .max(500, { message: 'Açıklama en fazla 500 karakter olabilir' })
    .min(1, { message: 'Açıklama gereklidir' }),

  priority: z
    .enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'], {
      errorMap: () => ({ message: 'Geçerli bir öncelik seçin' })
    }),

  startDate: z
    .union([
      z.string().min(1, { message: 'Başlangıç tarihi gereklidir' }),
      z.number().int().positive({ message: 'Geçerli bir başlangıç tarihi gereklidir' })
    ]),

  dueDate: z
    .union([
      z.string().min(1, { message: 'Bitiş tarihi gereklidir' }),
      z.number().int().positive({ message: 'Geçerli bir bitiş tarihi gereklidir' })
    ]),

  caseNumber: z
    .string()
    .optional(),

  status: z
    .enum(['OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'], {
      errorMap: () => ({ message: 'Geçerli bir durum seçin' })
    }),

  taskType: z
    .enum(['TASK', 'TRIAL', 'MEETING', 'REMINDER', 'OTHER'], {
      errorMap: () => ({ message: 'Geçerli bir görev türü seçin' })
    })
    .default('TASK')
    .optional(),
});

// Type for the form values
export type TaskFormValues = z.infer<typeof taskSchema>;

// Type for the task note
export interface TaskNote {
  id: number;
  content: string;
  createdAt: number; // Epoch time in milliseconds
  updatedAt: number; // Epoch time in milliseconds
}

// Type for the API response
export interface Task {
  id: number;
  title: string;
  description: string;
  priority: TaskPriority;
  startDate: number; // Epoch time in milliseconds
  dueDate: number; // Epoch time in milliseconds
  caseNumber?: string;
  status: TaskStatus;
  taskType?: TaskType;
  createdAt: number; // Epoch time in milliseconds
  updatedAt: number; // Epoch time in milliseconds
  reporter: number;
  notes: TaskNote[];
}

// Schema for task note form
export const taskNoteSchema = z.object({
  content: z
    .string()
    .min(1, { message: 'Not içeriği gereklidir' })
    .max(1000, { message: 'Not içeriği en fazla 1000 karakter olabilir' }),
});

// Type for the task note form values
export type TaskNoteFormValues = z.infer<typeof taskNoteSchema>;
