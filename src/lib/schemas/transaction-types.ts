import { z } from 'zod';

// Transaction type category enum based on API documentation
export const TransactionTypeCategory = z.enum(['INCOME', 'EXPENSE']);
export type TransactionTypeCategory = z.infer<typeof TransactionTypeCategory>;

// Transaction type response schema based on API documentation
export const transactionTypeResponseSchema = z.object({
  id: z.number().int().positive(),
  name: z.string(),
  category: TransactionTypeCategory,
});

export type TransactionType = z.infer<typeof transactionTypeResponseSchema>;

// Transaction type create request schema
export const transactionTypeCreateSchema = z.object({
  name: z.string().min(2, 'İşlem türü adı en az 2 karakter olmalıdır').max(255, 'İşlem türü adı en fazla 255 karakter olabilir'),
  category: TransactionTypeCategory,
});

export type TransactionTypeCreateRequest = z.infer<typeof transactionTypeCreateSchema>;

// Transaction type update request schema (same as create for this API)
export const transactionTypeUpdateSchema = transactionTypeCreateSchema;
export type TransactionTypeUpdateRequest = z.infer<typeof transactionTypeUpdateSchema>;

// Form values schema for react-hook-form
export const transactionTypeFormSchema = z.object({
  name: z.string().min(2, 'İşlem türü adı en az 2 karakter olmalıdır').max(255, 'İşlem türü adı en fazla 255 karakter olabilir'),
  category: TransactionTypeCategory,
});

export type TransactionTypeFormValues = z.infer<typeof transactionTypeFormSchema>;

// Utility functions
export function getTransactionTypeCategoryDisplayName(category: TransactionTypeCategory): string {
  switch (category) {
    case 'INCOME':
      return 'Gelir';
    case 'EXPENSE':
      return 'Gider';
    default:
      return category;
  }
}

export function transactionTypeToFormValues(transactionType: TransactionType): TransactionTypeFormValues {
  return {
    name: transactionType.name,
    category: transactionType.category,
  };
}

export function formValuesToCreateRequest(formValues: TransactionTypeFormValues): TransactionTypeCreateRequest {
  return {
    name: formValues.name.trim(),
    category: formValues.category,
  };
}

export function formValuesToUpdateRequest(formValues: TransactionTypeFormValues): TransactionTypeUpdateRequest {
  return {
    name: formValues.name.trim(),
    category: formValues.category,
  };
}

// Search and filter parameters
export interface TransactionTypeSearchParams {
  category?: TransactionTypeCategory;
  search?: string;
}
