import { z } from 'zod';

// Define the case type enum
export const CaseTypeEnum = {
  CEZA_DAVASI: 'CEZA_DAVASI',
  HUKUK_DAVASI: 'HUKUK_DAVASI',
  IDARI_DAVA: 'IDARI_DAVA',
  ICRA_TAKIBI: 'ICRA_TAKIBI',
  ARABULUCULUK: 'ARABULUCULUK',
  TAHKIM: 'TAHKIM',
  DIGER: 'DIGER',
} as const;

// Define the crime type enum
export const CrimeTypeEnum = {
  DOLANDIRICILIK: 'DOLANDIRICILIK',
  HIRSIZLIK: 'HIRSIZLIK',
  KASTEN_YARALAMA: 'KASTEN_YARALAMA',
  TAKSIRLE_YARALAMA: 'TAKSIRLE_YARALAMA',
  KASTEN_OLDURME: 'KASTEN_OLDURME',
  TAKSIRLE_OLDURME: 'TAKSIRLE_OLDURME',
  HAKARET: 'HAKARET',
  TEHDIT: 'TEHDIT',
  UYUSTURUCU: 'UYUSTURUCU',
  CINSEL_SUC: 'CINSEL_SUC',
  ZIMMET: 'ZIMMET',
  RUSVET: 'RUSVET',
  SAHTECILIK: 'SAHTECILIK',
  VERGI_SUCU: 'VERGI_SUCU',
  DIGER: 'DIGER',
} as const;

// Create type from enums
export type CaseType = keyof typeof CaseTypeEnum;
export type CrimeType = keyof typeof CrimeTypeEnum;

// Map for displaying human-readable case types
export const caseTypeDisplayMap: Record<CaseType, string> = {
  CEZA_DAVASI: 'Ceza Davası',
  HUKUK_DAVASI: 'Hukuk Davası',
  IDARI_DAVA: 'İdari Dava',
  ICRA_TAKIBI: 'İcra Takibi',
  ARABULUCULUK: 'Arabuluculuk',
  TAHKIM: 'Tahkim',
  DIGER: 'Diğer',
};

// Map for displaying human-readable crime types
export const crimeTypeDisplayMap: Record<CrimeType, string> = {
  DOLANDIRICILIK: 'Dolandırıcılık',
  HIRSIZLIK: 'Hırsızlık',
  KASTEN_YARALAMA: 'Kasten Yaralama',
  TAKSIRLE_YARALAMA: 'Taksirle Yaralama',
  KASTEN_OLDURME: 'Kasten Öldürme',
  TAKSIRLE_OLDURME: 'Taksirle Öldürme',
  HAKARET: 'Hakaret',
  TEHDIT: 'Tehdit',
  UYUSTURUCU: 'Uyuşturucu Madde Ticareti',
  CINSEL_SUC: 'Cinsel Suçlar',
  ZIMMET: 'Zimmet',
  RUSVET: 'Rüşvet',
  SAHTECILIK: 'Sahtecilik',
  VERGI_SUCU: 'Vergi Suçları',
  DIGER: 'Diğer',
};

// Schema for case details form
export const caseDetailsSchema = z.object({
  caseNumber: z
    .string()
    .min(1, { message: 'Dosya numarası gereklidir' }),

  caseType: z
    .enum(Object.keys(CaseTypeEnum) as [CaseType, ...CaseType[]], {
      errorMap: () => ({ message: 'Geçerli bir dosya türü seçin' })
    })
    .refine(val => !!val, {
      message: 'Dosya türü seçilmelidir',
    }),

  crimeType: z
    .enum(Object.keys(CrimeTypeEnum) as [CrimeType, ...CrimeType[]], {
      errorMap: () => ({ message: 'Geçerli bir suç türü seçin' })
    })
    .optional(),

  derdest: z
    .boolean(),

  caseValue: z
    .number()
    .nonnegative({ message: 'Dava değeri negatif olamaz' }),

  caseReason: z
    .string()
    .max(1000, { message: 'Dava nedeni en fazla 1000 karakter olabilir' })
    .optional(),

  caseTitle: z
    .string()
    .max(200, { message: 'Dava başlığı en fazla 200 karakter olabilir' })
    .refine(val => val !== undefined && val !== '', {
      message: 'Dava başlığı gereklidir',
    }),
});

// Type for the case details form values
export type CaseDetailsFormValues = z.infer<typeof caseDetailsSchema>;

// Type for the API response
export interface CaseDetails {
  id: number;
  caseNumber: string;
  caseType: CaseType;
  crimeType?: CrimeType;
  derdest: boolean;
  caseValue?: number;
  caseReason?: string;
  caseTitle?: string;
  createdAt: number; // Epoch time in seconds with decimal precision
  updatedAt: number; // Epoch time in seconds with decimal precision
  ownerId: number;
  ownerName: string;
  ownerEmail: string;
}
