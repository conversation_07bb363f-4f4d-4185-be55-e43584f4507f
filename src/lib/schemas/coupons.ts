import { z } from 'zod';

// Discount type enum based on API documentation
export const DiscountType = z.enum(['PERCENTAGE', 'FIXED_AMOUNT']);
export type DiscountType = z.infer<typeof DiscountType>;

// Coupon response schema based on API documentation
// Note: API documentation shows createdAt/updatedAt as date-time strings,
// but actual API returns epoch time as numbers (seconds since Unix epoch with decimal precision)
// expirationDate comes as array format: [year, month, day, hour, minute, second, nanosecond]
export const couponResponseSchema = z.object({
  id: z.number().int().positive(),
  code: z.string(),
  description: z.string().optional(),
  discountType: DiscountType,
  discountValue: z.number().positive(),
  usageLimit: z.number().int().positive(),
  currentUsageCount: z.number().int().nonnegative(),
  expirationDate: z.union([
    z.array(z.number()).length(7), // Array format: [year, month, day, hour, minute, second, nanosecond]
    z.string(), // ISO date string (fallback)
  ]).optional(),
  active: z.boolean(),
  isValid: z.boolean(),
  isExpired: z.boolean(),
  isUsageLimitExceeded: z.boolean(),
  createdAt: z.number(), // Epoch time in seconds (e.g., 1747867240.93577)
  updatedAt: z.number(), // Epoch time in seconds (e.g., 1747867240.93577)
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
});

export type Coupon = z.infer<typeof couponResponseSchema>;

// Paginated coupon response schema
export const paginatedCouponResponseSchema = z.object({
  content: z.array(couponResponseSchema),
  totalElements: z.number().int().nonnegative(),
  totalPages: z.number().int().nonnegative(),
  size: z.number().int().positive(),
  number: z.number().int().nonnegative(),
  numberOfElements: z.number().int().nonnegative(),
  first: z.boolean(),
  last: z.boolean(),
  empty: z.boolean(),
});

export type PaginatedCouponResponse = z.infer<typeof paginatedCouponResponseSchema>;

// Coupon create request schema
export const couponCreateSchema = z.object({
  code: z.string().min(3, 'Kupon kodu en az 3 karakter olmalıdır').max(50, 'Kupon kodu en fazla 50 karakter olabilir'),
  description: z.string().max(255, 'Açıklama en fazla 255 karakter olabilir').optional(),
  discountType: DiscountType,
  discountValue: z.number().min(0.01, 'İndirim değeri 0.01\'den büyük olmalıdır').max(999999.99, 'İndirim değeri çok büyük'),
  usageLimit: z.number().int().min(1, 'Kullanım limiti en az 1 olmalıdır'),
  expirationDate: z.string().optional(), // ISO date string
  active: z.boolean().default(true),
});

export type CouponCreateRequest = z.infer<typeof couponCreateSchema>;

// Coupon update request schema
export const couponUpdateSchema = z.object({
  description: z.string().max(255, 'Açıklama en fazla 255 karakter olabilir').optional(),
  discountType: DiscountType,
  discountValue: z.number().min(0.01, 'İndirim değeri 0.01\'den büyük olmalıdır').max(999999.99, 'İndirim değeri çok büyük'),
  usageLimit: z.number().int().min(1, 'Kullanım limiti en az 1 olmalıdır'),
  expirationDate: z.string().optional(), // ISO date string
  active: z.boolean(),
});

export type CouponUpdateRequest = z.infer<typeof couponUpdateSchema>;

// Form values for the coupon form component
export const couponFormSchema = z.object({
  code: z.string().min(3, 'Kupon kodu en az 3 karakter olmalıdır').max(50, 'Kupon kodu en fazla 50 karakter olabilir'),
  description: z.string().max(255, 'Açıklama en fazla 255 karakter olabilir').optional(),
  discountType: DiscountType,
  discountValue: z.string().min(1, 'İndirim değeri gereklidir').refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0.01 && num <= 999999.99;
  }, 'İndirim değeri 0.01 ile 999999.99 arasında olmalıdır'),
  usageLimit: z.string().min(1, 'Kullanım limiti gereklidir').refine((val) => {
    const num = parseInt(val);
    return !isNaN(num) && num >= 1;
  }, 'Kullanım limiti en az 1 olmalıdır'),
  expirationDate: z.string().optional(), // Date picker value
  expirationHour: z.string().optional(), // Hour (00-23)
  expirationMinute: z.string().optional(), // Minute (00-59)
  active: z.boolean(),
});

export type CouponFormValues = z.infer<typeof couponFormSchema>;

// Helper function to convert form values to API create request
export function formValuesToCreateRequest(values: CouponFormValues): CouponCreateRequest {
  let expirationDate = values.expirationDate;

  // If we have date and time components, combine them
  if (values.expirationDate && values.expirationHour && values.expirationMinute) {
    const date = new Date(values.expirationDate);
    date.setHours(parseInt(values.expirationHour), parseInt(values.expirationMinute), 0, 0);
    expirationDate = date.toISOString();
  }

  return {
    code: values.code,
    description: values.description || undefined,
    discountType: values.discountType,
    discountValue: parseFloat(values.discountValue),
    usageLimit: parseInt(values.usageLimit),
    expirationDate: expirationDate || undefined,
    active: values.active,
  };
}

// Helper function to convert form values to API update request
export function formValuesToUpdateRequest(values: CouponFormValues): CouponUpdateRequest {
  let expirationDate = values.expirationDate;

  // If we have date and time components, combine them
  if (values.expirationDate && values.expirationHour && values.expirationMinute) {
    const date = new Date(values.expirationDate);
    date.setHours(parseInt(values.expirationHour), parseInt(values.expirationMinute), 0, 0);
    expirationDate = date.toISOString();
  }

  return {
    description: values.description || undefined,
    discountType: values.discountType,
    discountValue: parseFloat(values.discountValue),
    usageLimit: parseInt(values.usageLimit),
    expirationDate: expirationDate || undefined,
    active: values.active,
  };
}

// Helper function to convert coupon to form values
export function couponToFormValues(coupon: Coupon): CouponFormValues {
  // Convert expiration date from array format to ISO string for form
  let expirationDateString = '';
  let expirationHour = '';
  let expirationMinute = '';

  if (coupon.expirationDate) {
    let date: Date;

    if (Array.isArray(coupon.expirationDate)) {
      // Handle array format: [year, month, day, hour, minute, second, nanosecond]
      const [year, month, day, hour = 23, minute = 59, second = 0] = coupon.expirationDate;
      // Note: JavaScript Date month is 0-based, but API month appears to be 1-based
      date = new Date(year, month - 1, day, hour, minute, second);
      expirationHour = hour.toString().padStart(2, '0');
      expirationMinute = minute.toString().padStart(2, '0');
    } else {
      // Handle ISO string format
      date = new Date(coupon.expirationDate);
      expirationHour = date.getHours().toString().padStart(2, '0');
      expirationMinute = date.getMinutes().toString().padStart(2, '0');
    }

    // Set time to start of day for date picker, actual time will be in separate fields
    const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    expirationDateString = dateOnly.toISOString();
  }

  return {
    code: coupon.code,
    description: coupon.description || '',
    discountType: coupon.discountType,
    discountValue: coupon.discountValue.toString(),
    usageLimit: coupon.usageLimit.toString(),
    expirationDate: expirationDateString,
    expirationHour,
    expirationMinute,
    active: coupon.active,
  };
}

// Discount type display names
export const discountTypeDisplayNames: Record<DiscountType, string> = {
  PERCENTAGE: 'Yüzde',
  FIXED_AMOUNT: 'Sabit Tutar',
};

// Helper function to get discount type display name
export function getDiscountTypeDisplayName(type: DiscountType): string {
  return discountTypeDisplayNames[type] || type;
}

// Helper function to format discount value
export function formatDiscountValue(value: number, type: DiscountType): string {
  if (type === 'PERCENTAGE') {
    return `%${value}`;
  } else {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(value);
  }
}

// Coupon search and filter parameters
export interface CouponSearchParams {
  page?: number;
  size?: number;
  sortBy?: string;
  sortDir?: 'asc' | 'desc';
  active?: boolean;
  search?: string;
}
