import { z } from 'zod';

// Admin User Report interface based on API response
export interface AdminUserReport {
  userId: number;
  createdAt: number; // epoch time with microseconds
  roles: string[];
  subscriptionLevel: string | null;
  totalPaymentAmount: number;
  newUser: boolean;
}

// Subscription level enum
export enum SubscriptionLevel {
  BASIC = 'BASIC',
  PREMIUM = 'PREMIUM',
  ENTERPRISE = 'ENTERPRISE'
}

// Helper function to format epoch time with microseconds
export function formatEpochTime(epochTime: number): string {
  // Convert from seconds to milliseconds
  const date = new Date(epochTime * 1000);
  return date.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// Helper function to format currency
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY'
  }).format(amount);
}

// Helper function to get subscription level display name
export function getSubscriptionLevelDisplayName(level: string | null): string {
  if (!level) return 'Abonelik Yok';
  
  switch (level) {
    case 'BASIC':
      return 'Temel';
    case 'PREMIUM':
      return 'Premium';
    case 'ENTERPRISE':
      return 'Kurumsal';
    default:
      return level;
  }
}

// Helper function to get subscription level color variant
export function getSubscriptionLevelVariant(level: string | null): 'default' | 'secondary' | 'destructive' | 'outline' {
  if (!level) return 'outline';
  
  switch (level) {
    case 'BASIC':
      return 'secondary';
    case 'PREMIUM':
      return 'default';
    case 'ENTERPRISE':
      return 'destructive';
    default:
      return 'outline';
  }
}

// Helper function to format roles
export function formatRoles(roles: string[]): string {
  if (roles.length === 0) return 'Kullanıcı';
  
  const roleDisplayNames: Record<string, string> = {
    'ADMIN': 'Yönetici',
    'USER': 'Kullanıcı',
    'MODERATOR': 'Moderatör'
  };
  
  return roles.map(role => roleDisplayNames[role] || role).join(', ');
}

// Helper function to get user status display
export function getUserStatusDisplay(newUser: boolean): { text: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' } {
  if (newUser) {
    return { text: 'Yeni Kullanıcı', variant: 'secondary' };
  }
  return { text: 'Aktif Kullanıcı', variant: 'default' };
}
