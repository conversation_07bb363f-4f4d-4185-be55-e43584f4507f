// Payment types and schemas

export interface Product {
  id: number;
  name: string;
  price: number;
  createdAt: number;
  updatedAt: number;
  validityPeriodInMonths: number;
  type: string;
}

export interface Payment {
  id: number;
  conversationId: string;
  price: number;
  paidPrice: number;
  currency: string;
  basketId: string;
  paymentId: string;
  paymentTransactionId: string;
  status: string;
  paymentDate: number;
  errorCode: string | null;
  errorMessage: string | null;
  errorGroup: string | null;
  paymentPageUrl: string;
  appliedCouponCode: string | null;
  originalPrice: number | null;
  discountAmount: number | null;
  product: Product;
  createdAt: number;
  updatedAt: number;
  validUntil: number;
}

// Payment status display names
export const getPaymentStatusDisplayName = (status: string): string => {
  const statusMap: Record<string, string> = {
    'SUCCESS': 'Başarılı',
    'FAILURE': 'Başarısız',
    'PENDING': 'Beklemede',
    'CANCELED': 'İptal Edildi'
  };
  
  return statusMap[status] || status;
};

// Payment status badge variant
export const getPaymentStatusVariant = (status: string): 'default' | 'secondary' | 'destructive' | 'outline' => {
  switch (status) {
    case 'SUCCESS':
      return 'default';
    case 'FAILED':
    case 'CANCELLED':
      return 'destructive';
    case 'PENDING':
      return 'secondary';
    case 'REFUNDED':
      return 'outline';
    default:
      return 'secondary';
  }
};

// Format currency
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Payment analytics types
export interface PaymentAnalytics {
  totalSuccessfulAmount: number;
  totalSuccessfulCount: number;
  monthlyBreakdown: MonthlyPaymentData[];
  yearlyBreakdown: YearlyPaymentData[];
}

export interface MonthlyPaymentData {
  month: string;
  year: number;
  amount: number;
  count: number;
  displayName: string;
}

export interface YearlyPaymentData {
  year: number;
  amount: number;
  count: number;
}

// Analytics calculation functions
export const calculatePaymentAnalytics = (payments: Payment[]): PaymentAnalytics => {
  // Filter only successful payments
  const successfulPayments = payments.filter(payment => payment.status === 'SUCCESS');

  // Calculate total successful amount and count
  const totalSuccessfulAmount = successfulPayments.reduce((sum, payment) => sum + payment.paidPrice, 0);
  const totalSuccessfulCount = successfulPayments.length;

  // Group by month and year
  const monthlyMap = new Map<string, { amount: number; count: number; year: number; month: number }>();
  const yearlyMap = new Map<number, { amount: number; count: number }>();

  successfulPayments.forEach(payment => {
    // Convert epoch time (with microseconds) to Date
    const date = new Date(payment.paymentDate * 1000);
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // getMonth() returns 0-11
    const monthKey = `${year}-${month.toString().padStart(2, '0')}`;

    // Monthly breakdown
    if (!monthlyMap.has(monthKey)) {
      monthlyMap.set(monthKey, { amount: 0, count: 0, year, month });
    }
    const monthData = monthlyMap.get(monthKey)!;
    monthData.amount += payment.paidPrice;
    monthData.count += 1;

    // Yearly breakdown
    if (!yearlyMap.has(year)) {
      yearlyMap.set(year, { amount: 0, count: 0 });
    }
    const yearData = yearlyMap.get(year)!;
    yearData.amount += payment.paidPrice;
    yearData.count += 1;
  });

  // Convert maps to arrays and sort
  const monthlyBreakdown: MonthlyPaymentData[] = Array.from(monthlyMap.entries())
    .map(([key, data]) => ({
      month: key,
      year: data.year,
      amount: data.amount,
      count: data.count,
      displayName: getMonthDisplayName(data.month, data.year),
    }))
    .sort((a, b) => a.month.localeCompare(b.month));

  const yearlyBreakdown: YearlyPaymentData[] = Array.from(yearlyMap.entries())
    .map(([year, data]) => ({
      year,
      amount: data.amount,
      count: data.count,
    }))
    .sort((a, b) => a.year - b.year);

  return {
    totalSuccessfulAmount,
    totalSuccessfulCount,
    monthlyBreakdown,
    yearlyBreakdown,
  };
};

// Helper function to get month display name
const getMonthDisplayName = (month: number, year: number): string => {
  const monthNames = [
    'Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran',
    'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'
  ];
  return `${monthNames[month - 1]} ${year}`;
};
