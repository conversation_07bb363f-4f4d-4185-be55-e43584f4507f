import { z } from 'zod';

// Define the priority enum
export const ReminderPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL',
} as const;

// Create a type from the enum
export type ReminderPriority = keyof typeof ReminderPriority;

// Schema for reminder form
export const reminderSchema = z.object({
  title: z
    .string()
    .min(1, { message: 'Başlık gereklidir' })
    .max(100, { message: 'Başlık en fazla 100 karakter olabilir' }),

  description: z
    .string()
    .max(500, { message: 'Açıklama en fazla 500 karakter olabilir' })
    .optional(),

  dueDate: z
    .union([
      z.string().min(1, { message: '<PERSON><PERSON>h gereklidir' }),
      z.number().int().positive({ message: 'Geçerli bir tarih gereklidir' })
    ]),

  repeatIntervalInHours: z
    .number({
      invalid_type_error: '<PERSON><PERSON><PERSON> aralığ<PERSON> bir sayı olmalıdır'
    })
    .int({ message: '<PERSON>kra<PERSON> aralığı tam sayı olmalıdır' })
    .nonnegative({ message: 'Tekrar aralığı negatif olamaz' })
    .optional(),

  priority: z
    .enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'], {
      errorMap: () => ({ message: 'Geçerli bir öncelik seçin' })
    }),
});

// Type for the form values
export type ReminderFormValues = z.infer<typeof reminderSchema>;

// Type for the API response
export interface Reminder {
  id: number;
  title: string;
  description?: string;
  dueDate: number; // Epoch time in milliseconds
  repeatIntervalInHours?: number;
  priority: string;
  reporterId: number;
}
