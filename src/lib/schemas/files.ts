import { number, z } from 'zod';

// File type enum based on API documentation
export const FileType = z.enum([
  'IMAGE',
  'DOCUMENT', 
  'SPREADSHEET',
  'PRESENTATION',
  'ARCHIVE',
  'VIDEO',
  'AUDIO',
  'OTHER'
]);
export type FileType = z.infer<typeof FileType>;

// File upload request schema (client-side only)
export const createFileUploadSchema = () => {
  if (typeof window === 'undefined') {
    // Server-side: return a schema without File validation
    return z.object({
      file: z.any(),
      description: z.string().max(500, 'Açıklama en fazla 500 karakter olabilir').optional(),
      tags: z.string().max(500, 'Etiketler en fazla 500 karakter olabilir').optional(),
      isPublic: z.boolean().default(false),
    });
  }
  // Client-side: return schema with File validation
  return z.object({
    file: z.instanceof(File, { message: '<PERSON><PERSON><PERSON>il<PERSON>' }),
    description: z.string().max(500, 'Açıklama en fazla 500 karakter olabilir').optional(),
    tags: z.string().max(500, 'Etiketler en fazla 500 karakter olabilir').optional(),
    isPublic: z.boolean().default(false),
  });
};

export const fileUploadSchema = createFileUploadSchema();
export type FileUploadRequest = z.infer<typeof fileUploadSchema>;

// File upload form values (for form handling)
export const fileUploadFormSchema = z.object({
  description: z.string().max(500, 'Açıklama en fazla 500 karakter olabilir').optional(),
  tags: z.string().max(500, 'Etiketler en fazla 500 karakter olabilir').optional(),
  isPublic: z.boolean(),
});

export type FileUploadFormValues = z.infer<typeof fileUploadFormSchema>;

// Uploader info schema
export const uploaderInfoSchema = z.object({
  id: z.number().int().positive(),
  fullName: z.string(),
  email: z.string().email(),
});

export type UploaderInfo = z.infer<typeof uploaderInfoSchema>;

// File response schema (detailed file information)
export const fileResponseSchema = z.object({
  responseMessage: z.string().optional(),
  timestamp: z.string().optional(),
  id: z.number().int().positive(),
  originalFilename: z.string(),
  contentType: z.string(),
  fileSize: z.number().int().positive(),
  fileType: FileType,
  description: z.string().optional(),
  tags: z.string().optional(),
  isPublic: z.boolean(),
  downloadCount: z.number().int().min(0),
  lastAccessedAt: z.number().optional(),
  md5Hash: z.string().optional(),
  uploader: uploaderInfoSchema.optional(),
  createdAt: z.number(),
  updatedAt: z.number(),
  fileExtension: z.string().optional(),
  formattedFileSize: z.string().optional(),
});

export type FileResponse = z.infer<typeof fileResponseSchema>;

// File list item schema (simplified for listing)
export const fileListItemSchema = z.object({
  id: z.number().int().positive(),
  originalFilename: z.string(),
  contentType: z.string(),
  fileSize: z.number().int().positive(),
  fileType: FileType,
  description: z.string().optional(),
  tags: z.string().optional(),
  isPublic: z.boolean(),
  downloadCount: z.number().int().min(0),
  lastAccessedAt: z.number(),
  uploaderName: z.string().optional(),
  uploaderEmail: z.string().optional(),
  createdAt: z.number(),
  updatedAt: z.number(),
  fileExtension: z.string().optional(),
  formattedFileSize: z.string().optional(),
});

export type FileListItem = z.infer<typeof fileListItemSchema>;

// File statistics schema
export const fileStatisticsSchema = z.object({
  totalFiles: z.number().int().min(0),
  totalSize: z.number().int().min(0),
  fileCountByType: z.record(z.string(), z.number().int().min(0)),
  fileCountByUploader: z.record(z.string(), z.number().int().min(0)),
});

export type FileStatistics = z.infer<typeof fileStatisticsSchema>;

// File download response schema
export const fileDownloadSchema = z.object({
  filename: z.string(),
  contentType: z.string(),
  fileSize: z.number().int().positive(),
  content: z.string(), // Base64 encoded content
  md5Hash: z.string().optional(),
  fileExtension: z.string().optional(),
  formattedFileSize: z.string().optional(),
});

export type FileDownload = z.infer<typeof fileDownloadSchema>;

// File type display names in Turkish
export const fileTypeDisplayNames: Record<FileType, string> = {
  IMAGE: 'Resim',
  DOCUMENT: 'Belge',
  SPREADSHEET: 'Elektronik Tablo',
  PRESENTATION: 'Sunum',
  ARCHIVE: 'Arşiv',
  VIDEO: 'Video',
  AUDIO: 'Ses',
  OTHER: 'Diğer',
};

// Helper function to get file type display name
export function getFileTypeDisplayName(type: FileType): string {
  return fileTypeDisplayNames[type] || type;
}

// Helper function to get file type from filename
export function getFileTypeFromFilename(filename: string): FileType {
  const extension = filename.split('.').pop()?.toLowerCase() || '';
  
  // Image files
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
    return 'IMAGE';
  }
  
  // Document files
  if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(extension)) {
    return 'DOCUMENT';
  }
  
  // Spreadsheet files
  if (['xls', 'xlsx', 'csv'].includes(extension)) {
    return 'SPREADSHEET';
  }
  
  // Presentation files
  if (['ppt', 'pptx'].includes(extension)) {
    return 'PRESENTATION';
  }
  
  // Archive files
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
    return 'ARCHIVE';
  }
  
  // Video files
  if (['mp4', 'avi', 'mov', 'wmv', 'flv'].includes(extension)) {
    return 'VIDEO';
  }
  
  // Audio files
  if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(extension)) {
    return 'AUDIO';
  }
  
  return 'OTHER';
}

// Helper function to format file size
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Helper function to get file icon based on type
export function getFileIcon(type: FileType): string {
  switch (type) {
    case 'IMAGE':
      return '🖼️';
    case 'DOCUMENT':
      return '📄';
    case 'SPREADSHEET':
      return '📊';
    case 'PRESENTATION':
      return '📽️';
    case 'ARCHIVE':
      return '🗜️';
    case 'VIDEO':
      return '🎥';
    case 'AUDIO':
      return '🎵';
    default:
      return '📁';
  }
}

// Supported file types and extensions
export const SUPPORTED_FILE_TYPES = {
  'image/*': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'text/plain': ['.txt'],
  'application/rtf': ['.rtf'],
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  'text/csv': ['.csv'],
  'application/vnd.ms-powerpoint': ['.ppt'],
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
  'video/*': ['.mp4', '.avi', '.mov', '.wmv', '.flv'],
  'audio/*': ['.mp3', '.wav', '.flac', '.aac', '.ogg'],
  'application/zip': ['.zip'],
  'application/x-rar-compressed': ['.rar'],
  'application/x-7z-compressed': ['.7z'],
  'application/x-tar': ['.tar'],
  'application/gzip': ['.gz'],
};

// Maximum file size (50MB as per API documentation)
export const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB in bytes

// Helper function to validate file
export function validateFile(file: File): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `Dosya boyutu ${formatFileSize(MAX_FILE_SIZE)} limitini aşıyor`,
    };
  }
  
  // Check file type
  const extension = '.' + file.name.split('.').pop()?.toLowerCase();
  const isSupported = Object.values(SUPPORTED_FILE_TYPES).some(extensions =>
    extensions.includes(extension)
  );
  
  if (!isSupported) {
    return {
      valid: false,
      error: 'Desteklenmeyen dosya türü',
    };
  }
  
  return { valid: true };
}
