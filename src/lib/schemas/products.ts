import { z } from 'zod';

// Product type enum based on API documentation
export const ProductType = z.enum(['BASIC']);
export type ProductType = z.infer<typeof ProductType>;

// Product response schema based on API documentation
// Note: API documentation shows createdAt/updatedAt as date-time strings,
// but actual API returns epoch time as numbers (seconds since Unix epoch with decimal precision)
export const productResponseSchema = z.object({
  id: z.number().int().positive(),
  name: z.string(),
  price: z.number().positive(),
  createdAt: z.number(), // Epoch time in seconds (e.g., 1747867240.93577)
  updatedAt: z.number(), // Epoch time in seconds (e.g., 1747867240.93577)
  validityPeriodInMonths: z.number().int().min(1).max(12),
  type: ProductType,
});

export type Product = z.infer<typeof productResponseSchema>;

// Product create request schema
export const productCreateSchema = z.object({
  name: z.string().min(1, 'Ürün adı gereklidir'),
  price: z.number().min(0.01, 'Fiyat 0.01\'den büyük olmalıdır'),
  validityPeriodInMonths: z.number().int().min(1, 'Geçerlilik süresi en az 1 ay olmalıdır').max(12, 'Geçerlilik süresi en fazla 12 ay olabilir'),
  type: ProductType,
});

export type ProductCreateRequest = z.infer<typeof productCreateSchema>;

// Product update request schema (same as create for this API)
export const productUpdateSchema = productCreateSchema;
export type ProductUpdateRequest = z.infer<typeof productUpdateSchema>;

// Form values for the product form component
export const productFormSchema = z.object({
  name: z.string().min(1, 'Ürün adı gereklidir'),
  price: z.string().min(1, 'Fiyat gereklidir').refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0.01;
  }, 'Fiyat 0.01\'den büyük olmalıdır'),
  validityPeriodInMonths: z.string().min(1, 'Geçerlilik süresi gereklidir').refine((val) => {
    const num = parseInt(val);
    return !isNaN(num) && num >= 1 && num <= 12;
  }, 'Geçerlilik süresi 1-12 ay arasında olmalıdır'),
  type: ProductType,
});

export type ProductFormValues = z.infer<typeof productFormSchema>;

// Helper function to convert form values to API request
export function formValuesToCreateRequest(values: ProductFormValues): ProductCreateRequest {
  return {
    name: values.name,
    price: parseFloat(values.price),
    validityPeriodInMonths: parseInt(values.validityPeriodInMonths),
    type: values.type,
  };
}

export function formValuesToUpdateRequest(values: ProductFormValues): ProductUpdateRequest {
  return formValuesToCreateRequest(values);
}

// Helper function to convert product to form values
export function productToFormValues(product: Product): ProductFormValues {
  return {
    name: product.name,
    price: product.price.toString(),
    validityPeriodInMonths: product.validityPeriodInMonths.toString(),
    type: product.type,
  };
}

// Product type display names
export const productTypeDisplayNames: Record<ProductType, string> = {
  BASIC: 'Temel',
};

// Helper function to get product type display name
export function getProductTypeDisplayName(type: ProductType): string {
  return productTypeDisplayNames[type] || type;
}
