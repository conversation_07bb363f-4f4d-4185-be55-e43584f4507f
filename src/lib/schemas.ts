'use client';

import * as z from 'zod';

// Login form validation schema
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Email is required' })
    .email({ message: 'Please enter a valid email address' }),
  password: z
    .string()
    .min(1, { message: 'Password is required' })
    .min(6, { message: 'Password must be at least 6 characters' }),
  rememberMe: z.boolean().default(false),
});

export type LoginFormValues = z.infer<typeof loginSchema>;

// Sign-up form validation schema
export const signupSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Email is required' })
    .email({ message: 'Please enter a valid email address' }),
  password: z
    .string()
    .min(1, { message: 'Password is required' })
    .min(8, { message: 'Password must be at least 8 characters' }),
  name: z
    .string()
    .min(1, { message: 'Name is required' }),
  surname: z
    .string()
    .min(1, { message: 'Surname is required' }),
  personInfoText: z
    .string()
    .optional(),
  identityNumber: z
    .string()
    .min(11, { message: 'Identity number must be 11 digits' })
    .max(11, { message: 'Identity number must be 11 digits' })
    .regex(/^\d+$/, { message: 'Identity number must contain only digits' }),
  birthDate: z
    .date({ required_error: 'Birth date is required' }),
  mobilePhone: z
    .string()
    .min(1, { message: 'Mobile phone is required' })
    .regex(/^\d+$/, { message: 'Mobile phone must contain only digits' }),
});

export type SignupFormValues = z.infer<typeof signupSchema>;

// OTP verification schema
export const otpVerificationSchema = z.object({
  otp: z
    .string()
    .length(8, { message: 'OTP must be 8 digits' })
    .regex(/^\d+$/, { message: 'OTP must contain only digits' }),
  email: z
    .string()
    .email({ message: 'Please enter a valid email address' }),
});

export type OtpVerificationValues = z.infer<typeof otpVerificationSchema>;

// Forgot password email schema
export const forgotPasswordEmailSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Email is required' })
    .email({ message: 'Please enter a valid email address' }),
});

export type ForgotPasswordEmailValues = z.infer<typeof forgotPasswordEmailSchema>;

// Forgot password OTP verification schema
export const forgotPasswordOtpSchema = z.object({
  otpValue: z
    .string()
    .min(1, { message: 'OTP is required' })
    .regex(/^\d+$/, { message: 'OTP must contain only digits' }),
  otpEmail: z
    .string()
    .email({ message: 'Please enter a valid email address' }),
});

export type ForgotPasswordOtpValues = z.infer<typeof forgotPasswordOtpSchema>;

// Forgot password update password schema
export const forgotPasswordUpdateSchema = z.object({
  password: z
    .string()
    .min(1, { message: 'Password is required' })
    .min(8, { message: 'Password must be at least 8 characters' }),
  validatedOtp: z.object({
    otpValue: z.string(),
    otpEmail: z.string().email(),
  }),
});

export type ForgotPasswordUpdateValues = z.infer<typeof forgotPasswordUpdateSchema>;
