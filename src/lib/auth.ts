'use client';

import { jwtVerify } from 'jose';
import { setCookie, deleteCookie, getCookie } from 'cookies-next';
import type { LoginResponse } from './api';

// JWT token handling
export const setAuthToken = (token: string, rememberMe: boolean = false): void => {
  // Store in localStorage for client-side access
  if (typeof window !== 'undefined') {
    localStorage.setItem('auth-token', token);
  }

  // Also store in cookies for client-side access
  const maxAge = rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60; // 30 days or 1 day
  setCookie('auth-token', token, {
    maxAge,
    path: '/',
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  });
};

export const removeAuthToken = (): void => {
  // Remove from localStorage
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth-token');
  }

  // Remove from cookies
  deleteCookie('auth-token');
};

export const getAuthToken = (): string | null => {
  // Try to get from localStorage first (client-side)
  if (typeof window !== 'undefined') {
    return localStorage.getItem('auth-token');
  }

  // For client-side, get from cookies
  return getCookie('auth-token') as string || null;
};

// Store user data from login response
export const setUserData = (userData: Omit<LoginResponse, 'jwt'>): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('user-data', JSON.stringify(userData));
  }
};

// Get stored user data
export const getUserData = (): Partial<LoginResponse> | null => {
  if (typeof window !== 'undefined') {
    const userData = localStorage.getItem('user-data');
    return userData ? JSON.parse(userData) : null;
  }
  return null;
};

// Verify JWT token
export const verifyToken = async (token: string): Promise<boolean> => {
  try {
    // This is a simple verification that the token is properly formatted
    // In a real app, you would verify with a proper secret key
    const encoder = new TextEncoder();
    const secretKey = encoder.encode('your-secret-key');

    await jwtVerify(token, secretKey, {
      algorithms: ['HS256'],
    });

    return true;
  } catch (error) {
    console.error('Token verification failed:', error);
    return false;
  }
};

// Handle login success
export const handleLoginSuccess = (
  response: LoginResponse,
  rememberMe: boolean = false
): void => {
  // Store the JWT token
  setAuthToken(response.jwt, rememberMe);

  // Store user data without the JWT
  const { jwt, ...userData } = response;
  setUserData(userData);
};
