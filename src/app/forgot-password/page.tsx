'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff, Loader2, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

// Import components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Toaster } from '@/components/ui/sonner';
import { toast } from 'sonner';

// Import validation schema and API services
import {
  forgotPasswordEmailSchema,
  forgotPasswordOtpSchema,
  forgotPasswordUpdateSchema,
  type ForgotPasswordEmailValues,
  type ForgotPasswordOtpValues,
  type ForgotPasswordUpdateValues,
} from '@/lib/schemas';
import { authService } from '@/lib/api';

// Define steps for the forgot password flow
enum ForgotPasswordStep {
  EMAIL_FORM,
  OTP_VERIFICATION,
  PASSWORD_UPDATE,
  SUCCESS,
}

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<ForgotPasswordStep>(ForgotPasswordStep.EMAIL_FORM);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [email, setEmail] = useState<string>('');
  const [validatedOtp, setValidatedOtp] = useState<{ otpValue: string; otpEmail: string } | null>(null);

  // Initialize email form
  const emailForm = useForm<ForgotPasswordEmailValues>({
    resolver: zodResolver(forgotPasswordEmailSchema),
    defaultValues: {
      email: '',
    },
  });

  // Initialize OTP verification form
  const otpForm = useForm<ForgotPasswordOtpValues>({
    resolver: zodResolver(forgotPasswordOtpSchema),
    defaultValues: {
      otpValue: '',
      otpEmail: '',
    },
  });

  // Initialize password update form
  const passwordForm = useForm<ForgotPasswordUpdateValues>({
    resolver: zodResolver(forgotPasswordUpdateSchema),
    defaultValues: {
      password: '',
      validatedOtp: {
        otpValue: '',
        otpEmail: '',
      },
    },
  });

  // Handle email form submission
  const onEmailSubmit = async (values: ForgotPasswordEmailValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Call forgot password send OTP API
      const response = await authService.forgotPasswordSendOtp({
        email: values.email,
      });

      // Store the email for OTP verification
      setEmail(values.email);

      // Move to OTP verification step
      setCurrentStep(ForgotPasswordStep.OTP_VERIFICATION);

      // Set the email in the OTP form
      otpForm.setValue('otpEmail', values.email);

      // Show success toast
      toast.success(response.responseMessage || 'Verification code sent. Please check your email.');
    } catch (err) {
      console.error('Forgot password error:', err);
      // Handle error
      setError(err instanceof Error ? err.message : 'Failed to send verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP verification form submission
  const onOtpSubmit = async (values: ForgotPasswordOtpValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Call validate OTP API
      const response = await authService.forgotPasswordValidateOtp({
        otpValue: values.otpValue,
        otpEmail: values.otpEmail,
      });

      if (response.validated) {
        // Store validated OTP
        setValidatedOtp({
          otpValue: values.otpValue,
          otpEmail: values.otpEmail,
        });

        // Set validated OTP in password form
        passwordForm.setValue('validatedOtp', {
          otpValue: values.otpValue,
          otpEmail: values.otpEmail,
        });

        // Move to password update step
        setCurrentStep(ForgotPasswordStep.PASSWORD_UPDATE);

        // Show success toast
        toast.success(response.responseMessage || 'OTP verified successfully. Please set a new password.');
      } else {
        setError('OTP verification failed. Please try again.');
      }
    } catch (err) {
      console.error('OTP verification error:', err);
      // Handle verification error
      setError(err instanceof Error ? err.message : 'Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle password update form submission
  const onPasswordSubmit = async (values: ForgotPasswordUpdateValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Call update password API
      const response = await authService.forgotPasswordUpdatePassword({
        password: values.password,
        validatedOtp: values.validatedOtp,
      });

      if (response.updated) {
        // Move to success step
        setCurrentStep(ForgotPasswordStep.SUCCESS);

        // Show success toast
        toast.success(response.responseMessage || 'Password updated successfully!');
      } else {
        setError('Password update failed. Please try again.');
      }
    } catch (err) {
      console.error('Password update error:', err);
      // Handle update error
      setError(err instanceof Error ? err.message : 'Password update failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle go to login
  const handleGoToLogin = () => {
    router.push('/login');
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-muted/40 px-4 py-12">
      <Toaster />
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">
            {currentStep === ForgotPasswordStep.EMAIL_FORM && "Forgot Password"}
            {currentStep === ForgotPasswordStep.OTP_VERIFICATION && "Verify Your Email"}
            {currentStep === ForgotPasswordStep.PASSWORD_UPDATE && "Set New Password"}
            {currentStep === ForgotPasswordStep.SUCCESS && "Password Reset Complete"}
          </CardTitle>
          <CardDescription className="text-center">
            {currentStep === ForgotPasswordStep.EMAIL_FORM && "Enter your email to receive a verification code"}
            {currentStep === ForgotPasswordStep.OTP_VERIFICATION && "Enter the verification code sent to your email"}
            {currentStep === ForgotPasswordStep.PASSWORD_UPDATE && "Enter your new password"}
            {currentStep === ForgotPasswordStep.SUCCESS && "Your password has been reset successfully"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {currentStep === ForgotPasswordStep.EMAIL_FORM && (
            <Form {...emailForm}>
              <form onSubmit={emailForm.handleSubmit(onEmailSubmit)} className="space-y-4">
                <FormField
                  control={emailForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          type="email"
                          autoComplete="email"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Error Message */}
                {error && (
                  <div className="text-sm font-medium text-destructive text-center">
                    {error}
                  </div>
                )}

                {/* Submit Button */}
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    "Send Verification Code"
                  )}
                </Button>
              </form>
            </Form>
          )}

          {currentStep === ForgotPasswordStep.OTP_VERIFICATION && (
            <Form {...otpForm}>
              <form onSubmit={otpForm.handleSubmit(onOtpSubmit)} className="space-y-4">
                <div className="text-sm text-muted-foreground mb-4">
                  We've sent a verification code to <span className="font-medium">{email}</span>.
                  Please enter the code below to continue.
                </div>

                <FormField
                  control={otpForm.control}
                  name="otpValue"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Verification Code</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter verification code"
                          type="text"
                          inputMode="numeric"
                          pattern="[0-9]*"
                          className="text-center text-lg font-medium tracking-widest"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Error Message */}
                {error && (
                  <div className="text-sm font-medium text-destructive text-center">
                    {error}
                  </div>
                )}

                {/* Submit Button */}
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    "Verify"
                  )}
                </Button>

                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => setCurrentStep(ForgotPasswordStep.EMAIL_FORM)}
                  disabled={isLoading}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
              </form>
            </Form>
          )}

          {currentStep === ForgotPasswordStep.PASSWORD_UPDATE && (
            <Form {...passwordForm}>
              <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-4">
                <FormField
                  control={passwordForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            placeholder="Enter new password"
                            type={showPassword ? "text" : "password"}
                            autoComplete="new-password"
                            disabled={isLoading}
                            {...field}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3"
                            onClick={() => setShowPassword(!showPassword)}
                            disabled={isLoading}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                            <span className="sr-only">
                              {showPassword ? "Hide password" : "Show password"}
                            </span>
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Error Message */}
                {error && (
                  <div className="text-sm font-medium text-destructive text-center">
                    {error}
                  </div>
                )}

                {/* Submit Button */}
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    "Update Password"
                  )}
                </Button>

                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => setCurrentStep(ForgotPasswordStep.OTP_VERIFICATION)}
                  disabled={isLoading}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
              </form>
            </Form>
          )}

          {currentStep === ForgotPasswordStep.SUCCESS && (
            <div className="flex flex-col items-center space-y-4">
              <div className="text-center text-muted-foreground">
                Your password has been reset successfully. You can now log in with your new password.
              </div>
              <Button onClick={handleGoToLogin} className="w-full">
                Go to Login
              </Button>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          {currentStep === ForgotPasswordStep.EMAIL_FORM && (
            <div className="text-sm text-center text-muted-foreground">
              Remember your password?{" "}
              <Link href="/login" className="underline underline-offset-4 hover:text-primary">
                Back to Login
              </Link>
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
