'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';

// Import components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { DatePicker } from '@/components/ui/date-picker';
import { Toaster } from '@/components/ui/sonner';
import { toast } from 'sonner';

// Import validation schema and API services
import { signupSchema, otpVerificationSchema, type SignupFormValues, type OtpVerificationValues } from '@/lib/schemas';
import { authService, type SignupRequest } from '@/lib/api';

// Define the steps in the sign-up process
enum SignupStep {
  FORM,
  OTP_VERIFICATION,
  SUCCESS
}

export default function RegisterPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<SignupStep>(SignupStep.FORM);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [registeredEmail, setRegisteredEmail] = useState<string>('');

  // Initialize form with react-hook-form and zod validation
  const signupForm = useForm<SignupFormValues>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      email: '',
      password: '',
      name: '',
      surname: '',
      personInfoText: '',
      identityNumber: '',
      birthDate: undefined,
      mobilePhone: '',
    },
  });

  // Initialize OTP verification form
  const otpForm = useForm<OtpVerificationValues>({
    resolver: zodResolver(otpVerificationSchema),
    defaultValues: {
      otp: '',
      email: '',
    },
  });

  // Handle sign-up form submission
  const onSignupSubmit = async (values: SignupFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Format the date to YYYY-MM-DD string format
      const formattedDate = format(values.birthDate, 'yyyy-MM-dd');

      // Prepare the request data
      const signupData: SignupRequest = {
        email: values.email,
        password: values.password,
        name: values.name,
        surname: values.surname,
        personInfoText: values.personInfoText || undefined,
        identityNumber: values.identityNumber,
        birthDate: formattedDate,
        mobilePhone: values.mobilePhone,
      };

      // Call signup API
      const response = await authService.signup(signupData);

      // Store the email for OTP verification
      setRegisteredEmail(values.email);

      // Move to OTP verification step
      setCurrentStep(SignupStep.OTP_VERIFICATION);

      // Set the email in the OTP form
      otpForm.setValue('email', values.email);

      // Show success toast
      toast.success(response.responseMessage || 'Verification email sent. Please check your email.');
    } catch (err) {
      console.error('Signup error:', err);
      // Handle signup error
      setError(err instanceof Error ? err.message : 'Signup failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP verification form submission
  const onOtpSubmit = async (values: OtpVerificationValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Call verify email API
      const response = await authService.verifyEmail({
        otp: values.otp,
        email: values.email,
      });

      if (response.verified) {
        // Move to success step
        setCurrentStep(SignupStep.SUCCESS);

        // Show success toast
        toast.success(response.responseMessage || 'Registration completed successfully!');
      } else {
        setError('Email verification failed. Please try again.');
      }
    } catch (err) {
      console.error('OTP verification error:', err);
      // Handle verification error
      setError(err instanceof Error ? err.message : 'Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle redirect to login page
  const handleGoToLogin = () => {
    router.push('/login');
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-muted/40 px-4 py-12">
      <Toaster />
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">
            {currentStep === SignupStep.FORM && "Create an Account"}
            {currentStep === SignupStep.OTP_VERIFICATION && "Verify Your Email"}
            {currentStep === SignupStep.SUCCESS && "Registration Complete"}
          </CardTitle>
          <CardDescription className="text-center">
            {currentStep === SignupStep.FORM && "Enter your details to create a new account"}
            {currentStep === SignupStep.OTP_VERIFICATION && "Enter the 8-digit code sent to your email"}
            {currentStep === SignupStep.SUCCESS && "Your account has been created successfully"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {currentStep === SignupStep.FORM && (
            <Form {...signupForm}>
              <form onSubmit={signupForm.handleSubmit(onSignupSubmit)} className="space-y-4">
                {/* Email Field */}
                <FormField
                  control={signupForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          type="email"
                          autoComplete="email"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Password Field */}
                <FormField
                  control={signupForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            placeholder="••••••••"
                            type={showPassword ? "text" : "password"}
                            autoComplete="new-password"
                            disabled={isLoading}
                            {...field}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowPassword(!showPassword)}
                            disabled={isLoading}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <Eye className="h-4 w-4 text-muted-foreground" />
                            )}
                            <span className="sr-only">
                              {showPassword ? "Hide password" : "Show password"}
                            </span>
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Name Field */}
                <FormField
                  control={signupForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="John"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Surname Field */}
                <FormField
                  control={signupForm.control}
                  name="surname"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Surname</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Doe"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Person Info Text Field */}
                <FormField
                  control={signupForm.control}
                  name="personInfoText"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Information (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Additional information"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Identity Number Field */}
                <FormField
                  control={signupForm.control}
                  name="identityNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Identity Number</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="12345678901"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Birth Date Field */}
                <FormField
                  control={signupForm.control}
                  name="birthDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Birth Date</FormLabel>
                      <DatePicker
                        date={field.value}
                        setDate={field.onChange}
                        disabled={isLoading}
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Mobile Phone Field */}
                <FormField
                  control={signupForm.control}
                  name="mobilePhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Mobile Phone</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="5551234567"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Error Message */}
                {error && (
                  <div className="text-sm font-medium text-destructive text-center">
                    {error}
                  </div>
                )}

                {/* Submit Button */}
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Signing up...
                    </>
                  ) : (
                    "Sign Up"
                  )}
                </Button>
              </form>
            </Form>
          )}

          {currentStep === SignupStep.OTP_VERIFICATION && (
            <Form {...otpForm}>
              <form onSubmit={otpForm.handleSubmit(onOtpSubmit)} className="space-y-4">
                <div className="text-sm text-muted-foreground mb-4">
                  We've sent an 8-digit verification code to <span className="font-medium">{registeredEmail}</span>.
                  Please enter the code below to complete your registration.
                </div>

                <FormField
                  control={otpForm.control}
                  name="otp"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Verification Code</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter 8-digit code"
                          type="text"
                          inputMode="numeric"
                          pattern="[0-9]*"
                          maxLength={8}
                          className="text-center text-lg font-medium tracking-widest"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Error Message */}
                {error && (
                  <div className="text-sm font-medium text-destructive text-center">
                    {error}
                  </div>
                )}

                {/* Submit Button */}
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    "Verify"
                  )}
                </Button>
              </form>
            </Form>
          )}

          {currentStep === SignupStep.SUCCESS && (
            <div className="flex flex-col items-center space-y-4">
              <div className="text-center text-muted-foreground">
                Your account has been created successfully. You can now log in with your credentials.
              </div>
              <Button onClick={handleGoToLogin} className="w-full">
                Go to Login
              </Button>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-sm text-center text-muted-foreground">
            Already have an account?{" "}
            <Link href="/login" className="underline underline-offset-4 hover:text-primary">
              Sign in
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
