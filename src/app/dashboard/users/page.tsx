'use client';

import { useEffect, useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Users, Search, User, Building } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import { getUserData } from '@/lib/auth';
import { authService, casesService, type CasePartiesResponse } from '@/lib/api';
import { DashboardLayout } from '@/components/dashboard/layout';

// Type for a user with case number
interface UserWithCase {
  caseNumber: string;
  name: string;
  role: string;
  type: string;
  attorney?: string;
}

export default function UsersPage() {
  const router = useRouter();
  const [userData, setUserData] = useState<any>(null);
  const [caseParties, setCaseParties] = useState<CasePartiesResponse>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [selectedUser, setSelectedUser] = useState<UserWithCase | null>(null);
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);

  const itemsPerPage = 10;

  // Fetch case parties data
  const fetchCaseParties = async () => {
    try {
      setLoading(true);
      const data = await casesService.getCaseParties();
      setCaseParties(data);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch case parties:', err);
      setError('Kullanıcı bilgileri yüklenirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Check if user is authenticated
    if (!authService.isAuthenticated()) {
      router.push('/login');
      return;
    }

    // Get user data from local storage
    const data = getUserData();
    setUserData(data);

    // Fetch case parties
    fetchCaseParties();
  }, [router]);

  // Transform case parties data into a flat list of users
  const allUsers = useMemo(() => {
    const users: UserWithCase[] = [];

    Object.entries(caseParties).forEach(([caseNumber, parties]) => {
      parties.forEach(party => {
        users.push({
          caseNumber,
          name: party.adi,
          role: party.rol,
          type: party.kisiKurum,
          attorney: party.vekil
        });
      });
    });

    return users;
  }, [caseParties]);

  // Filter users based on search term
  const filteredUsers = useMemo(() => {
    if (!searchTerm.trim()) return allUsers;

    const term = searchTerm.toLowerCase();
    return allUsers.filter(user =>
      user.name.toLowerCase().includes(term) ||
      user.role.toLowerCase().includes(term) ||
      user.caseNumber.toLowerCase().includes(term)
    );
  }, [allUsers, searchTerm]);

  // Paginate users
  const paginatedUsers = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredUsers.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredUsers, currentPage, itemsPerPage]);

  // Calculate total pages
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages is less than or equal to max visible pages
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      // Calculate start and end of middle pages
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);

      // Adjust if we're at the start or end
      if (currentPage <= 2) {
        endPage = 4;
      } else if (currentPage >= totalPages - 1) {
        startPage = totalPages - 3;
      }

      // Add ellipsis after first page if needed
      if (startPage > 2) {
        pages.push('ellipsis1');
      }

      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Add ellipsis before last page if needed
      if (endPage < totalPages - 1) {
        pages.push('ellipsis2');
      }

      // Always show last page
      pages.push(totalPages);
    }

    return pages;
  };

  // Handle user selection
  const handleUserSelect = (user: UserWithCase) => {
    setSelectedUser(user);
    setDialogOpen(true);
  };

  if (!userData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  // Render content based on loading and error states
  const renderContent = () => {
    if (loading) {
      // Create an array of unique IDs for skeleton items
      const skeletonItems = [
        'skeleton-1',
        'skeleton-2',
        'skeleton-3',
        'skeleton-4',
        'skeleton-5'
      ];

      return (
        <div className="space-y-2">
          {skeletonItems.map((id) => (
            <div key={id} className="flex items-center space-x-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-[250px]" />
                <Skeleton className="h-4 w-[200px]" />
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex justify-center p-4 text-red-500">
          <p>{error}</p>
        </div>
      );
    }

    return (
      <>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Dosya No</TableHead>
              <TableHead>İsim</TableHead>
              <TableHead>Rol</TableHead>
              <TableHead>Tür</TableHead>
              <TableHead>Vekil</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-4">
                  Arama kriterlerine uygun kullanıcı bulunamadı
                </TableCell>
              </TableRow>
            ) : (
              paginatedUsers.map((user, index) => (
                <TableRow
                  key={`${user.caseNumber}-${user.name}-${index}`}
                  className="cursor-pointer hover:bg-muted hover:shadow-sm transition-all"
                  onClick={() => handleUserSelect(user)}
                >
                  <TableCell className="font-medium">{user.caseNumber}</TableCell>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{user.role}</Badge>
                  </TableCell>
                  <TableCell>
                    {user.type === 'Kişi' ? (
                      <Badge variant="secondary" className="flex items-center gap-1 w-fit">
                        <User className="h-3 w-3" />
                        Kişi
                      </Badge>
                    ) : (
                      <Badge variant="secondary" className="flex items-center gap-1 w-fit">
                        <Building className="h-3 w-3" />
                        Kurum
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>{user.attorney ?? '-'}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>

        {totalPages > 1 && (
          <div className="mt-4">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
                    size="default"
                  />
                </PaginationItem>

                {getPageNumbers().map((page) => {
                  if (page === 'ellipsis1') {
                    return (
                      <PaginationItem key="ellipsis1">
                        <PaginationEllipsis />
                      </PaginationItem>
                    );
                  }

                  if (page === 'ellipsis2') {
                    return (
                      <PaginationItem key="ellipsis2">
                        <PaginationEllipsis />
                      </PaginationItem>
                    );
                  }

                  return (
                    <PaginationItem key={`page-${page}`}>
                      <PaginationLink
                        isActive={currentPage === page}
                        onClick={() => setCurrentPage(Number(page))}
                        size="default"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    className={currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}
                    size="default"
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </>
    );
  };

  return (
    <DashboardLayout userName={userData.name} userSurname={userData.surname}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Users className="h-8 w-8" />
            Kullanıcılar
          </h1>
          <p className="text-muted-foreground">
            Tüm dosyalardaki tarafları görüntüleyin
          </p>
        </div>

        <div className="flex items-center mb-4">
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="İsim, rol veya dosya numarası ile ara..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1); // Reset to first page on search
              }}
            />
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Kullanıcı Listesi</CardTitle>
            <CardDescription>
              Tüm dosyalardaki tarafların listesi. Detayları görmek için bir satıra tıklayın.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {renderContent()}
          </CardContent>
        </Card>
      </div>

      {/* User Details Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        {selectedUser && (
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Kullanıcı Detayları</DialogTitle>
              <DialogDescription>
                {selectedUser.caseNumber} - {selectedUser.name}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="flex items-center justify-center">
                {selectedUser.type === 'Kişi' ? (
                  <div className="h-24 w-24 rounded-full bg-muted flex items-center justify-center">
                    <User className="h-12 w-12 text-muted-foreground" />
                  </div>
                ) : (
                  <div className="h-24 w-24 rounded-full bg-muted flex items-center justify-center">
                    <Building className="h-12 w-12 text-muted-foreground" />
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">İsim</p>
                  <p className="text-base">{selectedUser.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Rol</p>
                  <p className="text-base">{selectedUser.role}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Dosya No</p>
                  <p className="text-base">{selectedUser.caseNumber}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Tür</p>
                  <p className="text-base">{selectedUser.type}</p>
                </div>
                {selectedUser.attorney && (
                  <div className="col-span-2">
                    <p className="text-sm font-medium text-muted-foreground">Vekil</p>
                    <p className="text-base">{selectedUser.attorney}</p>
                  </div>
                )}
              </div>
            </div>
          </DialogContent>
        )}
      </Dialog>
    </DashboardLayout>
  );
}
