'use client';

import { useState, useEffect } from 'react';
import { RefreshCw, CheckCircle, AlertCircle, Copy, Loader2 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { getUserData } from '@/lib/auth';
import { DashboardLayout } from '@/components/dashboard/layout';

interface SyncStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  error?: string;
}

interface UserData {
  name: string;
  surname: string;
}

export default function SynchronizationPage() {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState<string>('');
  const [clipboardContent, setClipboardContent] = useState<string>('');
  const [syncSteps, setSyncSteps] = useState<SyncStep[]>([
    {
      id: 'clipboard',
      title: 'Cookie Bilgisi Okunuyor',
      description: 'Sistem panosundan Cookie header değeri okunuyor',
      status: 'pending'
    },
    {
      id: 'uyap-connect',
      title: 'UYAP Bağlantısı',
      description: 'UYAP API\'ye bağlanılıyor',
      status: 'pending'
    },
    {
      id: 'uyap-data',
      title: 'UYAP Verileri',
      description: 'UYAP\'tan veriler çekiliyor',
      status: 'pending'
    },
    {
      id: 'backend-save',
      title: 'Veri Kaydetme',
      description: 'Veriler backend API\'ye kaydediliyor',
      status: 'pending'
    },
    {
      id: 'complete',
      title: 'Senkronizasyon Tamamlandı',
      description: 'Tüm veriler başarıyla senkronize edildi',
      status: 'pending'
    }
  ]);

  useEffect(() => {
    const loadUserData = async () => {
      try {
        const data = getUserData();
        if (data && data.name && data.surname) {
          setUserData({
            name: data.name,
            surname: data.surname
          });
        }
      } catch (error) {
        console.error('Error loading user data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUserData();
  }, []);

  const updateStepStatus = (stepId: string, status: SyncStep['status'], error?: string) => {
    setSyncSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { ...step, status, error }
        : step
    ));
  };

  const simulateDelay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  const readClipboard = async (): Promise<string> => {
    try {
      if (!navigator.clipboard) {
        throw new Error('Clipboard API desteklenmiyor');
      }
      
      const text = await navigator.clipboard.readText();
      if (!text) {
        throw new Error('Geçerli bir Cookie header bulunamadı');
      }
      
      return 'JSESSIONID=' + text;
    } catch (error) {
      throw new Error('Pano okuma hatası: ' + (error as Error).message);
    }
  };

  const simulateUyapApiCall = async (endpoint: string): Promise<any> => {
    // Simulate API call delay
    await simulateDelay(2000 + Math.random() * 1000);
    
    // Simulate random success/failure
    if (Math.random() > 0.8) {
      throw new Error(`UYAP API hatası: ${endpoint} endpoint\'ine erişilemedi`);
    }
    
    // Return mock data
    return {
      success: true,
      data: {
        endpoint,
        timestamp: new Date().toISOString(),
        records: Math.floor(Math.random() * 100) + 1
      }
    };
  };

  const simulateBackendSave = async (data: any): Promise<any> => {
    await simulateDelay(1500);
    
    if (Math.random() > 0.9) {
      throw new Error('Backend kaydetme hatası');
    }
    
    return {
      success: true,
      savedRecords: data.records || 0
    };
  };

  const startSynchronization = async () => {
    setIsSyncing(true);
    setProgress(0);
    
    // Reset all steps to pending
    setSyncSteps(prev => prev.map(step => ({ ...step, status: 'pending', error: undefined })));
    
    try {
      // Step 1: Read clipboard
      setCurrentStep('Cookie bilgisi okunuyor...');
      updateStepStatus('clipboard', 'running');
      setProgress(10);
      
      const cookieData = await readClipboard();
      setClipboardContent(cookieData);
      updateStepStatus('clipboard', 'completed');
      setProgress(20);
      
      await simulateDelay(500);
      
      // Step 2: Connect to UYAP
      setCurrentStep('UYAP\'a bağlanılıyor...');
      updateStepStatus('uyap-connect', 'running');
      setProgress(30);
      
      await simulateUyapApiCall('connect');
      updateStepStatus('uyap-connect', 'completed');
      setProgress(50);
      
      await simulateDelay(500);
      
      // Step 3: Fetch UYAP data
      setCurrentStep('UYAP verileri çekiliyor...');
      updateStepStatus('uyap-data', 'running');
      setProgress(60);
      
      const uyapData = await simulateUyapApiCall('data');
      updateStepStatus('uyap-data', 'completed');
      setProgress(80);
      
      await simulateDelay(500);
      
      // Step 4: Save to backend
      setCurrentStep('Veriler kaydediliyor...');
      updateStepStatus('backend-save', 'running');
      setProgress(90);
      
      await simulateBackendSave(uyapData.data);
      updateStepStatus('backend-save', 'completed');
      setProgress(95);
      
      await simulateDelay(500);
      
      // Step 5: Complete
      setCurrentStep('Senkronizasyon tamamlandı!');
      updateStepStatus('complete', 'completed');
      setProgress(100);
      
    } catch (error) {
      const errorMessage = (error as Error).message;
      setCurrentStep(`Hata: ${errorMessage}`);
      
      // Find the current running step and mark it as error
      const runningStep = syncSteps.find(step => step.status === 'running');
      if (runningStep) {
        updateStepStatus(runningStep.id, 'error', errorMessage);
      }
    } finally {
      setIsSyncing(false);
    }
  };

  const resetSynchronization = () => {
    setProgress(0);
    setCurrentStep('');
    setClipboardContent('');
    setSyncSteps(prev => prev.map(step => ({ ...step, status: 'pending', error: undefined })));
  };

  const getStepIcon = (status: SyncStep['status']) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-muted-foreground" />;
    }
  };

  const getStepBadgeVariant = (status: SyncStep['status']) => {
    switch (status) {
      case 'running':
        return 'default';
      case 'completed':
        return 'default';
      case 'error':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Kullanıcı bilgileri yüklenemedi.</p>
      </div>
    );
  }

  return (
    <DashboardLayout userName={userData.name} userSurname={userData.surname}>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">UYAP Senkronizasyonu</h1>
          <p className="text-muted-foreground">
            UYAP sisteminden verilerinizi senkronize edin.
          </p>
        </div>

        {/* Main Synchronization Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5" />
              Senkronizasyon Durumu
            </CardTitle>
            <CardDescription>
              UYAP verilerinizi senkronize etmek için aşağıdaki butona tıklayın.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Control Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={startSynchronization}
                disabled={isSyncing}
                className="flex-1 sm:flex-none"
                size="lg"
              >
                {isSyncing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Senkronizasyon Devam Ediyor...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Senkronizasyon Başlat
                  </>
                )}
              </Button>
              
              {!isSyncing && progress > 0 && (
                <Button
                  onClick={resetSynchronization}
                  variant="outline"
                  className="flex-1 sm:flex-none"
                >
                  Sıfırla
                </Button>
              )}
            </div>

            {/* Progress Bar */}
            {(isSyncing || progress > 0) && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>İlerleme</span>
                  <span>{progress}%</span>
                </div>
                <Progress value={progress} className="h-2" />
                {currentStep && (
                  <p className="text-sm text-muted-foreground">{currentStep}</p>
                )}
              </div>
            )}

            {/* Clipboard Content Display */}
            {clipboardContent && (
              <Alert>
                <Copy className="h-4 w-4" />
                <AlertDescription>
                  <strong>Okunan Cookie:</strong> {clipboardContent}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Steps Progress */}
        <Card>
          <CardHeader>
            <CardTitle>Senkronizasyon Adımları</CardTitle>
            <CardDescription>
              Senkronizasyon sürecinin detaylı durumu
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {syncSteps.map((step, index) => (
                <div key={step.id} className="flex items-start gap-3 p-3 rounded-lg border">
                  <div className="flex-shrink-0 mt-0.5">
                    {getStepIcon(step.status)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-sm">{step.title}</h4>
                      <Badge variant={getStepBadgeVariant(step.status)} className="text-xs">
                        {step.status === 'pending' && 'Bekliyor'}
                        {step.status === 'running' && 'Çalışıyor'}
                        {step.status === 'completed' && 'Tamamlandı'}
                        {step.status === 'error' && 'Hata'}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{step.description}</p>
                    {step.error && (
                      <Alert className="mt-2">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription className="text-sm">
                          {step.error}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Instructions Card */}
        <Card>
          <CardHeader>
            <CardTitle>Kullanım Talimatları</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="text-sm space-y-2">
              <p><strong>1.</strong> UYAP sistemine giriş yapın ve gerekli sayfaya gidin.</p>
              <p><strong>2.</strong> Tarayıcınızın geliştirici araçlarını açın (F12).</p>
              <p><strong>3.</strong> Network sekmesine gidin ve bir istek yapın.</p>
              <p><strong>4.</strong> Request Headers bölümünden Cookie değerini kopyalayın.</p>
              <p><strong>5.</strong> Bu sayfaya geri dönün ve "Senkronizasyon Başlat" butonuna tıklayın.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
