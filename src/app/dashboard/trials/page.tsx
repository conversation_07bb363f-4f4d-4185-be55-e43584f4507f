'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Gavel, Calendar, User, FileText, Building } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';

import { getUserData } from '@/lib/auth';
import { trialsService, type Trial } from '@/lib/api';
import { DashboardLayout } from '@/components/dashboard/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';

export default function TrialsPage() {
  const router = useRouter();
  const [userData, setUserData] = useState<{ name: string; surname: string } | null>(null);
  const [trials, setTrials] = useState<Trial[]>([]);
  const [selectedTrial, setSelectedTrial] = useState<Trial | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  useEffect(() => {
    const data = getUserData();
    if (!data) {
      router.push('/login');
      return;
    }
    setUserData({
      name: data.name ?? '',
      surname: data.surname ?? ''
    });

    const fetchTrials = async () => {
      try {
        setLoading(true);
        const trialsData = await trialsService.getTrials();
        // Sort trials by date
        const sortedTrials = [...trialsData].sort((a, b) => {
          const dateA = new Date(a.tarihSaat);
          const dateB = new Date(b.tarihSaat);
          return dateA.getTime() - dateB.getTime();
        });
        setTrials(sortedTrials);

        // Set the first trial as selected by default if available
        if (sortedTrials.length > 0) {
          setSelectedTrial(sortedTrials[0]);
        }

        setError(null);
      } catch (err) {
        console.error('Failed to fetch trials:', err);
        setError('Duruşma bilgileri yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
      } finally {
        setLoading(false);
      }
    };

    fetchTrials();
  }, [router]);

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = parseISO(dateString);
      return format(date, 'd MMMM yyyy, HH:mm', { locale: tr });
    } catch (error) {
      console.error('Date parsing error:', error);
      return dateString;
    }
  };

  // Get status badge color based on islemSonucu
  const getStatusBadge = (islemSonucu: number, islemSonucuAciklama: string) => {
    switch (islemSonucu) {
      case 0:
        return <Badge variant="outline">{islemSonucuAciklama}</Badge>;
      case 1:
        return <Badge variant="success">{islemSonucuAciklama}</Badge>;
      default:
        return <Badge variant="secondary">{islemSonucuAciklama}</Badge>;
    }
  };



  if (!userData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <DashboardLayout userName={userData.name} userSurname={userData.surname}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Duruşmalar</h1>
          <p className="text-muted-foreground mt-2">
            Yaklaşan duruşmalarınızı görüntüleyin ve takip edin.
          </p>
        </div>

        {/* Content based on loading/error/data state */}
        {(() => {
          if (loading) {
            return (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-center h-[200px]">
                    <p>Duruşma bilgileri yükleniyor...</p>
                  </div>
                </CardContent>
              </Card>
            );
          }

          if (error) {
            return (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-center h-[200px] text-destructive">
                    <p>{error}</p>
                  </div>
                </CardContent>
              </Card>
            );
          }

          if (trials.length === 0) {
            return (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-center h-[200px]">
                    <p>Görüntülenecek duruşma bulunamadı.</p>
                  </div>
                </CardContent>
              </Card>
            );
          }

          return (
            <Card>
              <CardHeader>
                <CardTitle>Duruşma Listesi</CardTitle>
                <CardDescription>
                  Tüm duruşmalarınızın listesi. Detayları görmek için bir satıra tıklayın.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Dosya No</TableHead>
                      <TableHead>Tarih/Saat</TableHead>
                      <TableHead>Mahkeme</TableHead>
                      <TableHead>İşlem Türü</TableHead>
                      <TableHead>Durum</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {trials.map((trial) => (
                      <TableRow
                        key={trial.kayitId}
                        className={`cursor-pointer hover:bg-muted hover:shadow-sm transition-all ${selectedTrial?.kayitId === trial.kayitId ? 'bg-muted font-medium' : ''}`}
                        onClick={() => {
                          setSelectedTrial(trial);
                          setDialogOpen(true);
                        }}
                        title="Detayları görmek için tıklayın"
                      >
                        <TableCell className="font-medium">{trial.dosyaNo}</TableCell>
                        <TableCell>{formatDate(trial.tarihSaat)}</TableCell>
                        <TableCell>{trial.yerelBirimAd}</TableCell>
                        <TableCell>{trial.islemTuruAciklama}</TableCell>
                        <TableCell>{getStatusBadge(trial.islemSonucu, trial.islemSonucuAciklama)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          );
        })()}

        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          {selectedTrial && (
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Duruşma Detayları</DialogTitle>
                <DialogDescription>
                  {selectedTrial.dosyaNo} - {selectedTrial.dosyaTurKodAciklama}
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-6 py-4">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-start space-x-2">
                      <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <h3 className="font-semibold">Tarih ve Saat</h3>
                        <p>{formatDate(selectedTrial.tarihSaat)}</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <h3 className="font-semibold">Dosya Bilgileri</h3>
                        <p>{selectedTrial.dosyaNo} - {selectedTrial.dosyaTurKodAciklama}</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <Building className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <h3 className="font-semibold">Mahkeme</h3>
                        <p>{selectedTrial.yerelBirimAd}</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <Gavel className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <h3 className="font-semibold">İşlem</h3>
                        <p>{selectedTrial.islemTuruAciklama} - {selectedTrial.islemSonucuAciklama}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Taraflar</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {selectedTrial.dosyaTaraflari.map((party) => (
                        <div key={`${party.isim}-${party.sifat}`} className="flex items-start space-x-2">
                          <User className="h-5 w-5 text-muted-foreground mt-0.5" />
                          <div>
                            <p className="text-sm font-medium">{party.isim} {party.soyad ?? ''}</p>
                            <p className="text-xs text-muted-foreground">{party.sifat}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button variant="outline" onClick={() => setDialogOpen(false)}>
                  Kapat
                </Button>
              </div>
            </DialogContent>
          )}
        </Dialog>
      </div>
    </DashboardLayout>
  );
}
