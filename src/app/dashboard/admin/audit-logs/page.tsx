'use client';

import { useState, useEffect } from 'react';
import { FileSearch, Refresh<PERSON>w, Bar<PERSON>hart3, Filter, Download, Trash2, AlertTriangle } from 'lucide-react';
import { DashboardLayout } from '@/components/dashboard/layout';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BackButton } from '@/components/ui/back-button';
import { toast } from 'sonner';
import { adminAuditLogsService } from '@/lib/api';
import { AuditLogResponse, AuditLogStatisticsResponse } from '@/lib/schemas/audit-logs';
import { AuditLogsList } from '@/components/dashboard/admin/audit-logs/list';
import { AuditLogsStatistics } from '@/components/dashboard/admin/audit-logs/statistics';
import { AuditLogsFilters } from '@/components/dashboard/admin/audit-logs/filters';
import { AuditLogsCleanup } from '@/components/dashboard/admin/audit-logs/cleanup';

export default function AuditLogsPage() {
  const [activeTab, setActiveTab] = useState('recent');
  const [logs, setLogs] = useState<AuditLogResponse[]>([]);
  const [statistics, setStatistics] = useState<AuditLogStatisticsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [statisticsLoading, setStatisticsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [statisticsError, setStatisticsError] = useState<string | null>(null);

  // Load recent audit logs
  const loadRecentLogs = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await adminAuditLogsService.getRecentAuditLogs();
      setLogs(data);
    } catch (error) {
      console.error('Load recent logs error:', error);
      setError(error instanceof Error ? error.message : 'Denetim kayıtları yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  // Load failed requests
  const loadFailedRequests = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await adminAuditLogsService.getFailedRequests();
      setLogs(data);
    } catch (error) {
      console.error('Load failed requests error:', error);
      setError(error instanceof Error ? error.message : 'Başarısız istekler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  // Load slow requests
  const loadSlowRequests = async (thresholdMs: number = 1000) => {
    try {
      setLoading(true);
      setError(null);
      const data = await adminAuditLogsService.getSlowRequests(thresholdMs);
      setLogs(data);
    } catch (error) {
      console.error('Load slow requests error:', error);
      setError(error instanceof Error ? error.message : 'Yavaş istekler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  // Load error logs
  const loadErrorLogs = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await adminAuditLogsService.getAuditLogsWithErrors();
      setLogs(data);
    } catch (error) {
      console.error('Load error logs error:', error);
      setError(error instanceof Error ? error.message : 'Hatalı kayıtlar yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  // Load statistics
  const loadStatistics = async (startDate: string, endDate: string) => {
    try {
      setStatisticsLoading(true);
      setStatisticsError(null);
      const data = await adminAuditLogsService.getAuditStatistics({ startDate, endDate });
      setStatistics(data);
    } catch (error) {
      console.error('Load statistics error:', error);
      setStatisticsError(error instanceof Error ? error.message : 'İstatistikler yüklenirken hata oluştu');
    } finally {
      setStatisticsLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    
    switch (value) {
      case 'recent':
        loadRecentLogs();
        break;
      case 'failed':
        loadFailedRequests();
        break;
      case 'slow':
        loadSlowRequests();
        break;
      case 'errors':
        loadErrorLogs();
        break;
      case 'statistics':
        // Statistics will be loaded when date range is selected
        break;
    }
  };

  // Handle filter search
  const handleFilterSearch = async (filters: any) => {
    try {
      setLoading(true);
      setError(null);
      
      let data: AuditLogResponse[] = [];
      
      if (filters.userId) {
        data = await adminAuditLogsService.getAuditLogsByUserId(filters.userId);
      } else if (filters.userEmail && filters.startDate && filters.endDate) {
        data = await adminAuditLogsService.getAuditLogsByUserAndDateRange({
          userEmail: filters.userEmail,
          startDate: filters.startDate,
          endDate: filters.endDate
        });
      } else if (filters.startDate && filters.endDate) {
        data = await adminAuditLogsService.getAuditLogsByDateRange({
          startDate: filters.startDate,
          endDate: filters.endDate
        });
      } else if (filters.endpointUrl) {
        data = await adminAuditLogsService.getAuditLogsByEndpoint(filters.endpointUrl);
      } else if (filters.clientIp) {
        data = await adminAuditLogsService.getAuditLogsByClientIp(filters.clientIp);
      } else {
        data = await adminAuditLogsService.getRecentAuditLogs();
      }
      
      setLogs(data);
      setActiveTab('filtered');
    } catch (error) {
      console.error('Filter search error:', error);
      setError(error instanceof Error ? error.message : 'Filtreleme sırasında hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  // Handle export
  const handleExport = async () => {
    try {
      const blob = await adminAuditLogsService.exportAuditLogs(logs);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `denetim-kayitlari-${new Date().toISOString().split('T')[0]}-${new Date().toTimeString().split(' ')[0].replace(/:/g, '')}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('Denetim kayıtları başarıyla dışa aktarıldı');
    } catch (error) {
      console.error('Export error:', error);
      toast.error(error instanceof Error ? error.message : 'Dışa aktarma sırasında hata oluştu');
    }
  };

  // Load initial data
  useEffect(() => {
    loadRecentLogs();
  }, []);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="flex items-center gap-3">
            <BackButton />
            <FileSearch className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Denetim Kayıtları</h1>
              <p className="text-muted-foreground">
                Sistem denetim kayıtlarını görüntüle ve analiz et
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              disabled={logs.length === 0}
              className="gap-2"
            >
              <Download className="h-4 w-4" />
              Dışa Aktar
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                switch (activeTab) {
                  case 'recent':
                    loadRecentLogs();
                    break;
                  case 'failed':
                    loadFailedRequests();
                    break;
                  case 'slow':
                    loadSlowRequests();
                    break;
                  case 'errors':
                    loadErrorLogs();
                    break;
                }
              }}
              className="gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Yenile
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Kayıt</CardTitle>
              <FileSearch className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{logs.length}</div>
              <p className="text-xs text-muted-foreground">
                Görüntülenen kayıt sayısı
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Başarısız İstekler</CardTitle>
              <AlertTriangle className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">
                {logs.filter(log => log.responseStatusCode >= 400).length}
              </div>
              <p className="text-xs text-muted-foreground">
                4xx ve 5xx hata kodları
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Ortalama Süre</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {logs.length > 0 
                  ? `${Math.round(logs.reduce((sum, log) => sum + log.processingTimeMs, 0) / logs.length)}ms`
                  : '0ms'
                }
              </div>
              <p className="text-xs text-muted-foreground">
                İşlem süresi ortalaması
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Benzersiz Kullanıcı</CardTitle>
              <FileSearch className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(logs.filter(log => log.userEmail).map(log => log.userEmail)).size}
              </div>
              <p className="text-xs text-muted-foreground">
                Farklı kullanıcı sayısı
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-6 lg:w-auto">
            <TabsTrigger value="recent" className="gap-2">
              <FileSearch className="h-4 w-4" />
              Son Kayıtlar
            </TabsTrigger>
            <TabsTrigger value="failed" className="gap-2">
              <AlertTriangle className="h-4 w-4" />
              Başarısız
            </TabsTrigger>
            <TabsTrigger value="slow" className="gap-2">
              <BarChart3 className="h-4 w-4" />
              Yavaş
            </TabsTrigger>
            <TabsTrigger value="errors" className="gap-2">
              <Trash2 className="h-4 w-4" />
              Hatalar
            </TabsTrigger>
            <TabsTrigger value="filter" className="gap-2">
              <Filter className="h-4 w-4" />
              Filtrele
            </TabsTrigger>
            <TabsTrigger value="statistics" className="gap-2">
              <BarChart3 className="h-4 w-4" />
              İstatistikler
            </TabsTrigger>
          </TabsList>

          <TabsContent value="recent" className="space-y-6">
            <AuditLogsList
              logs={logs}
              isLoading={loading}
              error={error}
              onRefresh={loadRecentLogs}
            />
          </TabsContent>

          <TabsContent value="failed" className="space-y-6">
            <AuditLogsList
              logs={logs}
              isLoading={loading}
              error={error}
              onRefresh={loadFailedRequests}
            />
          </TabsContent>

          <TabsContent value="slow" className="space-y-6">
            <AuditLogsList
              logs={logs}
              isLoading={loading}
              error={error}
              onRefresh={() => loadSlowRequests()}
            />
          </TabsContent>

          <TabsContent value="errors" className="space-y-6">
            <AuditLogsList
              logs={logs}
              isLoading={loading}
              error={error}
              onRefresh={loadErrorLogs}
            />
          </TabsContent>

          <TabsContent value="filter" className="space-y-6">
            <AuditLogsFilters onSearch={handleFilterSearch} />
            {activeTab === 'filtered' && (
              <AuditLogsList
                logs={logs}
                isLoading={loading}
                error={error}
                onRefresh={() => {}}
              />
            )}
          </TabsContent>

          <TabsContent value="statistics" className="space-y-6">
            <AuditLogsStatistics
              statistics={statistics}
              isLoading={statisticsLoading}
              error={statisticsError}
              onLoadStatistics={loadStatistics}
            />
          </TabsContent>
        </Tabs>

        {/* Cleanup Section */}
        <AuditLogsCleanup />
      </div>
    </DashboardLayout>
  );
}
