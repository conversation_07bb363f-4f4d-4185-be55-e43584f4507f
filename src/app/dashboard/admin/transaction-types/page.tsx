'use client';

import { useState, useEffect } from 'react';
import { ArrowUpDown, Plus, Search, RefreshCw } from 'lucide-react';
import { DashboardLayout } from '@/components/dashboard/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { BackButton } from '@/components/ui/back-button';
import { toast } from 'sonner';
import { transactionTypesService } from '@/lib/api';
import { TransactionType } from '@/lib/schemas/transaction-types';
import { TransactionTypeList } from '@/components/dashboard/admin/transaction-types/list';
import { TransactionTypeForm } from '@/components/dashboard/admin/transaction-types/form';

export default function TransactionTypeManagementPage() {
  const [transactionTypes, setTransactionTypes] = useState<TransactionType[]>([]);
  const [filteredTransactionTypes, setFilteredTransactionTypes] = useState<TransactionType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  // Fetch transaction types
  const fetchTransactionTypes = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await transactionTypesService.getAllTransactionTypes();
      setTransactionTypes(data);
    } catch (err) {
      console.error('Fetch transaction types error:', err);
      setError(err instanceof Error ? err.message : 'İşlem türleri yüklenirken hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchTransactionTypes();
  }, []);

  // Filter transaction types based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredTransactionTypes(transactionTypes);
    } else {
      const filtered = transactionTypes.filter(transactionType =>
        transactionType.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        transactionType.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredTransactionTypes(filtered);
    }
  }, [searchQuery, transactionTypes]);

  // Handle form success
  const handleFormSuccess = (transactionType: TransactionType) => {
    setCreateDialogOpen(false);
    fetchTransactionTypes();
    toast.success('İşlem türü başarıyla oluşturuldu', {
      description: `"${transactionType.name}" işlem türü sisteme eklendi.`
    });
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchTransactionTypes();
    toast.success('İşlem türü listesi yenilendi');
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Back Button */}
        <BackButton href="/dashboard/admin" />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-3">
            <ArrowUpDown className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">İşlem Türü Yönetimi</h1>
              <p className="text-muted-foreground">
                İşlem türlerini görüntüle, oluştur, düzenle ve sil
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Yenile
            </Button>
            <Button onClick={() => setCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Yeni İşlem Türü
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Toplam İşlem Türü
              </CardTitle>
              <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? (
                  <Skeleton className="h-8 w-16" />
                ) : (
                  transactionTypes.length
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Sistemde kayıtlı işlem türü sayısı
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Gelir Türleri
              </CardTitle>
              <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? (
                  <Skeleton className="h-8 w-16" />
                ) : (
                  transactionTypes.filter(t => t.category === 'INCOME').length
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Gelir kategorisindeki işlem türleri
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Gider Türleri
              </CardTitle>
              <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? (
                  <Skeleton className="h-8 w-16" />
                ) : (
                  transactionTypes.filter(t => t.category === 'EXPENSE').length
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Gider kategorisindeki işlem türleri
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <Card>
          <CardHeader>
            <CardTitle>İşlem Türlerini Ara</CardTitle>
            <CardDescription>
              İşlem türü adı veya kategoriye göre arama yapın
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="İşlem türü ara..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
                disabled={loading}
              />
            </div>
          </CardContent>
        </Card>

        {/* Transaction Types List */}
        <Card>
          <CardHeader>
            <CardTitle>İşlem Türleri</CardTitle>
            <CardDescription>
              {loading ? (
                <Skeleton className="h-4 w-48" />
              ) : (
                `${filteredTransactionTypes.length} işlem türü listeleniyor`
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <TransactionTypeList
              transactionTypes={filteredTransactionTypes}
              isLoading={loading}
              error={error}
              onRefresh={fetchTransactionTypes}
            />
          </CardContent>
        </Card>

        {/* Create Transaction Type Dialog */}
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto mx-4">
            <DialogHeader>
              <DialogTitle>Yeni İşlem Türü Oluştur</DialogTitle>
              <DialogDescription>
                Sisteme yeni bir işlem türü ekleyin
              </DialogDescription>
            </DialogHeader>
            <div className="max-h-[60vh] overflow-y-auto">
              <TransactionTypeForm
                onSuccess={handleFormSuccess}
                onCancel={() => setCreateDialogOpen(false)}
                inDialog={true}
              />
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  );
}
