'use client';


import Link from 'next/link';
import { Shield, Package, Users, Settings, BarChart3, Ticket, CreditCard, FolderOpen, ArrowUpDown, FileSearch } from 'lucide-react';
import { DashboardLayout } from '@/components/dashboard/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface AdminSection {
  title: string;
  description: string;
  href: string;
  icon: React.ReactNode;
  available: boolean;
}

export default function AdminPage() {
  const adminSections: AdminSection[] = [
    {
      title: '<PERSON>rün Yönetim<PERSON>',
      description: '<PERSON>rünleri görüntüle, oluştur, düzenle ve sil',
      href: '/dashboard/admin/products',
      icon: <Package className="h-8 w-8" />,
      available: true,
    },
    {
      title: '<PERSON><PERSON><PERSON>',
      description: '<PERSON><PERSON><PERSON> kupon<PERSON>ını görüntü<PERSON>, oluştur, düzenle ve sil',
      href: '/dashboard/admin/coupons',
      icon: <Ticket className="h-8 w-8" />,
      available: true,
    },
    {
      title: 'Ödeme Yönetimi',
      description: 'Ödeme işlemlerini görüntüle ve takip et',
      href: '/dashboard/admin/payments',
      icon: <CreditCard className="h-8 w-8" />,
      available: true,
    },
    {
      title: 'Dosya Yönetimi',
      description: 'Dosyaları yükle, görüntüle, düzenle ve sil',
      href: '/dashboard/admin/files',
      icon: <FolderOpen className="h-8 w-8" />,
      available: true,
    },
    {
      title: 'İşlem Türü Yönetimi',
      description: 'İşlem türlerini görüntüle, oluştur, düzenle ve sil',
      href: '/dashboard/admin/transaction-types',
      icon: <ArrowUpDown className="h-8 w-8" />,
      available: true,
    },
    {
      title: 'Kullanıcı Yönetimi',
      description: 'Kullanıcıları yönet ve izinleri düzenle',
      href: '/dashboard/admin/users',
      icon: <Users className="h-8 w-8" />,
      available: false,
    },
    {
      title: 'Sistem Ayarları',
      description: 'Sistem genelindeki ayarları yapılandır',
      href: '/dashboard/admin/system-settings',
      icon: <Settings className="h-8 w-8" />,
      available: false,
    },
    {
      title: 'Kullanıcı Raporları',
      description: 'Kullanıcı raporlarını görüntüle ve analiz et',
      href: '/dashboard/admin/user-reports',
      icon: <BarChart3 className="h-8 w-8" />,
      available: true,
    },
    {
      title: 'Denetim Kayıtları',
      description: 'Sistem denetim kayıtlarını görüntüle ve analiz et',
      href: '/dashboard/admin/audit-logs',
      icon: <FileSearch className="h-8 w-8" />,
      available: true,
    },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3">
          <Shield className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Admin Paneli</h1>
            <p className="text-muted-foreground">
              Sistem yönetimi ve yapılandırma araçları
            </p>
          </div>
        </div>

        {/* Admin Sections Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {adminSections.map((section) => (
            <Card 
              key={section.href} 
              className={`transition-all duration-200 ${
                section.available 
                  ? 'hover:shadow-lg hover:scale-105 cursor-pointer' 
                  : 'opacity-60 cursor-not-allowed'
              }`}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${
                    section.available 
                      ? 'bg-primary/10 text-primary' 
                      : 'bg-muted text-muted-foreground'
                  }`}>
                    {section.icon}
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{section.title}</CardTitle>
                    {!section.available && (
                      <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                        Yakında
                      </span>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="mb-4">
                  {section.description}
                </CardDescription>
                {section.available ? (
                  <Link href={section.href}>
                    <Button className="w-full">
                      Yönet
                    </Button>
                  </Link>
                ) : (
                  <Button disabled className="w-full">
                    Yakında Gelecek
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Info Section */}
        <Card className="border-dashed">
          <CardHeader>
            <CardTitle className="text-lg">Bilgi</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Admin paneli sistem yöneticileri için tasarlanmıştır. Burada sistem genelindeki
              ayarları yapılandırabilir, kullanıcıları yönetebilir ve raporları görüntüleyebilirsiniz.
              Şu anda Ürün Yönetimi, Kupon Yönetimi, Ödeme Yönetimi, Dosya Yönetimi, İşlem Türü Yönetimi, Kullanıcı Raporları ve Denetim Kayıtları modülleri aktiftir. Diğer modüller yakında eklenecektir.
            </p>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
