'use client';

import { useState, useEffect } from 'react';
import { Ticket, Plus, Search, RefreshCw, Filter } from 'lucide-react';
import { DashboardLayout } from '@/components/dashboard/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { BackButton } from '@/components/ui/back-button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { couponsService } from '@/lib/api';
import { Coupon, CouponSearchParams, PaginatedCouponResponse } from '@/lib/schemas/coupons';
import { CouponList } from '@/components/dashboard/admin/coupons/list';
import { CouponForm } from '@/components/dashboard/admin/coupons/form';

export default function CouponManagementPage() {
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [paginationData, setPaginationData] = useState<PaginatedCouponResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(20);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  // Fetch coupons
  const fetchCoupons = async (params: CouponSearchParams = {}) => {
    try {
      setLoading(true);
      setError(null);
      
      const searchParams: CouponSearchParams = {
        page: currentPage,
        size: pageSize,
        sortBy: 'createdAt',
        sortDir: 'desc',
        ...params,
      };

      if (searchQuery.trim()) {
        searchParams.search = searchQuery.trim();
      }

      if (activeFilter !== 'all') {
        searchParams.active = activeFilter === 'active';
      }

      const data = await couponsService.getAllCoupons(searchParams);
      setCoupons(data.content);
      setPaginationData(data);
    } catch (err) {
      console.error('Fetch coupons error:', err);
      setError(err instanceof Error ? err.message : 'Kuponlar yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchCoupons();
  }, [currentPage, pageSize, searchQuery, activeFilter]);

  // Handle form success
  const handleFormSuccess = (coupon: Coupon) => {
    setCreateDialogOpen(false);
    fetchCoupons();
    toast.success('Kupon başarıyla oluşturuldu', {
      description: `"${coupon.code}" kuponu sisteme eklendi.`
    });
  };

  // Handle refresh
  const handleRefresh = () => {
    setCurrentPage(0);
    fetchCoupons();
    toast.success('Kupon listesi yenilendi');
  };

  // Handle search
  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(0);
  };

  // Handle filter change
  const handleFilterChange = (value: string) => {
    setActiveFilter(value);
    setCurrentPage(0);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Back Button */}
        <div className="flex items-center">
          <BackButton label="Admin Paneline Dön" />
        </div>

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-3">
            <Ticket className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Kupon Yönetimi</h1>
              <p className="text-muted-foreground">
                İndirim kuponlarını görüntüle, oluştur, düzenle ve sil
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Yenile
            </Button>
            <Button onClick={() => setCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Yeni Kupon
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Arama ve Filtreler</CardTitle>
            <CardDescription>
              Kuponları koda göre arayın ve duruma göre filtreleyin
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Kupon kodu ile ara..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <Select value={activeFilter} onValueChange={handleFilterChange}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Durum" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tümü</SelectItem>
                    <SelectItem value="active">Aktif</SelectItem>
                    <SelectItem value="inactive">Pasif</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Coupons List */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Kuponlar</CardTitle>
                <CardDescription>
                  {loading ? (
                    <Skeleton className="h-4 w-32" />
                  ) : paginationData ? (
                    `Toplam ${paginationData.totalElements} kupon (Sayfa ${paginationData.number + 1}/${paginationData.totalPages})`
                  ) : (
                    'Kupon listesi'
                  )}
                </CardDescription>
              </div>
              {paginationData && paginationData.totalPages > 1 && (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={paginationData.first || loading}
                  >
                    Önceki
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    {paginationData.number + 1} / {paginationData.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={paginationData.last || loading}
                  >
                    Sonraki
                  </Button>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <CouponList
              coupons={coupons}
              isLoading={loading}
              error={error}
              onRefresh={fetchCoupons}
            />
          </CardContent>
        </Card>

        {/* Create Coupon Dialog */}
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto mx-4">
            <DialogHeader>
              <DialogTitle>Yeni Kupon Oluştur</DialogTitle>
              <DialogDescription>
                Sisteme yeni bir indirim kuponu ekleyin
              </DialogDescription>
            </DialogHeader>
            <div className="max-h-[60vh] overflow-y-auto">
              <CouponForm
                onSuccess={handleFormSuccess}
                onCancel={() => setCreateDialogOpen(false)}
                inDialog={true}
              />
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  );
}
