'use client';

import { useState, useEffect } from 'react';
import { Bar<PERSON>hart3, RefreshCw, Users, TrendingUp, DollarSign, UserCheck } from 'lucide-react';
import { DashboardLayout } from '@/components/dashboard/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BackButton } from '@/components/ui/back-button';
import { toast } from 'sonner';
import { adminUserReportsService } from '@/lib/api';
import { AdminUserReport } from '@/lib/schemas/user-reports';
import { UserReportsList } from '@/components/dashboard/admin/user-reports/list';
import { UserReportsStatistics } from '@/components/dashboard/admin/user-reports/statistics';

export default function AdminUserReportsPage() {
  const [userReports, setUserReports] = useState<AdminUserReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load user reports
  const loadUserReports = async () => {
    try {
      setLoading(true);
      setError(null);
      const reports = await adminUserReportsService.getAllUserReports();
      setUserReports(reports);
    } catch (err) {
      console.error('Failed to load user reports:', err);
      const errorMessage = err instanceof Error ? err.message : 'Kullanıcı raporları yüklenirken hata oluştu.';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadUserReports();
  }, []);

  // Handle refresh
  const handleRefresh = () => {
    loadUserReports();
  };

  return (
    <DashboardLayout>
      <div className="space-y-6 p-6">
        {/* Back Button */}
        <BackButton href="/dashboard/admin" />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-3">
            <BarChart3 className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Kullanıcı Raporları</h1>
              <p className="text-muted-foreground">
                Sistem kullanıcılarının detaylı raporlarını görüntüle ve analiz et
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Yenile
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <UserReportsStatistics 
          userReports={userReports} 
          isLoading={loading} 
        />

        {/* User Reports List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Kullanıcı Listesi
            </CardTitle>
            <CardDescription>
              Tüm kullanıcıların detaylı bilgileri ve istatistikleri
            </CardDescription>
          </CardHeader>
          <CardContent>
            <UserReportsList
              userReports={userReports}
              isLoading={loading}
              error={error}
              onRefresh={handleRefresh}
            />
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
