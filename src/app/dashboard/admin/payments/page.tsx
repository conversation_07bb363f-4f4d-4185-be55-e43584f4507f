'use client';

import { useState, useEffect } from 'react';
import { CreditCard, Search, RefreshCw } from 'lucide-react';
import { DashboardLayout } from '@/components/dashboard/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { BackButton } from '@/components/ui/back-button';
import { toast } from 'sonner';
import { paymentsService } from '@/lib/api';
import { Payment, calculatePaymentAnalytics, PaymentAnalytics } from '@/lib/schemas/payments';
import { PaymentList } from '@/components/dashboard/admin/payments/list';
import { PaymentAnalyticsSummary } from '@/components/dashboard/admin/payments/analytics-summary';
import { PaymentAnalyticsModal } from '@/components/dashboard/admin/payments/analytics-modal';

export default function PaymentsPage() {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [filteredPayments, setFilteredPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [analytics, setAnalytics] = useState<PaymentAnalytics | null>(null);
  const [analyticsModalOpen, setAnalyticsModalOpen] = useState(false);

  // Fetch payments
  const fetchPayments = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await paymentsService.getAllPayments();
      setPayments(data);
      setFilteredPayments(data);

      // Calculate analytics
      const analyticsData = calculatePaymentAnalytics(data);
      setAnalytics(analyticsData);
    } catch (err) {
      console.error('Fetch payments error:', err);
      setError(err instanceof Error ? err.message : 'Ödemeler yüklenirken hata oluştu');
      toast.error(err instanceof Error ? err.message : 'Ödemeler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  // Filter payments based on search query
  const filterPayments = (query: string) => {
    if (!query.trim()) {
      setFilteredPayments(payments);
      return;
    }

    const filtered = payments.filter(payment => 
      payment.paymentId.toLowerCase().includes(query.toLowerCase()) ||
      payment.basketId.toLowerCase().includes(query.toLowerCase()) ||
      payment.product.name.toLowerCase().includes(query.toLowerCase()) ||
      payment.status.toLowerCase().includes(query.toLowerCase()) ||
      (payment.appliedCouponCode && payment.appliedCouponCode.toLowerCase().includes(query.toLowerCase()))
    );
    setFilteredPayments(filtered);
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterPayments(query);
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchPayments();
  };

  // Handle analytics modal
  const handleAnalyticsClick = () => {
    setAnalyticsModalOpen(true);
  };

  const handleAnalyticsModalClose = () => {
    setAnalyticsModalOpen(false);
  };

  // Load payments on component mount
  useEffect(() => {
    fetchPayments();
  }, []);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Back Button */}
        <div className="flex items-center">
          <BackButton label="Admin Paneline Dön" />
        </div>

        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Ödeme Yönetimi</h1>
            <p className="text-muted-foreground">
              Ödeme işlemlerini görüntüle ve takip et
            </p>
          </div>
          <Button onClick={handleRefresh} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Yenile
          </Button>
        </div>

        {/* Payment Analytics Summary */}
        {analytics && (
          <PaymentAnalyticsSummary
            analytics={analytics}
            isLoading={loading}
            onClick={handleAnalyticsClick}
          />
        )}

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Ödemeler
            </CardTitle>
            <CardDescription>
              Toplam {filteredPayments.length} ödeme bulundu
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4 md:flex-row md:items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Ödeme ID, sepet ID, ürün adı veya durum ile ara..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payments List */}
        <Card>
          <CardContent className="p-0">
            {loading ? (
              <div className="p-6 space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-4 w-[250px]" />
                      <Skeleton className="h-4 w-[200px]" />
                    </div>
                  </div>
                ))}
              </div>
            ) : error ? (
              <div className="p-6 text-center">
                <p className="text-muted-foreground mb-4">{error}</p>
                <Button onClick={handleRefresh} variant="outline">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Tekrar Dene
                </Button>
              </div>
            ) : (
              <PaymentList 
                payments={filteredPayments}
                isLoading={loading}
                error={error}
                onRefresh={handleRefresh}
              />
            )}
          </CardContent>
        </Card>

        {/* Payment Analytics Modal */}
        {analytics && (
          <PaymentAnalyticsModal
            analytics={analytics}
            isOpen={analyticsModalOpen}
            onClose={handleAnalyticsModalClose}
          />
        )}
      </div>
    </DashboardLayout>
  );
}
