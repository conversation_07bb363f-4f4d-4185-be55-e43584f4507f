'use client';

import { useState, useEffect } from 'react';
import { FolderOpen, Plus, RefreshCw, BarChart3 } from 'lucide-react';
import { DashboardLayout } from '@/components/dashboard/layout';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BackButton } from '@/components/ui/back-button';
import { toast } from 'sonner';
import { filesService } from '@/lib/api';
import { FileListItem, FileStatistics } from '@/lib/schemas/files';
import { FileUpload } from '@/components/dashboard/admin/files/upload';
import { FileList } from '@/components/dashboard/admin/files/list';
import { FileStatisticsDashboard } from '@/components/dashboard/admin/files/statistics';
import { 
  FilePreviewDialog, 
  DeleteConfirmationDialog, 
  BulkDeleteConfirmationDialog 
} from '@/components/dashboard/admin/files/operations';

export default function FileManagementPage() {
  const [files, setFiles] = useState<FileListItem[]>([]);
  const [statistics, setStatistics] = useState<FileStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [statisticsLoading, setStatisticsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statisticsError, setStatisticsError] = useState<string | null>(null);
  
  // Dialog states
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [previewFile, setPreviewFile] = useState<FileListItem | null>(null);
  const [deleteFile, setDeleteFile] = useState<FileListItem | null>(null);
  const [bulkDeleteFiles, setBulkDeleteFiles] = useState<FileListItem[]>([]);
  const [isDeleting, setIsDeleting] = useState(false);

  const [activeTab, setActiveTab] = useState('files');

  const loadFiles = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await filesService.getAllFiles();
      setFiles(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Dosyalar yüklenirken hata oluştu';
      setError(errorMessage);
      console.error('Load files error:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      setStatisticsLoading(true);
      setStatisticsError(null);
      const data = await filesService.getFileStatistics();
      setStatistics(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'İstatistikler yüklenirken hata oluştu';
      setStatisticsError(errorMessage);
      console.error('Load statistics error:', err);
    } finally {
      setStatisticsLoading(false);
    }
  };

  useEffect(() => {
    loadFiles();
    loadStatistics();
  }, []);

  const handleRefresh = () => {
    loadFiles();
    loadStatistics();
  };

  const handleUploadSuccess = () => {
    setUploadDialogOpen(false);
    loadFiles();
    loadStatistics();
    toast.success('Dosya başarıyla yüklendi');
  };

  const handleDownload = async (file: FileListItem) => {
    try {
      const blob = await filesService.downloadFile(file.id);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = file.originalFilename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('Dosya indirildi');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Dosya indirilemedi');
    }
  };

  const handleDelete = async () => {
    if (!deleteFile) return;

    try {
      setIsDeleting(true);
      await filesService.deleteFile(deleteFile.id);
      setFiles(prev => prev.filter(f => f.id !== deleteFile.id));
      setDeleteFile(null);
      loadStatistics(); // Refresh statistics
      toast.success('Dosya silindi');
    } catch (error) {
      console.error('Delete error:', error);
      toast.error('Dosya silinemedi');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleBulkDelete = async () => {
    if (bulkDeleteFiles.length === 0) return;

    try {
      setIsDeleting(true);
      const fileIds = bulkDeleteFiles.map(f => f.id);
      await filesService.bulkDeleteFiles(fileIds);
      setFiles(prev => prev.filter(f => !fileIds.includes(f.id)));
      setBulkDeleteFiles([]);
      loadStatistics(); // Refresh statistics
      toast.success(`${bulkDeleteFiles.length} dosya silindi`);
    } catch (error) {
      console.error('Bulk delete error:', error);
      toast.error('Dosyalar silinemedi');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Back Button */}
        <div className="flex items-center">
          <BackButton label="Admin Paneline Dön" />
        </div>

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-3">
            <FolderOpen className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Dosya Yönetimi</h1>
              <p className="text-muted-foreground">
                Dosyaları yükle, görüntüle, düzenle ve sil
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading || statisticsLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading || statisticsLoading ? 'animate-spin' : ''}`} />
              Yenile
            </Button>
            <Button onClick={() => setUploadDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Dosya Yükle
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
            <TabsTrigger value="files" className="gap-2">
              <FolderOpen className="h-4 w-4" />
              Dosyalar
            </TabsTrigger>
            <TabsTrigger value="statistics" className="gap-2">
              <BarChart3 className="h-4 w-4" />
              İstatistikler
            </TabsTrigger>
          </TabsList>

          <TabsContent value="files" className="space-y-6">
            <FileList
              files={files}
              isLoading={loading}
              error={error}
              onRefresh={loadFiles}
              onDownload={handleDownload}
              onDelete={(file) => setDeleteFile(file)}
              onView={(file) => setPreviewFile(file)}
              onBulkDelete={(files) => setBulkDeleteFiles(files)}
            />
          </TabsContent>

          <TabsContent value="statistics" className="space-y-6">
            <FileStatisticsDashboard
              statistics={statistics}
              isLoading={statisticsLoading}
              error={statisticsError}
            />
          </TabsContent>
        </Tabs>

        {/* Upload Dialog */}
        <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
          <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto mx-4">
            <DialogHeader>
              <DialogTitle>Dosya Yükle</DialogTitle>
              <DialogDescription>
                Sisteme yeni dosyalar yükleyin
              </DialogDescription>
            </DialogHeader>
            <div className="max-h-[70vh] overflow-y-auto">
              <FileUpload
                onSuccess={handleUploadSuccess}
                onCancel={() => setUploadDialogOpen(false)}
                inDialog={true}
              />
            </div>
          </DialogContent>
        </Dialog>

        {/* File Preview Dialog */}
        <FilePreviewDialog
          file={previewFile}
          open={!!previewFile}
          onOpenChange={(open) => !open && setPreviewFile(null)}
          onDownload={handleDownload}
          onDelete={(file) => {
            setPreviewFile(null);
            setDeleteFile(file);
          }}
        />

        {/* Delete Confirmation Dialog */}
        <DeleteConfirmationDialog
          file={deleteFile}
          open={!!deleteFile}
          onOpenChange={(open) => !open && setDeleteFile(null)}
          onConfirm={handleDelete}
          isDeleting={isDeleting}
        />

        {/* Bulk Delete Confirmation Dialog */}
        <BulkDeleteConfirmationDialog
          files={bulkDeleteFiles}
          open={bulkDeleteFiles.length > 0}
          onOpenChange={(open) => !open && setBulkDeleteFiles([])}
          onConfirm={handleBulkDelete}
          isDeleting={isDeleting}
        />
      </div>
    </DashboardLayout>
  );
}
