'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Settings } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { getUserData } from '@/lib/auth';
import { authService } from '@/lib/api';
import { DashboardLayout } from '@/components/dashboard/layout';
import { ProfilePhoto } from '@/components/user/profile-photo';

export default function SettingsPage() {
  const router = useRouter();
  const [userData, setUserData] = useState<any>(null);

  useEffect(() => {
    // Check if user is authenticated
    if (!authService.isAuthenticated()) {
      router.push('/login');
      return;
    }

    // Get user data from local storage
    const data = getUserData();
    setUserData(data);
  }, [router]);

  if (!userData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <DashboardLayout userName={userData.name} userSurname={userData.surname}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            Manage your account settings and preferences.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <ProfilePhoto 
            userName={userData.name} 
            userSurname={userData.surname} 
          />

          <Card>
            <CardHeader>
              <CardTitle>Account Settings</CardTitle>
              <CardDescription>
                Manage your account details and preferences.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium">Name</p>
                  <p className="text-sm text-muted-foreground">{userData.name} {userData.surname}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">User ID</p>
                  <p className="text-sm text-muted-foreground">{userData.id}</p>
                </div>
                {/* Additional settings can be added here */}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
