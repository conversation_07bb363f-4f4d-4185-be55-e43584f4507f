'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, FileSignature } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import { getUserData } from '@/lib/auth';
import { powerOfAttorneyService, authService } from '@/lib/api';
import { DashboardLayout } from '@/components/dashboard/layout';
import { PowerOfAttorneyForm, PowerOfAttorneyList } from '@/components/dashboard/power-of-attorneys';
import { PowerOfAttorney } from '@/lib/schemas/power-of-attorney';

export default function PowerOfAttorneysPage() {
  const router = useRouter();
  const [userData, setUserData] = useState<any>(null);
  const [powerOfAttorneys, setPowerOfAttorneys] = useState<PowerOfAttorney[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState<boolean>(false);

  // Fetch power of attorneys from API
  const fetchPowerOfAttorneys = async () => {
    try {
      setLoading(true);
      const data = await powerOfAttorneyService.getPowerOfAttorneys();
      setPowerOfAttorneys(data);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch power of attorneys:', err);
      setError('Vekaletnameler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch user data and power of attorneys on component mount
  useEffect(() => {
    // Check if user is authenticated
    if (!authService.isAuthenticated()) {
      router.push('/login');
      return;
    }

    const fetchData = async () => {
      try {
        // Get user data from local storage
        const user = getUserData();
        if (!user) {
          // Redirect to login if not authenticated
          router.push('/login');
          return;
        }
        setUserData(user);

        // Fetch power of attorneys
        await fetchPowerOfAttorneys();
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Veriler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [router]);

  // Handle form success
  const handleFormSuccess = () => {
    setCreateDialogOpen(false);
    fetchPowerOfAttorneys();
  };

  // Filter active power of attorneys (end date is in the future)
  const activePoAs = powerOfAttorneys.filter(poa => {
    if (!poa.endDate) return false;

    let endDate: Date;
    if (Array.isArray(poa.endDate)) {
      // Handle [year, month, day] format
      endDate = new Date(poa.endDate[0], poa.endDate[1] - 1, poa.endDate[2]);
    } else if (typeof poa.endDate === 'number') {
      // Handle epoch time
      endDate = new Date(poa.endDate);
    } else {
      // Handle string format (YYYY-MM-DD)
      endDate = new Date(poa.endDate);
    }

    return endDate > new Date();
  });

  // Filter expired power of attorneys (end date is in the past)
  const expiredPoAs = powerOfAttorneys.filter(poa => {
    if (!poa.endDate) return false;

    let endDate: Date;
    if (Array.isArray(poa.endDate)) {
      // Handle [year, month, day] format
      endDate = new Date(poa.endDate[0], poa.endDate[1] - 1, poa.endDate[2]);
    } else if (typeof poa.endDate === 'number') {
      // Handle epoch time
      endDate = new Date(poa.endDate);
    } else {
      // Handle string format (YYYY-MM-DD)
      endDate = new Date(poa.endDate);
    }

    return endDate <= new Date();
  });

  if (!userData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <DashboardLayout userName={userData.name} userSurname={userData.surname}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <FileSignature className="h-8 w-8" />
              Vekaletler
            </h1>
            <p className="text-muted-foreground">
              Tüm vekaletnamelerinizi yönetin ve takip edin
            </p>
          </div>
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Yeni Vekaletname
          </Button>
        </div>

        <Tabs defaultValue="all" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">
              Tümü ({powerOfAttorneys.length})
            </TabsTrigger>
            <TabsTrigger value="active">
              Aktif ({activePoAs.length})
            </TabsTrigger>
            <TabsTrigger value="expired">
              Süresi Dolmuş ({expiredPoAs.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            <PowerOfAttorneyList
              powerOfAttorneys={powerOfAttorneys}
              isLoading={loading}
              error={error}
              onRefresh={fetchPowerOfAttorneys}
            />
          </TabsContent>

          <TabsContent value="active" className="space-y-4">
            <PowerOfAttorneyList
              powerOfAttorneys={activePoAs}
              isLoading={loading}
              error={error}
              onRefresh={fetchPowerOfAttorneys}
            />
          </TabsContent>

          <TabsContent value="expired" className="space-y-4">
            <PowerOfAttorneyList
              powerOfAttorneys={expiredPoAs}
              isLoading={loading}
              error={error}
              onRefresh={fetchPowerOfAttorneys}
            />
          </TabsContent>
        </Tabs>
      </div>

      {/* Create Power of Attorney Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-auto p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle>Yeni Vekaletname</DialogTitle>
            <DialogDescription>
              Yeni bir vekaletname oluşturun
            </DialogDescription>
          </DialogHeader>
          <PowerOfAttorneyForm
            onSuccess={handleFormSuccess}
            onCancel={() => setCreateDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
}
