'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Calculator } from 'lucide-react';

import { getUserData } from '@/lib/auth';
import { authService } from '@/lib/api';
import { DashboardLayout } from '@/components/dashboard/layout';
import { SeverancePayForm } from '@/components/dashboard/severance-pay/form';
import { SeverancePayResults } from '@/components/dashboard/severance-pay/results';
import { type SeverancePayResponse } from '@/lib/schemas/severance-pay';

export default function SeverancePayPage() {
  const router = useRouter();
  const [userData, setUserData] = useState<any>(null);
  const [calculationResult, setCalculationResult] = useState<SeverancePayResponse | null>(null);

  useEffect(() => {
    // Check if user is authenticated
    if (!authService.isAuthenticated()) {
      router.push('/login');
      return;
    }

    // Get user data from local storage
    const data = getUserData();
    setUserData(data);
  }, [router]);

  const handleCalculationComplete = (result: SeverancePayResponse) => {
    setCalculationResult(result);
    // Scroll to results
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleReset = () => {
    setCalculationResult(null);
  };

  if (!userData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <DashboardLayout userName={userData.name} userSurname={userData.surname}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Kıdem Tazminatı Hesaplama</h1>
          <p className="text-muted-foreground mt-2">
            İş sözleşmesinin sona ermesi durumunda alacağınız tazminatı hesaplayın.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <div className="md:col-span-2 lg:col-span-1">
            {calculationResult ? (
              <SeverancePayResults 
                result={calculationResult} 
                onReset={handleReset} 
              />
            ) : (
              <SeverancePayForm onCalculationComplete={handleCalculationComplete} />
            )}
          </div>
          
          <div className="hidden lg:block">
            <div className="rounded-lg border bg-card p-6 h-full flex flex-col justify-center items-center text-center">
              <Calculator className="h-12 w-12 text-primary mb-4" />
              <h3 className="text-xl font-semibold mb-2">Kıdem Tazminatı Nedir?</h3>
              <p className="text-muted-foreground">
                Kıdem tazminatı, belirli koşullar altında iş sözleşmesi sona eren işçiye, işyerinde çalıştığı süreye 
                ve aldığı ücrete bağlı olarak işveren tarafından ödenen bir tazminattır.
              </p>
              <div className="mt-6 space-y-4 text-left w-full">
                <div>
                  <h4 className="font-medium">Kimler Alabilir?</h4>
                  <p className="text-sm text-muted-foreground">
                    En az 1 yıl çalışmış ve iş kanununda belirtilen şartlarla işten ayrılan çalışanlar.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium">Nasıl Hesaplanır?</h4>
                  <p className="text-sm text-muted-foreground">
                    Her tam yıl için 30 günlük brüt ücret tutarında hesaplanır. Bir yıldan artan süreler için de orantılı ödeme yapılır.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
