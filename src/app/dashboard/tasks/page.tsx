'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, CheckSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import { getUserData } from '@/lib/auth';
import { tasksService, authService } from '@/lib/api';
import { DashboardLayout } from '@/components/dashboard/layout';
import { TaskForm, TaskList } from '@/components/dashboard/tasks';
import { Task } from '@/lib/schemas/tasks';

export default function TasksPage() {
  const router = useRouter();
  const [userData, setUserData] = useState<any>(null);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState<boolean>(false);

  // Fetch tasks from API
  const fetchTasks = async () => {
    try {
      setLoading(true);
      const data = await tasksService.getTasks();
      setTasks(data);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch tasks:', err);
      setError('Görevler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch user data and tasks on component mount
  useEffect(() => {
    // Check if user is authenticated
    if (!authService.isAuthenticated()) {
      router.push('/login');
      return;
    }

    const fetchData = async () => {
      try {
        // Get user data from local storage
        const user = getUserData();
        if (!user) {
          // Redirect to login if not authenticated
          router.push('/login');
          return;
        }
        setUserData(user);

        // Fetch tasks
        await fetchTasks();
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Veriler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [router]);

  // Handle form success
  const handleFormSuccess = () => {
    setCreateDialogOpen(false);
    fetchTasks();
  };

  // Filter tasks by status
  const openTasks = tasks.filter(task => task.status === 'OPEN');
  const inProgressTasks = tasks.filter(task => task.status === 'IN_PROGRESS');
  const completedTasks = tasks.filter(task => task.status === 'COMPLETED');
  const cancelledTasks = tasks.filter(task => task.status === 'CANCELLED');

  return (
    <DashboardLayout userName={userData?.name} userSurname={userData?.surname}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <CheckSquare className="h-8 w-8" />
              Görevler
            </h1>
            <p className="text-muted-foreground">
              Tüm görevlerinizi yönetin ve takip edin
            </p>
          </div>
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Yeni Görev
          </Button>
        </div>

        <Tabs defaultValue="open" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="open">
              Açık ({openTasks.length})
            </TabsTrigger>
            <TabsTrigger value="in-progress">
              Devam Eden ({inProgressTasks.length})
            </TabsTrigger>
            <TabsTrigger value="completed">
              Tamamlanan ({completedTasks.length})
            </TabsTrigger>
            <TabsTrigger value="cancelled">
              İptal Edilen ({cancelledTasks.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="open" className="space-y-4">
            <TaskList
              tasks={openTasks}
              isLoading={loading}
              error={error}
              onRefresh={fetchTasks}
            />
          </TabsContent>

          <TabsContent value="in-progress" className="space-y-4">
            <TaskList
              tasks={inProgressTasks}
              isLoading={loading}
              error={error}
              onRefresh={fetchTasks}
            />
          </TabsContent>

          <TabsContent value="completed" className="space-y-4">
            <TaskList
              tasks={completedTasks}
              isLoading={loading}
              error={error}
              onRefresh={fetchTasks}
            />
          </TabsContent>

          <TabsContent value="cancelled" className="space-y-4">
            <TaskList
              tasks={cancelledTasks}
              isLoading={loading}
              error={error}
              onRefresh={fetchTasks}
            />
          </TabsContent>
        </Tabs>
      </div>

      {/* Create Task Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Yeni Görev</DialogTitle>
            <DialogDescription>
              Yeni bir görev oluşturun
            </DialogDescription>
          </DialogHeader>
          <TaskForm
            onSuccess={handleFormSuccess}
            onCancel={() => setCreateDialogOpen(false)}
            inDialog={true}
          />
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
}
