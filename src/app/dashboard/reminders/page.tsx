'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import { getUserData } from '@/lib/auth';
import { remindersService } from '@/lib/api';
import { DashboardLayout } from '@/components/dashboard/layout';
import { ReminderForm } from '@/components/dashboard/reminders/form';
import { ReminderList } from '@/components/dashboard/reminders/list';
import { Reminder } from '@/lib/schemas/reminders';

export default function RemindersPage() {
  const router = useRouter();
  const [userData, setUserData] = useState<any>(null);
  const [reminders, setReminders] = useState<Reminder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  // Fetch user data and reminders on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get user data from local storage
        const user = getUserData();
        if (!user) {
          // Redirect to login if not authenticated
          router.push('/login');
          return;
        }
        setUserData(user);

        // Fetch reminders
        await fetchReminders();
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Veriler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [router]);

  // Fetch reminders from API
  const fetchReminders = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await remindersService.getReminders();
      setReminders(data);
    } catch (error) {
      console.error('Error fetching reminders:', error);
      setError(error instanceof Error ? error.message : 'Hatırlatmalar yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // Handle form success
  const handleFormSuccess = (reminder: Reminder) => {
    setCreateDialogOpen(false);
    fetchReminders();
  };

  if (!userData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <DashboardLayout userName={userData.name} userSurname={userData.surname}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Hatırlatmalar</h1>
            <p className="text-muted-foreground">
              Önemli hatırlatmalarınızı yönetin
            </p>
          </div>
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Yeni Hatırlatma
          </Button>
        </div>



        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">Tüm Hatırlatmalar</TabsTrigger>
            <TabsTrigger value="upcoming">Yaklaşan</TabsTrigger>
            <TabsTrigger value="past">Geçmiş</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            <ReminderList
              reminders={reminders}
              isLoading={loading}
              error={error}
              onRefresh={fetchReminders}
            />
          </TabsContent>

          <TabsContent value="upcoming" className="space-y-4">
            <ReminderList
              reminders={reminders.filter(r => r.dueDate > Date.now())}
              isLoading={loading}
              error={error}
              onRefresh={fetchReminders}
            />
          </TabsContent>

          <TabsContent value="past" className="space-y-4">
            <ReminderList
              reminders={reminders.filter(r => r.dueDate <= Date.now())}
              isLoading={loading}
              error={error}
              onRefresh={fetchReminders}
            />
          </TabsContent>
        </Tabs>
      </div>

      {/* Create Reminder Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Yeni Hatırlatma</DialogTitle>
            <DialogDescription>
              Yeni bir hatırlatma oluşturun
            </DialogDescription>
          </DialogHeader>
          <ReminderForm
            onSuccess={handleFormSuccess}
            onCancel={() => setCreateDialogOpen(false)}
            inDialog={true}
          />
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
}
