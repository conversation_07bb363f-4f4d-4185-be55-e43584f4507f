'use client';

import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { useNotifications } from '@/contexts/notifications-context';

export default function NotificationsPage() {
  const {
    uyapNotifications,
    isLoading,
    error,
    refreshNotifications,
    markUyapNotificationAsRead: markAsRead
  } = useNotifications();

  // Format date from "Mar 24, 2025 9:35:41 AM" to a more readable format
  const formatDate = (dateString: string) => {
    try {
      // Parse the date string
      const date = new Date(dateString);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return dateString; // Return original if parsing fails
      }

      // Format the date
      return format(date, 'dd MMM yyyy HH:mm', { locale: tr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Bildirimler</h1>
        <p className="text-muted-foreground">
          Tüm bildirimlerinizi bu sayfadan yönetebilirsiniz.
        </p>
      </div>

      <Tabs defaultValue="uyap">
        <TabsList>
          <TabsTrigger value="uyap">UYAP Bildirimleri</TabsTrigger>
          <TabsTrigger value="app" disabled>Uygulama Bildirimleri</TabsTrigger>
        </TabsList>

        <TabsContent value="uyap" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>UYAP Bildirimleri</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">{error}</p>
                  <Button onClick={refreshNotifications}>Yeniden Dene</Button>
                </div>
              ) : uyapNotifications.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Bildirim bulunmamaktadır.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {uyapNotifications.map((notification) => (
                    <div
                      key={notification.bildirimId}
                      className={cn(
                        "flex items-start p-4 border rounded-lg",
                        !notification.okunduMu && "bg-accent/50"
                      )}
                    >
                      <Checkbox
                        id={`notification-${notification.bildirimId}`}
                        checked={notification.okunduMu}
                        onCheckedChange={() => {
                          if (!notification.okunduMu) {
                            markAsRead(notification.bildirimId);
                          }
                        }}
                        className="mt-1 mr-4"
                      />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{notification.baslik}</h3>
                            {!notification.okunduMu && (
                              <Badge variant="default">Yeni</Badge>
                            )}
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {formatDate(notification.gonderilmeTarihi)}
                          </span>
                        </div>
                        <p className="mt-1 text-sm">{notification.mesaj}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="app" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Uygulama Bildirimleri</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  Uygulama bildirimleri yakında kullanıma sunulacaktır.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
