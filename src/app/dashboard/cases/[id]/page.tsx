'use client';

import { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import {
  FolderOpen,
  Calendar,
  FileText,
  Info,
  FileBarChart,
  Users,
  Receipt,
  History,
  StickyNote,
  AlertCircle,
  ChevronLeft,
  Plus,
  Edit,
  Trash2,
  RefreshCw
} from 'lucide-react';
import { getUserData } from '@/lib/auth';
import { casesService, authService, caseNotesService, caseDetailsService, type Case, type CaseParty } from '@/lib/api';
import { DashboardLayout } from '@/components/dashboard/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { CaseNoteForm } from '@/components/dashboard/cases/note-form';
import { CaseNoteList } from '@/components/dashboard/cases/note-list';
import { CaseDetailsForm } from '@/components/dashboard/cases/details-form';
import { CasePartiesList } from '@/components/dashboard/cases/parties-list';
import { TahsilatReddiyat } from '@/components/dashboard/cases/tahsilat-reddiyat';
import { CaseNote } from '@/lib/schemas/case-notes';
import { CaseDetails, caseTypeDisplayMap, crimeTypeDisplayMap } from '@/lib/schemas/case-details';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

export default function CaseDetailsPage() {
  const router = useRouter();
  const params = useParams();
  // Decode the caseId to handle URL-encoded characters like '+' (%2B)
  const caseId = decodeURIComponent(params.id as string);

  const [userData, setUserData] = useState<any>(null);
  const [caseData, setCaseData] = useState<Case | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Case notes state
  const [caseNotes, setCaseNotes] = useState<CaseNote[]>([]);
  const [notesLoading, setNotesLoading] = useState<boolean>(false);
  const [notesError, setNotesError] = useState<string | null>(null);
  const [noteDialogOpen, setNoteDialogOpen] = useState<boolean>(false);
  const [selectedNote, setSelectedNote] = useState<CaseNote | null>(null);

  // Case details state
  const [caseDetails, setCaseDetails] = useState<CaseDetails | null>(null);
  const [detailsLoading, setDetailsLoading] = useState<boolean>(false);
  const [detailsError, setDetailsError] = useState<string | null>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState<boolean>(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);

  // Case parties state
  const [caseParties, setCaseParties] = useState<CaseParty[]>([]);
  const [partiesLoading, setPartiesLoading] = useState<boolean>(false);
  const [partiesError, setPartiesError] = useState<string | null>(null);

  useEffect(() => {
    // Check if user is authenticated
    if (!authService.isAuthenticated()) {
      router.push('/login');
      return;
    }

    // Get user data from local storage
    const data = getUserData();
    setUserData(data);

    // Fetch case data
    const fetchCaseData = async () => {
      try {
        setLoading(true);

        // Fetch active cases to find the one with matching ID
        const [activeData, _] = await casesService.getActiveCases();
        let foundCase = activeData.find(c => c.dosyaId === caseId);

        // If not found in active cases, check inactive cases
        if (!foundCase) {
          const [inactiveData, __] = await casesService.getInactiveCases();
          foundCase = inactiveData.find(c => c.dosyaId === caseId);
        }

        if (foundCase) {
          setCaseData(foundCase);
          setError(null);
        } else {
          setError('Dosya bulunamadı.');
        }
      } catch (err) {
        console.error('Failed to fetch case details:', err);
        setError('Dosya bilgileri yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
      } finally {
        setLoading(false);
      }
    };

    fetchCaseData();
  }, [caseId, router]);

  // Fetch case notes, details, and parties when case data is loaded
  useEffect(() => {
    if (caseData) {
      fetchCaseNotes();
      fetchCaseDetails();
      fetchCaseParties();
    }
  }, [caseData]);

  // Fetch case notes
  const fetchCaseNotes = async () => {
    if (!caseData) return;

    try {
      setNotesLoading(true);
      setNotesError(null);
      const notes = await caseNotesService.getCaseNotes(caseData.dosyaNo);
      setCaseNotes(notes);
    } catch (err) {
      console.error('Failed to fetch case notes:', err);
      setNotesError(err instanceof Error ? err.message : 'Notlar yüklenirken bir hata oluştu.');
    } finally {
      setNotesLoading(false);
    }
  };

  // Fetch case details
  const fetchCaseDetails = async () => {
    if (!caseData) return;

    try {
      setDetailsLoading(true);
      setDetailsError(null);
      const details = await caseDetailsService.getCaseDetailsByCaseNumber(caseData.dosyaNo);
      setCaseDetails(details);
    } catch (err) {
      console.error('Failed to fetch case details:', err);
      setDetailsError(err instanceof Error ? err.message : 'Dosya detayları yüklenirken bir hata oluştu.');
    } finally {
      setDetailsLoading(false);
    }
  };

  // Fetch case parties
  const fetchCaseParties = async () => {
    if (!caseData) return;

    try {
      setPartiesLoading(true);
      setPartiesError(null);
      const parties = await casesService.getCasePartiesByCaseNumber(caseData.dosyaNo);
      setCaseParties(parties);
    } catch (err) {
      console.error('Failed to fetch case parties:', err);
      setPartiesError(err instanceof Error ? err.message : 'Dosya tarafları yüklenirken bir hata oluştu.');
    } finally {
      setPartiesLoading(false);
    }
  };

  // Handle adding a new note
  const handleAddNote = () => {
    setSelectedNote(null);
    setNoteDialogOpen(true);
  };

  // Handle editing a note
  const handleEditNote = (note: CaseNote) => {
    setSelectedNote(note);
    setNoteDialogOpen(true);
  };

  // Handle note form success
  const handleNoteFormSuccess = (note: CaseNote) => {
    if (selectedNote) {
      // Update existing note in the list
      setCaseNotes(prevNotes =>
        prevNotes.map(n => n.id === note.id ? note : n)
      );
    } else {
      // Add new note to the list
      setCaseNotes(prevNotes => [note, ...prevNotes]);
    }
    setNoteDialogOpen(false);
  };

  // Handle note deletion
  const handleNoteDelete = (noteId: number) => {
    setCaseNotes(prevNotes => prevNotes.filter(note => note.id !== noteId));
  };

  // Handle adding or editing case details
  const handleManageCaseDetails = () => {
    setDetailsDialogOpen(true);
  };

  // Handle case details save success
  const handleDetailsSaveSuccess = (details: CaseDetails) => {
    setDetailsDialogOpen(false);
    setCaseDetails(details);
  };

  // Handle case details delete
  const handleDetailsDelete = async () => {
    if (!caseDetails) return;

    try {
      setDeleteDialogOpen(false);
      await caseDetailsService.deleteCaseDetails(caseDetails.id);
      setCaseDetails(null);
    } catch (err) {
      console.error('Failed to delete case details:', err);
      alert('Dosya detayları silinirken bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  // Format date for display
  const formatDate = (dateObj: any) => {
    try {
      if (!dateObj || !dateObj.date) return 'Tarih bilgisi yok';

      const { date, time } = dateObj;
      const { year, month, day } = date;
      const { hour, minute } = time;

      const dateString = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}T${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`;
      return format(parseISO(dateString), 'd MMMM yyyy, HH:mm', { locale: tr });
    } catch (error) {
      console.error('Date parsing error:', error);
      return 'Geçersiz tarih';
    }
  };

  // Get case type badge with color based on dosyaTurKod
  const getCaseTypeBadge = (caseType: string, caseTypeCode: number) => {
    // Color based on case type code
    const codeColors: Record<number, string> = {
      313: 'bg-blue-500', // İşçi İşveren Dava Şartı Arabuluculuk Dosyası
      // Add more case type codes and colors as needed
      100: 'bg-green-500',
      200: 'bg-purple-500',
      300: 'bg-yellow-500',
      400: 'bg-red-500',
    };

    const bgColor = codeColors[caseTypeCode] || 'bg-gray-500';

    return (
      <span className={`px-2 py-1 rounded-full text-white text-xs font-medium ${bgColor}`}>
        {caseType}
      </span>
    );
  };

  // Get status badge based on active/inactive status
  const getStatusBadge = (status: string, isActive: boolean) => {
    return (
      <Badge variant={isActive ? "success" : "secondary"}>
        {status}
      </Badge>
    );
  };

  if (!userData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <DashboardLayout userName={userData.name} userSurname={userData.surname}>
      <div className="space-y-4 sm:space-y-6">
        <div className="sticky top-0 z-30 flex items-center gap-2 bg-background pb-2 pt-1">
          <Button variant="outline" size="sm" asChild className="shadow-sm">
            <Link href="/dashboard/cases">
              <ChevronLeft className="h-4 w-4 mr-1" />
              Dosyalara Dön
            </Link>
          </Button>
          <h1 className="text-xl sm:text-3xl font-bold tracking-tight">Dosya Detayları</h1>
        </div>

        {loading ? (
          <div className="space-y-4">
            <Skeleton className="h-8 w-full max-w-md" />
            <Skeleton className="h-[500px] w-full rounded-md" />
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-[400px] bg-muted/30 rounded-lg">
            <div className="text-center space-y-4">
              <AlertCircle className="h-12 w-12 text-destructive mx-auto" />
              <p className="text-lg font-medium text-destructive">{error}</p>
              <Button variant="outline" asChild>
                <Link href="/dashboard/cases">Dosyalara Dön</Link>
              </Button>
            </div>
          </div>
        ) : caseData && (
          <>
            <div className="sticky top-[40px] z-20 flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 bg-background p-4 rounded-lg border shadow-sm mb-4">
              <div className="flex items-center gap-2">
                <FolderOpen className="h-5 w-5 text-primary" />
                <span className="font-semibold">Dosya No:</span>
                <span>{caseData.dosyaNo}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-semibold">Tür:</span>
                <span>{getCaseTypeBadge(caseData.dosyaTur, caseData.dosyaTurKod)}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-semibold">Durum:</span>
                <span>{getStatusBadge(caseData.dosyaDurum, caseData.dosyaDurumKod === 1)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-muted-foreground" />
                <span className="font-semibold">Açılış:</span>
                <span>{formatDate(caseData.dosyaAcilisTarihi)}</span>
              </div>
            </div>

            <Tabs defaultValue="details" className="w-full">
              <div className="sticky top-[72px] z-10 bg-background pb-2 shadow-sm mb-4 pt-1 rounded-md">
                <TabsList className="w-full grid grid-cols-3 sm:grid-cols-6">
                  <TabsTrigger value="details">Detaylar</TabsTrigger>
                  <TabsTrigger value="notes">Notlar</TabsTrigger>
                  <TabsTrigger value="report">Rapor</TabsTrigger>
                  <TabsTrigger value="parties">Taraflar</TabsTrigger>
                  <TabsTrigger value="payments">Tahsilat</TabsTrigger>
                  <TabsTrigger value="history">Safahat</TabsTrigger>
                </TabsList>
              </div>

              {/* Dosya Detayları Tab */}
              <TabsContent value="details">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                      <CardTitle>Dosya Detayları</CardTitle>
                      <CardDescription>
                        Dosya ile ilgili temel bilgiler
                      </CardDescription>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleManageCaseDetails}
                    >
                      {caseDetails ? <Edit className="h-4 w-4 mr-2" /> : <Plus className="h-4 w-4 mr-2" />}
                      {caseDetails ? 'Düzenle' : 'Ekle'}
                    </Button>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Basic Case Information */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="flex items-start space-x-2 bg-muted/30 p-3 rounded-md overflow-hidden">
                        <Calendar className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <div className="min-w-0">
                          <h3 className="text-sm font-semibold truncate">Açılış Tarihi</h3>
                          <p className="text-sm truncate">{formatDate(caseData.dosyaAcilisTarihi)}</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-2 bg-muted/30 p-3 rounded-md overflow-hidden">
                        <FileText className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <div className="min-w-0">
                          <h3 className="text-sm font-semibold truncate">Dosya No</h3>
                          <p className="text-sm truncate">{caseData.dosyaNo}</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-2 bg-muted/30 p-3 rounded-md overflow-hidden">
                        <Info className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <div className="min-w-0">
                          <h3 className="text-sm font-semibold truncate">Dosya Türü</h3>
                          <p className="text-sm truncate">{caseData.dosyaTur}</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-2 bg-muted/30 p-3 rounded-md overflow-hidden">
                        <Info className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <div className="min-w-0">
                          <h3 className="text-sm font-semibold truncate">Durum</h3>
                          <p className="text-sm truncate">{caseData.dosyaDurum}</p>
                        </div>
                      </div>
                    </div>

                    {/* Extended Case Details */}
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Detaylı Bilgiler</h3>
                      {detailsLoading ? (
                        <div className="space-y-2">
                          <Skeleton className="h-8 w-full" />
                          <Skeleton className="h-8 w-full" />
                          <Skeleton className="h-8 w-full" />
                        </div>
                      ) : detailsError ? (
                        <div className="bg-destructive/15 text-destructive text-sm p-4 rounded-lg">
                          <p>{detailsError}</p>
                        </div>
                      ) : caseDetails ? (
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div className="bg-muted/30 p-3 rounded-md">
                              <h4 className="text-sm font-semibold">Dosya Türü</h4>
                              <p className="text-sm">{caseTypeDisplayMap[caseDetails.caseType]}</p>
                            </div>

                            {caseDetails.crimeType && (
                              <div className="bg-muted/30 p-3 rounded-md">
                                <h4 className="text-sm font-semibold">Suç Türü</h4>
                                <p className="text-sm">{crimeTypeDisplayMap[caseDetails.crimeType]}</p>
                              </div>
                            )}

                            <div className="bg-muted/30 p-3 rounded-md">
                              <h4 className="text-sm font-semibold">Derdest</h4>
                              <p className="text-sm">{caseDetails.derdest ? 'Evet' : 'Hayır'}</p>
                            </div>

                            {caseDetails.caseValue !== undefined && (
                              <div className="bg-muted/30 p-3 rounded-md">
                                <h4 className="text-sm font-semibold">Dava Değeri</h4>
                                <p className="text-sm">{caseDetails.caseValue.toLocaleString('tr-TR')} ₺</p>
                              </div>
                            )}
                          </div>

                          {caseDetails.caseTitle && (
                            <div className="bg-muted/30 p-3 rounded-md">
                              <h4 className="text-sm font-semibold">Dava Başlığı</h4>
                              <p className="text-sm break-words">{caseDetails.caseTitle}</p>
                            </div>
                          )}

                          {caseDetails.caseReason && (
                            <div className="bg-muted/30 p-3 rounded-md">
                              <h4 className="text-sm font-semibold">Dava Nedeni</h4>
                              <p className="text-sm break-words">{caseDetails.caseReason}</p>
                            </div>
                          )}

                          <div className="flex justify-end">
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => setDeleteDialogOpen(true)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Sil
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="bg-muted/30 p-4 rounded-lg">
                          <p className="text-sm text-muted-foreground">
                            Bu dosya için henüz detaylı bilgi girilmemiş. Detaylı bilgi eklemek için "Ekle" butonuna tıklayın.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Notlar Tab */}
              <TabsContent value="notes">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div>
                      <CardTitle>Dosya Notları</CardTitle>
                      <CardDescription>
                        Dosya ile ilgili notlar
                      </CardDescription>
                    </div>
                    <Button size="sm" onClick={handleAddNote}>
                      <StickyNote className="h-4 w-4 mr-2" />
                      Yeni Not Ekle
                    </Button>
                  </CardHeader>
                  <CardContent>
                    {notesLoading ? (
                      <div className="space-y-4">
                        <Skeleton className="h-20 w-full" />
                        <Skeleton className="h-20 w-full" />
                        <Skeleton className="h-20 w-full" />
                      </div>
                    ) : notesError ? (
                      <div className="bg-destructive/15 text-destructive text-sm p-4 rounded-lg">
                        <p>{notesError}</p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={fetchCaseNotes}
                        >
                          Tekrar Dene
                        </Button>
                      </div>
                    ) : (
                      <CaseNoteList
                        notes={caseNotes}
                        onEdit={handleEditNote}
                        onDelete={handleNoteDelete}
                      />
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Dosya Rapor Tab */}
              <TabsContent value="report">
                <Card>
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      <FileBarChart className="h-5 w-5 text-primary" />
                      <CardTitle>Dosya Raporu</CardTitle>
                    </div>
                    <CardDescription>
                      Dosya ile ilgili özet bilgiler ve istatistikler
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="bg-muted/30 p-4 rounded-lg">
                      <p className="text-sm text-muted-foreground">
                        Dosya raporu API entegrasyonu tamamlandığında burada görüntülenecektir. Rapor, dosya ile ilgili özet bilgileri ve istatistikleri içerecektir.
                      </p>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <Card className="overflow-hidden">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium truncate">Dosya Özeti</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground line-clamp-3">API entegrasyonu sonrası veriler burada görüntülenecektir.</p>
                        </CardContent>
                      </Card>

                      <Card className="overflow-hidden">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium truncate">Duruşma Bilgileri</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground line-clamp-3">API entegrasyonu sonrası veriler burada görüntülenecektir.</p>
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Taraflar Tab */}
              <TabsContent value="parties">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div>
                      <div className="flex items-center space-x-2">
                        <Users className="h-5 w-5 text-primary" />
                        <CardTitle>Dosya Tarafları</CardTitle>
                      </div>
                      <CardDescription>
                        Dosya ile ilgili taraflar ve temsilciler
                      </CardDescription>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={fetchCaseParties}
                      disabled={partiesLoading}
                    >
                      <RefreshCw className={`h-4 w-4 mr-2 ${partiesLoading ? 'animate-spin' : ''}`} />
                      Yenile
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <CasePartiesList
                      parties={caseParties}
                      isLoading={partiesLoading}
                      error={partiesError}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Tahsilat Reddiyat Tab */}
              <TabsContent value="payments">
                <Card>
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      <Receipt className="h-5 w-5 text-primary" />
                      <CardTitle>Tahsilat ve Reddiyat</CardTitle>
                    </div>
                    <CardDescription>
                      Dosya ile ilgili finansal işlemler
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {caseData && (
                      <TahsilatReddiyat caseNumber={caseData.dosyaNo} />
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Safahat Tab */}
              <TabsContent value="history">
                <Card>
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      <History className="h-5 w-5 text-primary" />
                      <CardTitle>Dosya Safahatı</CardTitle>
                    </div>
                    <CardDescription>
                      Dosya ile ilgili geçmiş işlemler ve süreçler
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-muted/30 p-4 rounded-lg mb-6">
                      <p className="text-sm text-muted-foreground">
                        Dosya safahatı API entegrasyonu tamamlandığında burada görüntülenecektir. Safahat, dosyanın geçmiş işlemlerini ve süreçlerini içerecektir.
                      </p>
                    </div>

                    <div className="relative pl-6 border-l-2 border-muted-foreground/20 space-y-6">
                      <div className="relative">
                        <div className="absolute -left-[29px] p-1 rounded-full bg-primary">
                          <div className="h-2 w-2 rounded-full bg-primary-foreground"></div>
                        </div>
                        <div className="bg-card p-4 rounded-lg shadow-sm overflow-hidden">
                          <p className="text-sm font-medium truncate">Dosya Açılış</p>
                          <p className="text-xs text-muted-foreground mt-1 truncate">{formatDate(caseData.dosyaAcilisTarihi)}</p>
                          <p className="text-sm mt-2 line-clamp-2">Dosya açılış işlemi gerçekleştirildi.</p>
                        </div>
                      </div>

                      <div className="relative">
                        <div className="absolute -left-[29px] p-1 rounded-full bg-muted">
                          <div className="h-2 w-2 rounded-full bg-muted-foreground"></div>
                        </div>
                        <div className="bg-card p-4 rounded-lg shadow-sm overflow-hidden">
                          <p className="text-sm font-medium truncate">API Entegrasyonu Bekleniyor</p>
                          <p className="text-xs text-muted-foreground mt-1 truncate">Tarih bilgisi yok</p>
                          <p className="text-sm mt-2 line-clamp-2">API entegrasyonu tamamlandığında gerçek veriler burada görüntülenecektir.</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>

      {/* Note Dialog */}
      <Dialog open={noteDialogOpen} onOpenChange={setNoteDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{selectedNote ? 'Notu Düzenle' : 'Yeni Not Ekle'}</DialogTitle>
            <DialogDescription>
              {caseData?.dosyaNo} numaralı dosyaya {selectedNote ? 'not düzenle' : 'yeni not ekle'}
            </DialogDescription>
          </DialogHeader>
          {caseData && (
            <CaseNoteForm
              caseNumber={caseData.dosyaNo}
              note={selectedNote || undefined}
              onSuccess={handleNoteFormSuccess}
              onCancel={() => setNoteDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Case Details Dialog */}
      <Dialog open={detailsDialogOpen} onOpenChange={(open) => {
        // Only allow closing if not loading
        setDetailsDialogOpen(open);
        console.log("Dialog state changed:", open);
      }}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>{caseDetails ? 'Dosya Detaylarını Düzenle' : 'Dosya Detayları Ekle'}</DialogTitle>
            <DialogDescription>
              {caseData?.dosyaNo} numaralı dosyaya detaylı bilgi {caseDetails ? 'düzenle' : 'ekle'}
            </DialogDescription>
          </DialogHeader>
          {caseData && (
            <CaseDetailsForm
              caseNumber={caseData.dosyaNo}
              caseDetails={caseDetails || undefined}
              onSuccess={(details) => {
                console.log("Form submission successful:", details);
                handleDetailsSaveSuccess(details);
              }}
              onCancel={() => {
                console.log("Form cancelled");
                setDetailsDialogOpen(false);
              }}
              inDialog={true}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Dosya detaylarını silmek istediğinizden emin misiniz?</AlertDialogTitle>
            <AlertDialogDescription>
              Bu işlem geri alınamaz. Dosya detayları kalıcı olarak silinecektir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction onClick={handleDetailsDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </DashboardLayout>
  );
}
