'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import { FolderOpen, Calendar, Info } from 'lucide-react';
import { getUserData } from '@/lib/auth';
import { casesService, authService, type Case } from '@/lib/api';
import { DashboardLayout } from '@/components/dashboard/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function CasesPage() {
  const router = useRouter();
  const [userData, setUserData] = useState<any>(null);
  const [activeCases, setActiveCases] = useState<Case[]>([]);
  const [inactiveCases, setInactiveCases] = useState<Case[]>([]);
  const [totalActiveCases, setTotalActiveCases] = useState<number>(0);
  const [totalInactiveCases, setTotalInactiveCases] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if user is authenticated
    if (!authService.isAuthenticated()) {
      router.push('/login');
      return;
    }

    // Get user data from local storage
    const data = getUserData();
    setUserData(data);

    // Fetch cases
    const fetchCases = async () => {
      try {
        setLoading(true);

        // Fetch active cases
        const [activeData, activeTotal] = await casesService.getActiveCases();
        setActiveCases(activeData);
        setTotalActiveCases(activeTotal);

        // Fetch inactive cases
        const [inactiveData, inactiveTotal] = await casesService.getInactiveCases();
        setInactiveCases(inactiveData);
        setTotalInactiveCases(inactiveTotal);

        setError(null);
      } catch (err) {
        console.error('Failed to fetch cases:', err);
        setError('Dosya bilgileri yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
      } finally {
        setLoading(false);
      }
    };

    fetchCases();
  }, [router]);

  // Format date for display
  const formatDate = (dateObj: any) => {
    try {
      if (!dateObj || !dateObj.date) return 'Tarih bilgisi yok';

      const { date, time } = dateObj;
      const { year, month, day } = date;
      const { hour, minute } = time;

      const dateString = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}T${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`;
      return format(parseISO(dateString), 'd MMMM yyyy, HH:mm', { locale: tr });
    } catch (error) {
      console.error('Date parsing error:', error);
      return 'Geçersiz tarih';
    }
  };

  // Get case type badge with color based on dosyaTurKod
  const getCaseTypeBadge = (caseType: string, caseTypeCode: number) => {
    // Color based on case type code
    const codeColors: Record<number, string> = {
      313: 'bg-blue-500', // İşçi İşveren Dava Şartı Arabuluculuk Dosyası
      // Add more case type codes and colors as needed
      // This is just an example, you can customize based on your needs
      100: 'bg-green-500',
      200: 'bg-purple-500',
      300: 'bg-yellow-500',
      400: 'bg-red-500',
    };

    const bgColor = codeColors[caseTypeCode] || 'bg-gray-500';

    return (
      <span className={`px-2 py-1 rounded-full text-white text-xs font-medium ${bgColor}`}>
        {caseType}
      </span>
    );
  };

  // Get status badge based on active/inactive status
  const getStatusBadge = (status: string, isActive: boolean) => {
    return (
      <Badge variant={isActive ? "success" : "secondary"}>
        {status}
      </Badge>
    );
  };

  if (!userData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  const renderCasesTable = (cases: Case[], isActive: boolean) => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-[200px]">
          <p>Dosya bilgileri yükleniyor...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-[200px] text-destructive">
          <p>{error}</p>
        </div>
      );
    }

    if (cases.length === 0) {
      return (
        <div className="flex items-center justify-center h-[200px]">
          <p>Görüntülenecek dosya bulunamadı.</p>
        </div>
      );
    }

    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Dosya No</TableHead>
            <TableHead>Dosya Türü</TableHead>
            <TableHead>Açılış Tarihi</TableHead>
            <TableHead>Durum</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {cases.map((caseItem) => (
            <TableRow
              key={caseItem.dosyaId}
              className="cursor-pointer hover:bg-muted hover:shadow-sm transition-all"
              onClick={() => router.push(`/dashboard/cases/${encodeURIComponent(caseItem.dosyaId)}`)}
              title="Detayları görmek için tıklayın"
            >
              <TableCell className="font-medium">{caseItem.dosyaNo}</TableCell>
              <TableCell>{getCaseTypeBadge(caseItem.dosyaTur, caseItem.dosyaTurKod)}</TableCell>
              <TableCell>{formatDate(caseItem.dosyaAcilisTarihi)}</TableCell>
              <TableCell>{getStatusBadge(caseItem.dosyaDurum, isActive)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  return (
    <DashboardLayout userName={userData.name} userSurname={userData.surname}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dosyalar</h1>
          <p className="text-muted-foreground mt-2">
            Tüm aktif ve kapalı dosyalarınızı görüntüleyin ve yönetin.
          </p>
        </div>

        <Tabs defaultValue="active" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="active">
              Aktif Dosyalar ({totalActiveCases})
            </TabsTrigger>
            <TabsTrigger value="inactive">
              Kapalı Dosyalar ({totalInactiveCases})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="active">
            <Card>
              <CardHeader>
                <CardTitle>Aktif Dosyalar</CardTitle>
                <CardDescription>
                  Tüm aktif dosyalarınızın listesi. Detayları görmek için bir satıra tıklayın.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {renderCasesTable(activeCases, true)}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="inactive">
            <Card>
              <CardHeader>
                <CardTitle>Kapalı Dosyalar</CardTitle>
                <CardDescription>
                  Tüm kapalı dosyalarınızın listesi. Detayları görmek için bir satıra tıklayın.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {renderCasesTable(inactiveCases, false)}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>


      </div>
    </DashboardLayout>
  );
}
