'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { TrendingUp, Gavel, FolderOpen, FileBarChart, User, Calendar, Mail, Phone, CreditCard, CheckCircle, AlertCircle, ClipboardList } from 'lucide-react';
import { format, parseISO, formatDistance, isBefore, isToday, isTomorrow } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { getUserData } from '@/lib/auth';
import { authService, trialsService, casesService, userReportService, type Trial, type UserReport } from '@/lib/api';
import { DashboardLayout } from '@/components/dashboard/layout';
import { StatCard } from '@/components/dashboard/stat-card';
import { UserAvatar } from '@/components/user/user-avatar';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

export default function DashboardPage() {
  const router = useRouter();
  const [userData, setUserData] = useState<any>(null);
  const [trials, setTrials] = useState<Trial[]>([]);
  const [activeCases, setActiveCases] = useState<number>(0);
  const [inactiveCases, setInactiveCases] = useState<number>(0);
  const [userReport, setUserReport] = useState<UserReport | null>(null);
  const [reportDialogOpen, setReportDialogOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [casesLoading, setCasesLoading] = useState<boolean>(true);
  const [reportLoading, setReportLoading] = useState<boolean>(true);

  useEffect(() => {
    // Check if user is authenticated
    if (!authService.isAuthenticated()) {
      router.push('/login');
      return;
    }

    // Get user data from local storage
    const data = getUserData();
    setUserData(data);

    // Fetch trials data
    const fetchTrials = async () => {
      try {
        setLoading(true);
        const trialsData = await trialsService.getTrials();

        // Sort trials by date
        const sortedTrials = [...trialsData].sort((a, b) => {
          const dateA = new Date(a.tarihSaat);
          const dateB = new Date(b.tarihSaat);
          return dateA.getTime() - dateB.getTime();
        });

        setTrials(sortedTrials);
      } catch (error) {
        console.error('Failed to fetch trials:', error);
      } finally {
        setLoading(false);
      }
    };

    // Fetch cases data
    const fetchCases = async () => {
      try {
        setCasesLoading(true);

        // Fetch active cases
        const [_, activeTotal] = await casesService.getActiveCases();
        setActiveCases(activeTotal);

        // Fetch inactive cases
        const [__, inactiveTotal] = await casesService.getInactiveCases();
        setInactiveCases(inactiveTotal);
      } catch (error) {
        console.error('Failed to fetch cases:', error);
      } finally {
        setCasesLoading(false);
      }
    };

    // Fetch user report data
    const fetchUserReport = async () => {
      try {
        setReportLoading(true);
        const reportData = await userReportService.getUserReport();
        setUserReport(reportData);
      } catch (error) {
        console.error('Failed to fetch user report:', error);
      } finally {
        setReportLoading(false);
      }
    };

    fetchTrials();
    fetchCases();
    fetchUserReport();
  }, [router]);

  // Helper function to get a human-readable description of the next trial
  const getNextTrialDescription = (trial: Trial) => {
    if (!trial) return "Yaklaşan duruşma bulunmuyor";

    const trialDate = parseISO(trial.tarihSaat);
    const now = new Date();

    // Check if the trial is today
    if (isToday(trialDate)) {
      return `Bugün: ${trial.dosyaNo} - ${format(trialDate, 'HH:mm', { locale: tr })}`;
    }

    // Check if the trial is tomorrow
    if (isTomorrow(trialDate)) {
      return `Yarın: ${trial.dosyaNo} - ${format(trialDate, 'HH:mm', { locale: tr })}`;
    }

    // Check if the trial is in the past
    if (isBefore(trialDate, now)) {
      return `Geçmiş: ${trial.dosyaNo} - ${format(trialDate, 'd MMM yyyy', { locale: tr })}`;
    }

    // For future dates, show relative time
    const relativeTime = formatDistance(trialDate, now, {
      addSuffix: true,
      locale: tr
    });

    return `${relativeTime.charAt(0).toUpperCase() + relativeTime.slice(1)}: ${trial.dosyaNo}`;
  };

  if (!userData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <DashboardLayout userName={userData.name} userSurname={userData.surname}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {userData.name}! Here's an overview of your account.
          </p>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Aktif Dosyalar"
            value={casesLoading ? "..." : activeCases.toString()}
            icon={<FolderOpen className="h-5 w-5" />}
            description="Devam eden dosyalar"
          />
          <StatCard
            title="Kapalı Dosyalar"
            value={casesLoading ? "..." : inactiveCases.toString()}
            icon={<FolderOpen className="h-5 w-5" />}
            description="Sonuçlanmış dosyalar"
          />
          <StatCard
            title="Duruşmalar"
            value={loading ? "..." : trials.length.toString()}
            icon={<Gavel className="h-5 w-5" />}
            description={trials.length > 0
              ? getNextTrialDescription(trials[0])
              : "Yaklaşan duruşma bulunmuyor"}
          />
          <button
            className="p-0 border-0 bg-transparent w-full text-left"
            onClick={() => setReportDialogOpen(true)}
            title="Detaylı raporu görmek için tıklayın"
          >
            <StatCard
              title="Kullanıcı Raporu"
              value={reportLoading ? "..." : userReport?.taskSummary.totalTasks.toString() ?? "0"}
              icon={<FileBarChart className="h-5 w-5" />}
              description="Detaylı rapor için tıklayın"
            />
          </button>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card className="col-span-2">
            <CardHeader>
              <CardTitle>Performance Overview</CardTitle>
              <CardDescription>
                Monthly performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] flex items-center justify-center border rounded-md">
                <TrendingUp className="h-8 w-8 text-muted-foreground" />
                <span className="ml-2 text-muted-foreground">Chart placeholder</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center space-y-4">
                <UserAvatar
                  userName={userData.name}
                  userSurname={userData.surname}
                  size="xl"
                  className="mb-2"
                />
                <div className="w-full space-y-4">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Name</p>
                    <p className="text-sm text-muted-foreground">{userData.name} {userData.surname}</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">User ID</p>
                    <p className="text-sm text-muted-foreground">{userData.id}</p>
                  </div>
                  {userData.isNewUser && (
                    <p className="text-sm text-blue-500">Welcome! You are a new user.</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* User Report Dialog */}
        <Dialog open={reportDialogOpen} onOpenChange={setReportDialogOpen}>
          {userReport && (
            <DialogContent className="w-[95vw] max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Kullanıcı Raporu</DialogTitle>
                <DialogDescription className="line-clamp-2">
                  {userReport.fullName} - Rapor Tarihi: {userReport.reportGeneratedAt[0]}-{userReport.reportGeneratedAt[1]}-{userReport.reportGeneratedAt[2]}
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-6 py-2 sm:py-4">
                {/* User Profile Section */}
                <div className="space-y-3">
                  <h3 className="text-base sm:text-lg font-semibold">Kullanıcı Bilgileri</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    <div className="flex items-start space-x-2">
                      <User className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                      <div className="min-w-0">
                        <h4 className="font-semibold text-sm sm:text-base">Ad Soyad</h4>
                        <p className="text-sm sm:text-base break-words">{userReport.fullName}</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <Mail className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                      <div className="min-w-0">
                        <h4 className="font-semibold text-sm sm:text-base">E-posta</h4>
                        <p className="text-sm sm:text-base break-words">{userReport.email}</p>
                        <Badge variant={userReport.isEmailVerified ? "success" : "destructive"} className="mt-1">
                          {userReport.isEmailVerified ? "Doğrulanmış" : "Doğrulanmamış"}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <Phone className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                      <div className="min-w-0">
                        <h4 className="font-semibold text-sm sm:text-base">Telefon</h4>
                        <p className="text-sm sm:text-base break-words">{userReport.mobilePhone}</p>
                        <Badge variant={userReport.isMobilePhoneVerified ? "success" : "destructive"} className="mt-1">
                          {userReport.isMobilePhoneVerified ? "Doğrulanmış" : "Doğrulanmamış"}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <Calendar className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                      <div className="min-w-0">
                        <h4 className="font-semibold text-sm sm:text-base">Doğum Tarihi</h4>
                        <p className="text-sm sm:text-base">{userReport.birthDate[0]}-{userReport.birthDate[1]}-{userReport.birthDate[2]}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* UYAP Details Section */}
                <div className="space-y-3">
                  <h3 className="text-base sm:text-lg font-semibold">UYAP Bilgileri</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    <div className="flex items-start space-x-2">
                      <User className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                      <div className="min-w-0">
                        <h4 className="font-semibold text-sm sm:text-base">TC Kimlik No</h4>
                        <p className="text-sm sm:text-base">{userReport.uyapDetails.details.tcKimlikNo}</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <CheckCircle className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                      <div className="min-w-0">
                        <h4 className="font-semibold text-sm sm:text-base">Statüler</h4>
                        <div className="flex flex-wrap gap-1 sm:gap-2 mt-1">
                          <Badge variant={userReport.uyapDetails.details.bilirkisiMi ? "default" : "outline"} className="text-xs">
                            Bilirkişi: {userReport.uyapDetails.details.bilirkisiMi ? "Evet" : "Hayır"}
                          </Badge>
                          <Badge variant={userReport.uyapDetails.details.arabulucuMu ? "default" : "outline"} className="text-xs">
                            Arabulucu: {userReport.uyapDetails.details.arabulucuMu ? "Evet" : "Hayır"}
                          </Badge>
                          <Badge variant={userReport.uyapDetails.details.uzlastirmaciMi ? "default" : "outline"} className="text-xs">
                            Uzlaştırmacı: {userReport.uyapDetails.details.uzlastirmaciMi ? "Evet" : "Hayır"}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Financial Summary Section */}
                <div className="space-y-3">
                  <h3 className="text-base sm:text-lg font-semibold">Finansal Özet</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4">
                    <div className="flex flex-col p-3 sm:p-4 border rounded-md">
                      <h4 className="font-semibold text-center text-sm sm:text-base">Toplam Gelir</h4>
                      <p className="text-lg sm:text-xl font-bold text-center text-green-600">
                        {userReport.financialSummary.totalIncome.toLocaleString('tr-TR')} ₺
                      </p>
                    </div>
                    <div className="flex flex-col p-3 sm:p-4 border rounded-md">
                      <h4 className="font-semibold text-center text-sm sm:text-base">Toplam Gider</h4>
                      <p className="text-lg sm:text-xl font-bold text-center text-red-600">
                        {userReport.financialSummary.totalExpenses.toLocaleString('tr-TR')} ₺
                      </p>
                    </div>
                    <div className="flex flex-col p-3 sm:p-4 border rounded-md">
                      <h4 className="font-semibold text-center text-sm sm:text-base">Net Gelir</h4>
                      <p className={`text-lg sm:text-xl font-bold text-center ${userReport.financialSummary.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {userReport.financialSummary.netIncome.toLocaleString('tr-TR')} ₺
                      </p>
                    </div>
                  </div>
                </div>

                {/* Task Summary Section */}
                <div className="space-y-3">
                  <h3 className="text-base sm:text-lg font-semibold">Görev Özeti</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    <div className="space-y-3 sm:space-y-4">
                      <h4 className="font-medium text-sm sm:text-base">Durum Dağılımı</h4>
                      <div className="space-y-2 sm:space-y-3">
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs sm:text-sm">
                            <span>Açık</span>
                            <span>{userReport.taskSummary.taskCountsByStatus.OPEN} / {userReport.taskSummary.totalTasks}</span>
                          </div>
                          <Progress value={(userReport.taskSummary.taskCountsByStatus.OPEN / userReport.taskSummary.totalTasks) * 100} className="h-2 bg-muted" />
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs sm:text-sm">
                            <span>Devam Eden</span>
                            <span>{userReport.taskSummary.taskCountsByStatus.IN_PROGRESS} / {userReport.taskSummary.totalTasks}</span>
                          </div>
                          <Progress value={(userReport.taskSummary.taskCountsByStatus.IN_PROGRESS / userReport.taskSummary.totalTasks) * 100} className="h-2 bg-muted" />
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs sm:text-sm">
                            <span>Tamamlanmış</span>
                            <span>{userReport.taskSummary.taskCountsByStatus.COMPLETED} / {userReport.taskSummary.totalTasks}</span>
                          </div>
                          <Progress value={(userReport.taskSummary.taskCountsByStatus.COMPLETED / userReport.taskSummary.totalTasks) * 100} className="h-2 bg-muted" />
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs sm:text-sm">
                            <span>İptal Edilmiş</span>
                            <span>{userReport.taskSummary.taskCountsByStatus.CANCELLED} / {userReport.taskSummary.totalTasks}</span>
                          </div>
                          <Progress value={(userReport.taskSummary.taskCountsByStatus.CANCELLED / userReport.taskSummary.totalTasks) * 100} className="h-2 bg-muted" />
                        </div>
                      </div>
                    </div>
                    <div className="space-y-3 sm:space-y-4">
                      <h4 className="font-medium text-sm sm:text-base">Öncelik Dağılımı</h4>
                      <div className="space-y-2 sm:space-y-3">
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs sm:text-sm">
                            <span>Düşük</span>
                            <span>{userReport.taskSummary.taskCountsByPriority.LOW} / {userReport.taskSummary.totalTasks}</span>
                          </div>
                          <Progress value={(userReport.taskSummary.taskCountsByPriority.LOW / userReport.taskSummary.totalTasks) * 100} className="h-2 bg-muted" />
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs sm:text-sm">
                            <span>Orta</span>
                            <span>{userReport.taskSummary.taskCountsByPriority.MEDIUM} / {userReport.taskSummary.totalTasks}</span>
                          </div>
                          <Progress value={(userReport.taskSummary.taskCountsByPriority.MEDIUM / userReport.taskSummary.totalTasks) * 100} className="h-2 bg-muted" />
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs sm:text-sm">
                            <span>Yüksek</span>
                            <span>{userReport.taskSummary.taskCountsByPriority.HIGH} / {userReport.taskSummary.totalTasks}</span>
                          </div>
                          <Progress value={(userReport.taskSummary.taskCountsByPriority.HIGH / userReport.taskSummary.totalTasks) * 100} className="h-2 bg-muted" />
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs sm:text-sm">
                            <span>Kritik</span>
                            <span>{userReport.taskSummary.taskCountsByPriority.CRITICAL} / {userReport.taskSummary.totalTasks}</span>
                          </div>
                          <Progress value={(userReport.taskSummary.taskCountsByPriority.CRITICAL / userReport.taskSummary.totalTasks) * 100} className="h-2 bg-muted" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end mt-2">
                <Button variant="outline" onClick={() => setReportDialogOpen(false)}>
                  Kapat
                </Button>
              </div>
            </DialogContent>
          )}
        </Dialog>
      </div>
    </DashboardLayout>
  );
}
