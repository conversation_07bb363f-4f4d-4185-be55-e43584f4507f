/**
 * Test script for User Reports functionality
 * This script tests the admin user reports API integration
 */

const axios = require('axios');

const API_URL = 'http://*************:4244';
const TEST_EMAIL = '<EMAIL>'; // Replace with actual test credentials
const TEST_PASSWORD = 'testpassword'; // Replace with actual test credentials

async function testUserReports() {
  console.log('🧪 Testing User Reports Functionality...\n');

  try {
    // Step 1: Login to get JWT token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${API_URL}/auth/user/login`, {
      email: TEST_EMAIL,
      password: TEST_PASSWORD
    });

    if (!loginResponse.data.jwt) {
      throw new Error('Login failed - no JWT token received');
    }

    const token = loginResponse.data.jwt;
    console.log('✅ Login successful');

    // Step 2: Test user reports API
    console.log('\n2. Testing user reports API...');
    const userReportsResponse = await axios.get(`${API_URL}/api/admin/users/reports`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ User reports API call successful');
    console.log(`📊 Retrieved ${userReportsResponse.data.length} user reports`);

    // Step 3: Validate response structure
    console.log('\n3. Validating response structure...');
    if (!Array.isArray(userReportsResponse.data)) {
      throw new Error('Response is not an array');
    }

    if (userReportsResponse.data.length > 0) {
      const firstUser = userReportsResponse.data[0];
      const requiredFields = ['userId', 'createdAt', 'roles', 'subscriptionLevel', 'totalPaymentAmount', 'newUser'];
      
      for (const field of requiredFields) {
        if (!(field in firstUser)) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      console.log('✅ Response structure is valid');
      console.log('\n📋 Sample user data:');
      console.log(`   User ID: ${firstUser.userId}`);
      console.log(`   Created At: ${new Date(firstUser.createdAt * 1000).toLocaleString()}`);
      console.log(`   Roles: ${firstUser.roles.join(', ') || 'None'}`);
      console.log(`   Subscription: ${firstUser.subscriptionLevel || 'None'}`);
      console.log(`   Total Payment: ${firstUser.totalPaymentAmount} TRY`);
      console.log(`   New User: ${firstUser.newUser ? 'Yes' : 'No'}`);
    } else {
      console.log('⚠️  No user data returned (empty array)');
    }

    console.log('\n🎉 All tests passed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:');
    
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Message: ${error.response.data?.responseMessage || error.response.data?.error || error.message}`);
    } else {
      console.error(`   Error: ${error.message}`);
    }
    
    process.exit(1);
  }
}

// Run the test
testUserReports();
