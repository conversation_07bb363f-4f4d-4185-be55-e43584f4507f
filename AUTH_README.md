# Authentication System for AVAS

This document provides an overview of the authentication system implemented in the AVAS Next.js frontend.

## Overview

The authentication system uses JWT (JSON Web Tokens) for secure authentication with the backend API. It includes:

- Login page with form validation
- Secure token storage
- Protected routes
- "Remember me" functionality
- Error handling

## Files Structure

- `src/app/login/page.tsx` - Login page component
- `src/app/dashboard/page.tsx` - Dashboard page (protected route)
- `src/lib/api.ts` - API service for backend communication
- `src/lib/auth.ts` - Authentication utilities
- `src/lib/schemas.ts` - Zod validation schemas
- `src/middleware.ts` - Route protection middleware
- `.env.local` - Environment variables

## Authentication Flow

1. User enters credentials on the login page
2. Form validation checks the input
3. Credentials are sent to the backend API
4. On successful login, JWT token is stored securely
5. User is redirected to the dashboard
6. Protected routes check for valid authentication

## Environment Variables

The following environment variables are used:

- `NEXT_PUBLIC_API_URL` - Backend API URL
- `NEXT_PUBLIC_API_LOGIN_ENDPOINT` - Login endpoint path

## API Integration

The login endpoint is: `/auth/user/login`

The login request expects:

```json
{
  "password": "mypassword",
  "email": "<EMAIL>"
}
```

And returns:

```json
{
  "responseMessage": "string",
  "timestamp": "2025-05-14T22:43:19.483Z",
  "id": 0,
  "name": "string",
  "surname": "string",
  "isNewUser": true,
  "isDeleted": true,
  "jwt": "string"
}
```

## Security Considerations

- JWT tokens are stored in both localStorage and cookies
- Cookies are set with secure flags in production
- Middleware protects routes from unauthorized access
- Form validation prevents malicious input

## Usage

To use the authentication system:

1. Configure the backend API URL in `.env.local`
2. Start the development server with `npm run dev`
3. Navigate to `/login` to test the login functionality

## Extending the System

To add registration functionality:

1. Create a registration form component
2. Add API service methods for registration
3. Update the middleware to include the registration route
4. Link to the registration page from the login page
