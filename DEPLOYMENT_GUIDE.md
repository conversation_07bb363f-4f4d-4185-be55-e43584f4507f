# Deployment Guide: Hosting Next.js App with Nginx and SSL

This guide will help you deploy your Next.js application on a server with Nginx as a reverse proxy and SSL certificates from Let's Encrypt for the domain `api4js.com.tr`.

## Prerequisites

- Ubuntu/Debian server with root access
- Domain `api4js.com.tr` pointing to your server's IP address
- Node.js 18+ installed
- Git installed

## Quick Setup

If you want to get started quickly, run these commands:

```bash
# Make scripts executable
chmod +x scripts/*.sh

# Run the complete setup (requires root)
sudo ./scripts/setup-ssl.sh
```

## Detailed Setup Instructions

### 1. Server Preparation

Update your system and install required packages:

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y nginx nodejs npm git curl ufw

# Install Node.js 18+ (if not already installed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 2. Configure Firewall

```bash
# Allow SSH, HTTP, and HTTPS
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 3. Domain Configuration

Ensure your domain `api4js.com.tr` and `www.api4js.com.tr` point to your server's IP address:

```bash
# Check DNS resolution
nslookup api4js.com.tr
nslookup www.api4js.com.tr
```

### 4. Install and Configure Nginx

```bash
# Install Nginx
sudo apt install -y nginx

# Copy the Nginx configuration
sudo cp nginx/api4js.com.tr.conf /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/api4js.com.tr.conf /etc/nginx/sites-enabled/

# Remove default site
sudo rm -f /etc/nginx/sites-enabled/default

# Test configuration
sudo nginx -t

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 5. SSL Certificate Setup

**Important**: Before running the SSL setup, make sure your domain is pointing to your server.

```bash
# Make the SSL setup script executable
chmod +x scripts/setup-ssl.sh

# Edit the script to add your email address
nano scripts/setup-ssl.sh
# Change: EMAIL="<EMAIL>" to your actual email

# Run SSL setup (requires root)
sudo ./scripts/setup-ssl.sh
```

### 6. Application Setup

```bash
# Create application directory
sudo mkdir -p /var/www/api4js.com.tr
sudo chown -R $USER:$USER /var/www/api4js.com.tr

# Copy your application files
cp -r . /var/www/api4js.com.tr/
cd /var/www/api4js.com.tr

# Install dependencies and build
npm install --legacy-peer-deps
npm run build
```

### 7. Create Systemd Service

```bash
# Copy the service file
sudo cp systemd/hukapp-nextjs.service /etc/systemd/system/

# Reload systemd and start the service
sudo systemctl daemon-reload
sudo systemctl enable hukapp-nextjs
sudo systemctl start hukapp-nextjs

# Check service status
sudo systemctl status hukapp-nextjs
```

### 8. Deploy the Application

```bash
# Make deployment script executable
chmod +x scripts/deploy.sh

# Run deployment
./scripts/deploy.sh
```

## Verification

After setup, verify everything is working:

```bash
# Check Nginx status
sudo systemctl status nginx

# Check application service
sudo systemctl status hukapp-nextjs

# Test HTTP redirect
curl -I http://api4js.com.tr

# Test HTTPS
curl -I https://api4js.com.tr

# Check SSL certificate
openssl s_client -connect api4js.com.tr:443 -servername api4js.com.tr
```

## Monitoring and Logs

### View Application Logs
```bash
# Real-time logs
sudo journalctl -u hukapp-nextjs -f

# Recent logs
sudo journalctl -u hukapp-nextjs --since "1 hour ago"
```

### View Nginx Logs
```bash
# Access logs
sudo tail -f /var/log/nginx/api4js.com.tr.access.log

# Error logs
sudo tail -f /var/log/nginx/api4js.com.tr.error.log
```

## Maintenance

### SSL Certificate Renewal
Certificates will auto-renew via cron job. To manually renew:

```bash
sudo certbot renew
sudo systemctl reload nginx
```

### Application Updates
To deploy updates:

```bash
cd /opt/hukapp-nextjs-frontend
git pull origin main
./scripts/deploy.sh
```

### Restart Services
```bash
# Restart application
sudo systemctl restart hukapp-nextjs

# Restart Nginx
sudo systemctl restart nginx
```

## Troubleshooting

### Common Issues

1. **502 Bad Gateway**: Application service is not running
   ```bash
   sudo systemctl start hukapp-nextjs
   ```

2. **SSL Certificate Issues**: Check certificate status
   ```bash
   sudo certbot certificates
   ```

3. **Permission Issues**: Fix file permissions
   ```bash
   sudo chown -R www-data:www-data /var/www/api4js.com.tr
   ```

4. **Port Already in Use**: Check what's using port 3000
   ```bash
   sudo lsof -i :3000
   ```

### Log Locations
- Application logs: `journalctl -u hukapp-nextjs`
- Nginx access logs: `/var/log/nginx/api4js.com.tr.access.log`
- Nginx error logs: `/var/log/nginx/api4js.com.tr.error.log`
- SSL logs: `/var/log/letsencrypt/letsencrypt.log`

## Security Considerations

- Firewall is configured to allow only necessary ports
- SSL certificates are automatically renewed
- Security headers are configured in Nginx
- Application runs as non-root user (www-data)
- Rate limiting is enabled

## Performance Optimization

The configuration includes:
- Gzip compression
- Static file caching
- HTTP/2 support
- Connection keep-alive
- Rate limiting

Your Next.js application should now be accessible at `https://api4js.com.tr` with automatic HTTPS redirect and SSL certificates!
