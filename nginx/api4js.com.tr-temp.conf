# Temporary Nginx configuration for api4js.com.tr (before SSL setup)
# This configuration handles HTTP traffic and Let's Encrypt challenges

# Rate limiting
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;

# Upstream for Next.js application
upstream nextjs_backend {
    server 127.0.0.1:3000;
    keepalive 32;
}

# HTTP server - serves content and handles Let's Encrypt challenges
server {
    listen 80;
    listen [::]:80;
    server_name api4js.com.tr www.api4js.com.tr;
    
    # Let's Encrypt challenge location
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    # Rate limiting
    limit_req zone=api_limit burst=20 nodelay;
    
    # Root directory for static files
    root /var/www/api4js.com.tr;
    index index.html;
    
    # Logging
    access_log /var/log/nginx/api4js.com.tr.access.log;
    error_log /var/log/nginx/api4js.com.tr.error.log;
    
    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri @nextjs;
    }
    
    # Next.js static files
    location /_next/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri @nextjs;
    }
    
    # Next.js API routes and pages
    location / {
        try_files $uri @nextjs;
    }
    
    # Proxy to Next.js application
    location @nextjs {
        proxy_pass http://nextjs_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_redirect off;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
