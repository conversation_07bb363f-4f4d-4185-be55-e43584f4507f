{"openapi": "3.0.1", "info": {"title": "AVAS Service API", "version": "v1"}, "servers": [{"url": "/", "description": "Default Server URL"}, {"url": "http://localhost:4244", "description": "Local Development Server"}, {"url": "https://api.hukapp.com", "description": "Production Server"}, {"url": "http://*************:4244", "description": "Development Server"}], "tags": [{"name": "Client Management", "description": "CRUD operations for managing clients"}, {"name": "Admin User Reports", "description": "Admin endpoints for generating comprehensive reports about all users"}, {"name": "OPEN AI Key Retrieveal", "description": "API for retrieving OPEN AI Key "}, {"name": "Admin File Management", "description": "Admin endpoints for comprehensive file management operations"}, {"name": "User File Management", "description": "User endpoints for file access operations"}, {"name": "Client Notes", "description": "API for managing client notes"}, {"name": "Admin Transaction Types Management", "description": "Admin endpoints for managing transaction types"}, {"name": "Trial Notes", "description": "API for managing trial notes"}, {"name": "Client Accounting", "description": "API for managing client-specific accounting records (Alı<PERSON>lar, Alınacaklar, Geri <PERSON>meler)"}, {"name": "Admin Coupon Management", "description": "CRUD operations for discount coupons"}, {"name": "Case Details", "description": "API for managing case details with case type, crime type, derdest status, case value, reason, and title"}, {"name": "Payment", "description": "Payment operations with iyzico"}, {"name": "Admin Product Management", "description": "CRUD operations for products"}, {"name": "Email", "description": "Email API"}, {"name": "Admin Audit <PERSON>", "description": "Admin API for managing and viewing user activity audit logs"}, {"name": "Case Notes", "description": "API for managing case notes"}, {"name": "Case Reports", "description": "API for generating comprehensive case reports"}, {"name": "Power of Attorneys", "description": "API for managing power of attorneys for legal cases"}, {"name": "Income/Expense Transactions", "description": "API for managing income and expense transactions"}, {"name": "Sync", "description": "API for syncing with UYAP system"}, {"name": "Frequent Case Numbers", "description": "API for managing frequently used case numbers"}, {"name": "User Report", "description": "API for generating comprehensive user reports"}], "paths": {"/api/user/trial-notes/{id}": {"get": {"tags": ["Trial Notes"], "summary": "Get a specific trial note", "description": "Retrieves a specific trial note by its ID", "operationId": "getTrialNoteById", "parameters": [{"name": "id", "in": "path", "description": "ID of the trial note", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"200": {"description": "Trial note retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}, "404": {"description": "Trial note not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Trial Notes"], "summary": "Update a trial note", "description": "Updates an existing trial note", "operationId": "updateTrialNote", "parameters": [{"name": "id", "in": "path", "description": "ID of the trial note to update", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrialNoteUpdateDto"}}}, "required": true}, "responses": {"200": {"description": "Trial note updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}, "404": {"description": "Trial note not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Trial Notes"], "summary": "Delete a trial note", "description": "Deletes a trial note by its ID", "operationId": "deleteTrialNote", "parameters": [{"name": "id", "in": "path", "description": "ID of the trial note to delete", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"204": {"description": "Trial note deleted successfully"}, "404": {"description": "Trial note not found"}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "/api/user/office/transactions/income-expense/{id}": {"put": {"tags": ["Income/Expense Transactions"], "summary": "Update Income/Expense transaction by id", "operationId": "putMethodName", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TransactionResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Income/Expense Transactions"], "summary": "Delete an Income/Expense transaction by id", "operationId": "deleteMethodName", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/clients/{id}": {"get": {"tags": ["Client Management"], "summary": "Get client by ID", "description": "Retrieves a specific client by ID for the authenticated user", "operationId": "getClientById", "parameters": [{"name": "id", "in": "path", "description": "Client ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"404": {"description": "Client not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientResponseDto"}}}}, "200": {"description": "Client retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientResponseDto"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Client Management"], "summary": "Update client", "description": "Updates an existing client for the authenticated user", "operationId": "updateClient", "parameters": [{"name": "id", "in": "path", "description": "Client ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientUpdateDto"}}}, "required": true}, "responses": {"409": {"description": "Client name already exists", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientResponseDto"}}}}, "400": {"description": "Invalid input data or client name already exists", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientResponseDto"}}}}, "404": {"description": "Client not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientResponseDto"}}}}, "200": {"description": "Client updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientResponseDto"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Client Management"], "summary": "Delete client", "description": "Deletes (soft delete) an existing client for the authenticated user", "operationId": "deleteClient", "parameters": [{"name": "id", "in": "path", "description": "Client ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"404": {"description": "Client not found"}, "204": {"description": "Client deleted successfully"}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "/api/user/clients/notes/{noteId}": {"put": {"tags": ["Client Notes"], "summary": "Update a client note", "description": "Updates an existing client note", "operationId": "updateClientNote", "parameters": [{"name": "noteId", "in": "path", "description": "Note ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientNoteRequest"}}}, "required": true}, "responses": {"404": {"description": "Client note not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientNoteResponse"}}}}, "400": {"description": "Invalid request data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientNoteResponse"}}}}, "200": {"description": "Client note updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientNoteResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Client Notes"], "summary": "Delete a client note", "description": "Deletes an existing client note", "operationId": "deleteClientNote", "parameters": [{"name": "noteId", "in": "path", "description": "Note ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"404": {"description": "Client note not found"}, "204": {"description": "Client note deleted successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/user/client-accounting/{id}": {"get": {"tags": ["Client Accounting"], "summary": "Get accounting record by ID", "description": "Retrieves a specific accounting record by ID", "operationId": "getClientAccountingRecordById", "parameters": [{"name": "id", "in": "path", "description": "Accounting record ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Accounting record retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}, "404": {"description": "Accounting record not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}, "402": {"description": "Subscription required", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Client Accounting"], "summary": "Update accounting record", "description": "Updates an existing accounting record", "operationId": "updateClientAccounting", "parameters": [{"name": "id", "in": "path", "description": "Accounting record ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientAccountingUpdateRequest"}}}, "required": true}, "responses": {"404": {"description": "Accounting record not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}, "400": {"description": "Invalid request data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}, "402": {"description": "Subscription required", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}, "200": {"description": "Accounting record updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Client Accounting"], "summary": "Delete accounting record", "description": "Deletes an accounting record", "operationId": "deleteClientAccounting", "parameters": [{"name": "id", "in": "path", "description": "Accounting record ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Accounting record deleted successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "404": {"description": "Accounting record not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "402": {"description": "Subscription required", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/power-of-attorneys/{id}": {"get": {"tags": ["Power of Attorneys"], "summary": "Get a specific power of attorney", "description": "Retrieves a specific power of attorney by its ID for the authenticated user", "operationId": "getPowerOfAttorneyById", "parameters": [{"name": "id", "in": "path", "description": "ID of the power of attorney to retrieve", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"404": {"description": "Power of attorney not found"}, "403": {"description": "Forbidden"}, "400": {"description": "Invalid ID format"}, "401": {"description": "Unauthorized"}, "200": {"description": "Power of attorney retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PowerOfAttorneyResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Power of Attorneys"], "summary": "Update a power of attorney", "description": "Updates an existing power of attorney with the provided data. Only the owner can update their power of attorney.", "operationId": "updatePowerOfAttorney", "parameters": [{"name": "id", "in": "path", "description": "ID of the power of attorney to update", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PowerOfAttorneyRequest"}}}, "required": true}, "responses": {"200": {"description": "Power of attorney updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PowerOfAttorneyResponse"}}}}, "403": {"description": "Forbidden - not the owner of the resource"}, "404": {"description": "Power of attorney not found"}, "401": {"description": "Unauthorized"}, "400": {"description": "Invalid input data or ID format"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Power of Attorneys"], "summary": "Delete a power of attorney", "description": "Deletes a power of attorney by its ID. Only the owner can delete their power of attorney.", "operationId": "deletePowerOfAttorney", "parameters": [{"name": "id", "in": "path", "description": "ID of the power of attorney to delete", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"403": {"description": "Forbidden - not the owner of the resource"}, "404": {"description": "Power of attorney not found"}, "400": {"description": "Invalid ID format"}, "401": {"description": "Unauthorized"}, "204": {"description": "Power of attorney deleted successfully"}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/notes/{id}": {"get": {"tags": ["Case Notes"], "summary": "Get a specific case note", "description": "Retrieves a specific case note by its ID", "operationId": "getCaseNoteById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseNoteResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Case Notes"], "summary": "Update a case note", "description": "Updates an existing case note", "operationId": "updateCaseNote", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaseNoteRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseNoteResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Case Notes"], "summary": "Delete a case note", "description": "Deletes a case note by its ID", "operationId": "deleteCaseNote", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}]}}, "/api/user/case-details/{id}": {"get": {"tags": ["Case Details"], "summary": "Get case details by ID", "description": "Retrieves case details by its ID", "operationId": "getCaseDetailsById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Case details retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseDetailsResponse"}}}}, "404": {"description": "Case details not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseDetailsResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Case Details"], "summary": "Update case details", "description": "Updates an existing case details record", "operationId": "updateCaseDetails", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaseDetailsRequest"}}}, "required": true}, "responses": {"200": {"description": "Case details updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseDetailsResponse"}}}}, "404": {"description": "Case details not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseDetailsResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Case Details"], "summary": "Delete case details", "description": "Deletes an existing case details record", "operationId": "deleteCaseDetails", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"204": {"description": "Case details deleted successfully"}, "404": {"description": "Case details not found"}}, "security": [{"bearerAuth": []}]}}, "/api/tasks/{id}": {"get": {"tags": ["task-controller"], "summary": "Get a task by id of the authenticated user", "operationId": "getTaskById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TaskResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["task-controller"], "summary": "Update an existing task of the authenticated user by id", "operationId": "updateTask", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TaskResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["task-controller"], "summary": "Delete a task of the authenticated user by id", "operationId": "deleteTask", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}]}}, "/api/tasks/notes/{noteId}": {"put": {"tags": ["task-controller"], "summary": "Updates a note of the authenticated user by id", "operationId": "updateNote", "parameters": [{"name": "noteId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoteRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/NoteResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["task-controller"], "summary": "Delete a note of the authenticated user by id", "operationId": "deleteNote", "parameters": [{"name": "noteId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}]}}, "/api/reminders/{id}": {"get": {"tags": ["reminder-controller"], "summary": "Get a reminder of the authenticated user by id", "operationId": "getReminderById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReminderResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["reminder-controller"], "summary": "Update a reminder of the authenticated user by id", "operationId": "updateReminder", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReminderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReminderResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["reminder-controller"], "summary": "Delete a reminder of the authenticated user by id", "operationId": "deleteReminder", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}]}}, "/api/admin/products/{id}": {"get": {"tags": ["Admin Product Management"], "summary": "Get product by ID", "description": "Retrieve a specific product by its ID", "operationId": "getProductById", "parameters": [{"name": "id", "in": "path", "description": "Product ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "404": {"description": "Product not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "200": {"description": "Product retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Admin Product Management"], "summary": "Update product", "description": "Update an existing product", "operationId": "updateProduct", "parameters": [{"name": "id", "in": "path", "description": "Product ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "Product updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "400": {"description": "Invalid input", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "404": {"description": "Product not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Admin Product Management"], "summary": "Delete product", "description": "Delete a product by its ID", "operationId": "deleteProduct", "parameters": [{"name": "id", "in": "path", "description": "Product ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"204": {"description": "Product deleted successfully"}, "403": {"description": "Forbidden"}, "404": {"description": "Product not found"}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "/api/admin/office/transaction-types/{id}": {"put": {"tags": ["Admin Transaction Types Management"], "summary": "Update an existing transaction type", "operationId": "updateTransactionType", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionTypeRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TransactionTypeResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Admin Transaction Types Management"], "summary": "Delete an existing transaction type", "operationId": "deleteTransactionType", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/coupons/{id}": {"get": {"tags": ["Admin Coupon Management"], "summary": "Get coupon by ID", "description": "Retrieve a specific coupon by its ID", "operationId": "getCouponById", "parameters": [{"name": "id", "in": "path", "description": "Coupon ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"200": {"description": "Coupon retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}, "404": {"description": "Coupon not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Admin Coupon Management"], "summary": "Update coupon", "description": "Update an existing coupon's properties", "operationId": "updateCoupon", "parameters": [{"name": "id", "in": "path", "description": "Coupon ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCouponDto"}}}, "required": true}, "responses": {"200": {"description": "Coupon updated successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}, "404": {"description": "Coupon not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Admin Coupon Management"], "summary": "Delete/deactivate coupon", "description": "Deactivate a coupon (soft delete)", "operationId": "deleteCoupon", "parameters": [{"name": "id", "in": "path", "description": "Coupon ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"204": {"description": "Coupon deactivated successfully"}, "404": {"description": "Coupon not found"}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "/auth/user/verify-email": {"post": {"tags": ["person-controller"], "summary": "Verify email and complete registration", "description": "Verifies the email using OTP and completes the person registration", "operationId": "verifyEmail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyEmailRequest"}}}, "required": true}, "responses": {"200": {"description": "Email verified and person created", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/VerifyEmailResponse"}}}}, "400": {"description": "Invalid OTP or expired registration"}, "500": {"description": "Beklenmeyen bir hata <PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin."}}}}, "/auth/user/login": {"post": {"tags": ["person-controller"], "summary": "Login a person", "description": "Logs in a person with the provided credentials", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonLoginRequest"}}}, "required": true}, "responses": {"200": {"description": "Successfully logged in", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PersonLoginResponse"}}}}, "400": {"description": "Invalid input"}, "500": {"description": "Beklenmeyen bir hata <PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin."}, "401": {"description": "Invalid credentials"}}}}, "/auth/user/forgot-password/validate-otp": {"post": {"tags": ["person-controller"], "summary": "Validate OTP", "description": "Validates the provided OTP for the given email", "operationId": "validateOtp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateOtpRequest"}}}, "required": true}, "responses": {"200": {"description": "OTP is valid"}, "400": {"description": "Invalid OTP"}}}}, "/auth/user/forgot-password/update-password": {"post": {"tags": ["person-controller"], "summary": "Update password", "description": "Updates the password for the given email", "operationId": "updatePassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePasswordRequest"}}}, "required": true}, "responses": {"400": {"description": "Invalid input"}, "200": {"description": "Password updated successfully"}}}}, "/auth/user/forgot-password/send-otp": {"post": {"tags": ["person-controller"], "summary": "Forgot password. Generates an OTP and sends it to the provided email", "description": "If the user exists, sends a random 8-digit OTP to email with 3 minutes expiration", "operationId": "forgotPassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "OTP sent"}, "400": {"description": "Invalid input"}}}}, "/auth/user/create": {"post": {"tags": ["person-controller"], "summary": "Initiate person creation", "description": "Initiates the person creation process by sending a verification email", "operationId": "create<PERSON>erson", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonCreateRequest"}}}, "required": true}, "responses": {"409": {"description": "Email already exists"}, "400": {"description": "Invalid input"}, "500": {"description": "Beklenmeyen bir hata <PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin."}, "200": {"description": "Verification email sent", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PersonCreateResponse"}}}}}}}, "/auth/user/create-direct": {"post": {"tags": ["person-controller"], "summary": "Create a new person directly (legacy)", "description": "Creates a new person directly without email verification", "operationId": "createPersonDirect", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonCreateRequest"}}}, "required": true}, "responses": {"201": {"description": "Successfully created person", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PersonCreateResponse"}}}}, "400": {"description": "Invalid input"}, "500": {"description": "Beklenmeyen bir hata <PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin."}, "409": {"description": "<PERSON><PERSON><PERSON> eden kayıt."}}}}, "/api/v1/email/test": {"post": {"tags": ["Email"], "summary": "Send a test email", "description": "Sends a test email to the specified address", "operationId": "sendTestEmail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailRequest"}}}, "required": true}, "responses": {"200": {"description": "<PERSON>ail sent successfully", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "500": {"description": "Failed to send email", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "400": {"description": "Invalid request", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/email/test-otp": {"post": {"tags": ["Email"], "summary": "Send a test OTP email", "description": "Sends a test OTP email to the specified address", "operationId": "sendTestOtpEmail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailRequest"}}}, "required": true}, "responses": {"200": {"description": "<PERSON>ail sent successfully", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "500": {"description": "Failed to send email", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "400": {"description": "Invalid request", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/email/test-html": {"post": {"tags": ["Email"], "summary": "Send a test HTML email", "description": "Sends a test HTML email to the specified address", "operationId": "sendTestHtmlEmail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailRequest"}}}, "required": true}, "responses": {"200": {"description": "<PERSON>ail sent successfully", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "500": {"description": "Failed to send email", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "400": {"description": "Invalid request", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/trial-notes": {"get": {"tags": ["Trial Notes"], "summary": "Get all trial notes", "description": "Retrieves all trial notes for the authenticated user", "operationId": "getAllTrialNotes", "responses": {"200": {"description": "Trial notes retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Trial Notes"], "summary": "Create a new trial note", "description": "Creates a new trial note for a specific case", "operationId": "createTrialNote", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrialNoteCreateDto"}}}, "required": true}, "responses": {"404": {"description": "Case not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}, "201": {"description": "Trial note created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/sync": {"post": {"tags": ["Sync"], "summary": "Sync UYAP", "description": "Sync to UYAP system using provided JSID. Requires authentication with <PERSON><PERSON> token", "operationId": "syncToUyap", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/sync/jsid": {"get": {"tags": ["Sync"], "summary": "Get JSID from cache and check UYAP connectivity", "operationId": "getJsidFromCache", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Sync"], "summary": "Save JSID to cache and check UYAP connectivity", "operationId": "postJsid", "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/payment/create-order": {"post": {"tags": ["Payment"], "summary": "Create order", "description": "Create a new order to pay with iyzico for selected products", "operationId": "createOrder", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PaymentResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/office/transactions/income-expense": {"get": {"tags": ["Income/Expense Transactions"], "summary": "Get all Income/Expense transactions, you can filter by category", "operationId": "getAllTransactions", "parameters": [{"name": "category", "in": "query", "required": false, "schema": {"type": "string", "enum": ["INCOME", "EXPENSE"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Income/Expense Transactions"], "summary": "Save Income/Expense", "description": "Create a new income or expense", "operationId": "saveIncomeExpense", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TransactionResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/frequent-case-numbers": {"get": {"tags": ["Frequent Case Numbers"], "summary": "Get all frequently used case numbers", "description": "Retrieves all frequently used case numbers for the authenticated user, ordered by last used timestamp.", "operationId": "getFrequentCaseNumbers", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FrequentCaseNumberResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Frequent Case Numbers"], "summary": "Add a new case number to frequently used list", "description": "Adds a new case number to the user's frequently used list. The case number must exist in the user's cases.", "operationId": "addFrequentCaseNumber", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrequentCaseNumberRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FrequentCaseNumberResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/compensation/calculate": {"post": {"tags": ["compensation-calculator-controller"], "summary": "Calculate severance/notice compensation", "operationId": "postMethodName", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompensationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CompensationResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/clients": {"get": {"tags": ["Client Management"], "summary": "Get all clients", "description": "Retrieves all clients for the authenticated user. Results are sorted by creation date.", "operationId": "getAllClients", "responses": {"200": {"description": "Clients retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClientResponseDto"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Client Management"], "summary": "Create new client", "description": "Creates a new client for the authenticated user.\n\nAvailable Client Types:\n- INDIVIDUAL: Bireysel müvekkil\n- CORPORATE: Kurumsal müvekkil\n- OTHER: <PERSON><PERSON><PERSON>\n\nThe client name must be unique for each user.\n", "operationId": "createClient", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientCreateDto"}}}, "required": true}, "responses": {"409": {"description": "Client name already exists", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientResponseDto"}}}}, "201": {"description": "Client created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientResponseDto"}}}}, "400": {"description": "Invalid input data or client name already exists", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientResponseDto"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/clients/{clientId}/notes": {"get": {"tags": ["Client Notes"], "summary": "Get all notes for a client", "description": "Retrieves all notes for a specific client", "operationId": "getClientNotes", "parameters": [{"name": "clientId", "in": "path", "description": "Client ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Client notes retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClientNoteResponse"}}}}}, "404": {"description": "Client not found", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClientNoteResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Client Notes"], "summary": "Create a new client note", "description": "Creates a new note for a specific client", "operationId": "createClientNote", "parameters": [{"name": "clientId", "in": "path", "description": "Client ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientNoteRequest"}}}, "required": true}, "responses": {"201": {"description": "Client note created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientNoteResponse"}}}}, "404": {"description": "Client not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientNoteResponse"}}}}, "400": {"description": "Invalid request data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientNoteResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/client-accounting": {"get": {"tags": ["Client Accounting"], "summary": "Get all client accounting records", "description": "Retrieves all accounting records for the authenticated user, optionally filtered by accounting type", "operationId": "getAllClientAccountingRecords", "parameters": [{"name": "accountingType", "in": "query", "description": "Filter by accounting type (RECEIVED, RECEIVABLE, REFUND)", "required": false, "schema": {"type": "string", "enum": ["RECEIVED", "RECEIVABLE", "REFUND"]}}], "responses": {"402": {"description": "Subscription required", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}}, "200": {"description": "Accounting records retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Client Accounting"], "summary": "Create client accounting record", "description": "Creates a new accounting record for a client with specified type (RECEIVED, RECEIVABLE, REFUND)", "operationId": "createClientAccounting", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientAccountingCreateRequest"}}}, "required": true}, "responses": {"404": {"description": "Client not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}, "400": {"description": "Invalid request data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}, "402": {"description": "Subscription required", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}, "201": {"description": "Accounting record created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/power-of-attorneys": {"get": {"tags": ["Power of Attorneys"], "summary": "Get all power of attorneys", "description": "Retrieves all power of attorneys for the authenticated user. Results are sorted by creation date.", "operationId": "getAllPowerOfAttorneys", "responses": {"200": {"description": "List of power of attorneys retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PowerOfAttorneyResponse"}}}}, "403": {"description": "Forbidden"}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Power of Attorneys"], "summary": "Create a new power of attorney", "description": "Creates a new power of attorney document for a specific legal case. Requires authentication.", "operationId": "createPowerOfAttorney", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PowerOfAttorneyRequest"}}}, "required": true}, "responses": {"201": {"description": "Power of attorney created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PowerOfAttorneyResponse"}}}}, "400": {"description": "Invalid input data"}, "403": {"description": "Forbidden"}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/notes": {"get": {"tags": ["Case Notes"], "summary": "Get all case notes", "description": "Retrieves all case notes for the authenticated user", "operationId": "getAllCaseNotes", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CaseNoteResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Case Notes"], "summary": "Create a new case note", "description": "Creates a new note for a specific case", "operationId": "createCaseNote", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaseNoteRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseNoteResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/case-details": {"get": {"tags": ["Case Details"], "summary": "Get all case details", "description": "Retrieves all case details for the authenticated user", "operationId": "getAllCaseDetails", "responses": {"200": {"description": "Case details retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CaseDetailsResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Case Details"], "summary": "Create new case details", "description": "Creates a new case details record with the provided information.\n\nAvailable Case Types: CEZA_DAVASI (Ceza Davası), HUKUK_DAVASI (Hukuk Davası), IDARI_DAVA (İdari Dava),\nICRA_TAKIBI (İcra Takibi), ARABULUCULUK (Arabuluculuk), TAHKIM (Tahkim), DIGER (Diğer)\n\nAvailable Crime Types: DOLANDIRICILIK (Dolandırıcılık), HIRSIZLIK (Hırsızlık), KASTEN_YARALAMA (Kasten Yaralama),\nTAKSIRLE_YARALAMA (Taksirle Yaralama), KASTEN_OLDURME (Kasten Öldürme), TAKSIRLE_OLDURME (Taksirle Öldürme),\nHAKARET (Hakaret), TEHDIT (Tehdit), UYUSTURUCU (Uyuşturucu Madde Ticareti), CINSEL_SUC (Cinsel Suçlar),\nZIMMET (Zimmet), RUSVET (Rüşvet), SAHTECILIK (Sahtecilik), VERGI_SUCU (Vergi Suçları), DIGER (Diğer)\n", "operationId": "createCaseDetails", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaseDetailsRequest"}}}, "required": true}, "responses": {"201": {"description": "Case details created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseDetailsResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/tasks": {"get": {"tags": ["task-controller"], "summary": "Get all tasks of the authenticated user", "operationId": "getAllTasks", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaskResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["task-controller"], "summary": "Create a new task", "operationId": "createTask", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TaskResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/tasks/{taskId}/notes": {"post": {"tags": ["task-controller"], "summary": "Create a new note for a task of the authenticated user", "operationId": "createNoteForTask", "parameters": [{"name": "taskId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoteRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/NoteResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/reminders": {"get": {"tags": ["reminder-controller"], "summary": "Get all reminders of the authenticated user", "operationId": "getAllReminders", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReminderResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["reminder-controller"], "summary": "Create a new reminder for the authenticated user", "operationId": "createReminder", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReminderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReminderResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/products": {"get": {"tags": ["Admin Product Management"], "summary": "Get all products", "description": "Retrieve all available products", "operationId": "getAllProducts", "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductResponse"}}}}}, "200": {"description": "Products retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Admin Product Management"], "summary": "Create new product", "description": "Create a new product", "operationId": "createProduct", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCreateRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "400": {"description": "Invalid input", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "201": {"description": "Product created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/office/transaction-types": {"get": {"tags": ["Admin Transaction Types Management"], "summary": "Get all transaction types. You can also filter by category", "operationId": "getTransactionTypes", "parameters": [{"name": "category", "in": "query", "required": false, "schema": {"type": "string", "enum": ["INCOME", "EXPENSE"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionTypeResponse"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Admin Transaction Types Management"], "summary": "Create Transaction Type", "description": "Create a new transaction type", "operationId": "createTransactionType", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionTypeRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TransactionTypeResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/files/upload": {"post": {"tags": ["Admin File Management"], "summary": "Upload a file", "description": "Upload a file to the database with metadata storage.\n\n**Supported file types:**\n- Documents: pdf, doc, docx, txt, rtf\n- Spreadsheets: xls, xlsx, csv\n- Presentations: ppt, pptx\n- Images: jpg, jpeg, png, gif, bmp, webp\n- Videos: mp4, avi, mov, wmv, flv\n- Audio: mp3, wav, flac, aac, ogg\n- Archives: zip, rar, 7z, tar, gz\n\n**File size limit:** 50MB\n\n**Admin privileges:** This endpoint requires ADMIN role and bypasses subscription checks.\n", "operationId": "uploadFile", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/FileUploadDto"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FileResponseDto"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/coupons": {"get": {"tags": ["Admin Coupon Management"], "summary": "Get all coupons", "description": "Retrieve all coupons with pagination support", "operationId": "getAllCoupons", "parameters": [{"name": "page", "in": "query", "description": "Page number (0-based)", "required": false, "schema": {"minimum": 0, "type": "integer", "format": "int32", "default": 0}, "example": 0}, {"name": "size", "in": "query", "description": "Page size", "required": false, "schema": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32", "default": 20}, "example": 20}, {"name": "sortBy", "in": "query", "description": "Sort field", "required": false, "schema": {"type": "string", "default": "createdAt"}, "example": "createdAt"}, {"name": "sortDir", "in": "query", "description": "Sort direction", "required": false, "schema": {"type": "string", "default": "desc"}, "example": "desc"}, {"name": "active", "in": "query", "description": "Filter by active status", "required": false, "schema": {"type": "boolean"}}, {"name": "search", "in": "query", "description": "Search by coupon code", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Coupons retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageCouponResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageCouponResponseDto"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Admin Coupon Management"], "summary": "Create new coupon", "description": "Create a new discount coupon with specified parameters", "operationId": "createCoupon", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCouponDto"}}}, "required": true}, "responses": {"409": {"description": "Coupon code already exists", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}, "201": {"description": "Coupon created successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}, "400": {"description": "Invalid input data", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/trials": {"get": {"tags": ["uyap-user-info-controller"], "summary": "Get user trials", "operationId": "getUserTrials", "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/trial-notes/case": {"get": {"tags": ["Trial Notes"], "summary": "Get trial notes for a specific case", "description": "Retrieves all trial notes for a specific case number", "operationId": "getTrialNotesByCaseNumber", "parameters": [{"name": "caseNumber", "in": "query", "description": "Case number to filter by", "required": true, "schema": {"type": "string"}, "example": "2023/123"}], "responses": {"200": {"description": "Trial notes retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}}, "404": {"description": "Case not found", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TrialNoteResponseDto"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/report": {"get": {"tags": ["User Report"], "summary": "Generate detailed user report", "description": "Generates a comprehensive report containing all available information about the authenticated user, including financial data, task statistics, and optionally the user's photo", "operationId": "generateUserReport", "parameters": [{"name": "includePhoto", "in": "query", "description": "Whether to include the user's photo in the report. Including the photo increases response size.", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"401": {"description": "Unauthorized - User not authenticated"}, "200": {"description": "Report successfully generated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserReportResponse"}}}}, "404": {"description": "User not found"}, "500": {"description": "Internal server error"}}, "security": [{"bearerAuth": []}]}}, "/api/user/photo": {"get": {"tags": ["uyap-user-info-controller"], "summary": "Get user photo", "operationId": "getUserPhoto", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPhotoResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/payment": {"get": {"tags": ["Payment"], "summary": "Get all products", "description": "Retrieve all available products", "operationId": "getAllProducts_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/payment/{paymentId}": {"get": {"tags": ["Payment"], "summary": "Get payment by ID", "description": "Get payment details by payment ID", "operationId": "getPaymentById", "parameters": [{"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PaymentResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/payment/{paymentId}/check": {"get": {"tags": ["Payment"], "summary": "Retrieve payment status", "description": "Check if a payment is made or not using iyzico API and update payment status", "operationId": "checkPaymentStatus", "parameters": [{"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PaymentStatusResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/payment/payments": {"get": {"tags": ["Payment"], "summary": "Get all payments", "description": "Get all payments for the authenticated user", "operationId": "getAllPayments", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/office/transactions/income-expense/transaction-types": {"get": {"tags": ["Income/Expense Transactions"], "summary": "Get all transaction types. You can also filter by category", "operationId": "getTransactionTypes_1", "parameters": [{"name": "category", "in": "query", "required": false, "schema": {"type": "string", "enum": ["INCOME", "EXPENSE"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionTypeResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/office/transactions/income-expense/currencies": {"get": {"tags": ["Income/Expense Transactions"], "summary": "Get Currency List", "description": "Get available currencies", "operationId": "getCurrencyList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CurrencyDto"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/notifications": {"get": {"tags": ["uyap-user-info-controller"], "summary": "Get user notifications", "operationId": "getUserNotifications", "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/frequent-case-numbers/{id}": {"get": {"tags": ["Frequent Case Numbers"], "summary": "Get a specific frequently used case number", "description": "Retrieves a specific frequently used case number by its ID.", "operationId": "getFrequentCaseNumberById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FrequentCaseNumberResponse"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Frequent Case Numbers"], "summary": "Delete a frequently used case number", "description": "Deletes a specific frequently used case number by its ID.", "operationId": "deleteFrequentCaseNumber", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/details": {"get": {"tags": ["uyap-user-info-controller"], "summary": "Get user details", "operationId": "getUserDetails", "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/clients/notes": {"get": {"tags": ["Client Notes"], "summary": "Get all client notes", "description": "Retrieves all client notes for the authenticated user across all clients", "operationId": "getAllClientNotes", "responses": {"200": {"description": "Client notes retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClientNoteResponse"}}}}}, "402": {"description": "Subscription required", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClientNoteResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/client-accounting/client/{clientId}": {"get": {"tags": ["Client Accounting"], "summary": "Get accounting records by client", "description": "Retrieves all accounting records for a specific client, optionally filtered by accounting type", "operationId": "getClientAccountingRecordsByClient", "parameters": [{"name": "clientId", "in": "path", "description": "Client ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "accountingType", "in": "query", "description": "Filter by accounting type (RECEIVED, RECEIVABLE, REFUND)", "required": false, "schema": {"type": "string", "enum": ["RECEIVED", "RECEIVABLE", "REFUND"]}}], "responses": {"200": {"description": "Client accounting records retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}, "404": {"description": "Client not found", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}}, "402": {"description": "Subscription required", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/client-accounting/client/{clientId}/total": {"get": {"tags": ["Client Accounting"], "summary": "Get total amount by client and type", "description": "Calculates total amount for a specific client and accounting type", "operationId": "getTotalAmountByClientAndType", "parameters": [{"name": "clientId", "in": "path", "description": "Client ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "accountingType", "in": "query", "description": "Accounting type (RECEIVED, RECEIVABLE, REFUND)", "required": true, "schema": {"type": "string", "enum": ["RECEIVED", "RECEIVABLE", "REFUND"]}}], "responses": {"404": {"description": "Client not found", "content": {"*/*": {"schema": {"type": "number", "format": "double"}}}}, "200": {"description": "Total amount calculated successfully", "content": {"*/*": {"schema": {"type": "number", "format": "double"}}}}, "402": {"description": "Subscription required", "content": {"*/*": {"schema": {"type": "number", "format": "double"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"type": "number", "format": "double"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/client-accounting/by-type": {"get": {"tags": ["Client Accounting"], "summary": "Get all client accounting records by type", "operationId": "getAllClientAccountingRecordsByType", "parameters": [{"name": "accountingType", "in": "query", "description": "Accounting type (RECEIVED, RECEIVABLE, REFUND)", "required": true, "schema": {"type": "string", "enum": ["RECEIVED", "RECEIVABLE", "REFUND"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClientAccountingResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/chat/openai": {"get": {"tags": ["OPEN AI Key Retrieveal"], "operationId": "returnOpenAiApiKey", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases": {"get": {"tags": ["uyap-user-info-controller"], "summary": "Get user cases by case status", "operationId": "getUserCases", "parameters": [{"name": "active", "in": "query", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/taraflar": {"get": {"tags": ["uyap-case-details-controller"], "summary": "Get case parties", "operationId": "getCaseParties", "parameters": [{"name": "caseNumber", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/taraflar-all": {"get": {"tags": ["uyap-case-details-controller"], "summary": "Get all parties", "operationId": "getAllParties", "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/tahsilat-reddiyat": {"get": {"tags": ["uyap-case-details-controller"], "summary": "Get case collections and denials", "operationId": "getCaseCollectionAndDenial", "parameters": [{"name": "caseNumber", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/safahat": {"get": {"tags": ["uyap-case-details-controller"], "summary": "Get case history", "operationId": "getCaseHistory", "parameters": [{"name": "caseNumber", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/report": {"get": {"tags": ["Case Reports"], "summary": "Generate detailed case report", "description": "Generates a comprehensive report for a specific case number, including financial data and task statistics", "operationId": "generateCaseReport", "parameters": [{"name": "caseNumber", "in": "query", "description": "The case number to generate the report for", "required": true, "schema": {"type": "string", "example": "2023/123"}}], "responses": {"404": {"description": "Case not found"}, "401": {"description": "Unauthorized - User not authenticated"}, "200": {"description": "Report successfully generated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaseReportResponse"}}}}, "500": {"description": "Internal server error"}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/power-of-attorneys/number/{powerOfAttorneyNumber}": {"get": {"tags": ["Power of Attorneys"], "summary": "Get a power of attorney by its number", "description": "Retrieves a specific power of attorney by its power of attorney number for the authenticated user", "operationId": "getPowerOfAttorneyByNumber", "parameters": [{"name": "powerOfAttorneyNumber", "in": "path", "description": "Power of attorney number to retrieve", "required": true, "schema": {"type": "string"}, "example": "POA-2023-12345"}], "responses": {"400": {"description": "Invalid power of attorney number format"}, "404": {"description": "Power of attorney not found"}, "403": {"description": "Forbidden"}, "401": {"description": "Unauthorized"}, "200": {"description": "Power of attorney retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PowerOfAttorneyResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/power-of-attorneys/case": {"get": {"tags": ["Power of Attorneys"], "summary": "Get power of attorneys for a specific case", "description": "Retrieves all power of attorneys associated with a specific case number for the authenticated user", "operationId": "getPowerOfAttorneysByCaseNumber", "parameters": [{"name": "caseNumber", "in": "query", "description": "Case number to filter power of attorneys", "required": true, "schema": {"type": "string"}, "example": "2023/123"}], "responses": {"400": {"description": "Invalid case number"}, "200": {"description": "List of power of attorneys for the specified case retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PowerOfAttorneyResponse"}}}}, "403": {"description": "Forbidden"}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "/api/user/cases/notes/case": {"get": {"tags": ["Case Notes"], "summary": "Get notes for a specific case", "description": "Retrieves all notes for a specific case number", "operationId": "getCaseNotesByCaseNumber", "parameters": [{"name": "caseNumber", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CaseNoteResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/case-details/types": {"get": {"tags": ["Case Details"], "summary": "Get available case types and crime types", "description": "Returns all available case types and crime types with their display names", "operationId": "getAvailableTypes", "responses": {"200": {"description": "Types retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseTypeOptionsResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/case-details/by-case-number": {"get": {"tags": ["Case Details"], "summary": "Get case details by case number", "description": "Retrieves case details by its case number", "operationId": "getCaseDetailsByCaseNumber", "parameters": [{"name": "caseNumber", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Case details retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseDetailsResponse"}}}}, "404": {"description": "Case details not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaseDetailsResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/user/announcements": {"get": {"tags": ["uyap-user-info-controller"], "summary": "Get UYAP announcements", "operationId": "getUyapAnnouncements", "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/files": {"get": {"tags": ["User File Management"], "summary": "List available files", "operationId": "listFiles", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileListDto"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/files/{id}/download": {"get": {"tags": ["User File Management"], "summary": "Download file by ID", "operationId": "downloadFile", "parameters": [{"name": "id", "in": "path", "description": "File ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "byte"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/users/reports": {"get": {"tags": ["Admin User Reports"], "summary": "Generate comprehensive reports for all users", "description": "Generates detailed reports for all users in the system, including:\n\n**User Information:**\n- User ID and creation date\n- New user status\n- User roles and permissions\n\n**Subscription Information:**\n- Current subscription level (BASIC, PREMIUM, ENTERPRISE)\n- Total payment amount from all successful transactions\n\n**Admin Privileges:** This endpoint requires ADMIN role and excludes sensitive information\nas per admin reporting standards.\n\n**No Pagination:** Returns all users in a single response for comprehensive admin overview.\n", "operationId": "generateAllUserReports", "responses": {"500": {"description": "Internal server error while generating reports", "content": {"application/json": {}}}, "200": {"description": "Successfully generated user reports", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AdminReportAboutUser"}}}}}, "403": {"description": "Access denied - ADMIN role required", "content": {"application/json": {}}}, "401": {"description": "Unauthorized - User not authenticated", "content": {"application/json": {}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/products/payments": {"get": {"tags": ["Admin Product Management"], "summary": "Get all payments in the system", "description": "Get all payments in the system", "operationId": "getAllPayments_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/files": {"get": {"tags": ["Admin File Management"], "summary": "List all files", "operationId": "listFiles_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileListDto"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/files/{id}": {"get": {"tags": ["Admin File Management"], "summary": "Get file details by ID", "operationId": "getFileById", "parameters": [{"name": "id", "in": "path", "description": "File ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FileResponseDto"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Admin File Management"], "summary": "Delete file by ID", "operationId": "deleteFile", "parameters": [{"name": "id", "in": "path", "description": "File ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"200": {"description": "OK"}}, "security": [{"bearerAuth": []}]}}, "/api/admin/files/{id}/download": {"get": {"tags": ["Admin File Management"], "summary": "Download file by ID", "operationId": "downloadFile_1", "parameters": [{"name": "id", "in": "path", "description": "File ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "byte"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/files/statistics": {"get": {"tags": ["Admin File Management"], "summary": "Get file statistics", "operationId": "getFileStatistics", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FileStatistics"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/coupons/code/{code}": {"get": {"tags": ["Admin Coupon Management"], "summary": "Get coupon by code", "description": "Retrieve a specific coupon by its code", "operationId": "getCouponByCode", "parameters": [{"name": "code", "in": "path", "description": "Coupon code", "required": true, "schema": {"type": "string"}, "example": "SUMMER2024"}], "responses": {"200": {"description": "Coupon retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}, "404": {"description": "Coupon not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CouponResponseDto"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/audit-logs/user/{userId}": {"get": {"tags": ["Admin Audit <PERSON>"], "summary": "Get audit logs by user id", "description": "Retrieve all audit logs for a specific user. Useful for investigating user activity patterns and security incidents.", "operationId": "getAuditLogsByUser", "parameters": [{"name": "userId", "in": "path", "description": "User id to filter audit logs", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 21}], "responses": {"200": {"description": "User audit logs retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "403": {"description": "Access denied - Admin role required", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "500": {"description": "Internal server error", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "404": {"description": "User not found", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/audit-logs/user/{userEmail}/date-range": {"get": {"tags": ["Admin Audit <PERSON>"], "summary": "Get audit logs by user and date range", "description": "Retrieve audit logs for a specific user within a date range. Useful for detailed investigation of user activity during specific periods.", "operationId": "getAuditLogsByUserAndDateRange", "parameters": [{"name": "userEmail", "in": "path", "description": "User email to filter audit logs", "required": true, "schema": {"type": "string"}, "example": "<EMAIL>"}, {"name": "startDate", "in": "query", "description": "Start date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}, "example": "2024-01-01"}, {"name": "endDate", "in": "query", "description": "End date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}, "example": "2024-01-31"}], "responses": {"400": {"description": "Invalid date range", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "200": {"description": "User audit logs retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "403": {"description": "Access denied - Admin role required", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "500": {"description": "Internal server error", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/audit-logs/statistics": {"get": {"tags": ["Admin Audit <PERSON>"], "summary": "Get audit log statistics", "description": "Retrieve comprehensive audit log statistics including request counts, user activity, performance metrics, and error rates for the specified date range.", "operationId": "getAuditStatistics", "parameters": [{"name": "startDate", "in": "query", "description": "Start date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}, "example": "2024-01-01"}, {"name": "endDate", "in": "query", "description": "End date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}, "example": "2024-01-31"}], "responses": {"400": {"description": "Invalid date range", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AuditLogStatisticsResponse"}}}}, "200": {"description": "Statistics retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AuditLogStatisticsResponse"}}}}, "403": {"description": "Access denied - Admin role required", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AuditLogStatisticsResponse"}}}}, "500": {"description": "Internal server error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AuditLogStatisticsResponse"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/audit-logs/slow-requests": {"get": {"tags": ["Admin Audit <PERSON>"], "summary": "Get slow requests", "description": "Retrieve all audit logs for slow requests exceeding the specified threshold. Useful for performance monitoring and optimization.", "operationId": "getSlowRequests", "parameters": [{"name": "thresholdMs", "in": "query", "description": "Processing time threshold in milliseconds", "required": false, "schema": {"type": "integer", "format": "int64", "default": 1000}, "example": 1000}], "responses": {"403": {"description": "Access denied - Admin role required", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "500": {"description": "Internal server error", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "200": {"description": "Slow requests retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/audit-logs/recent": {"get": {"tags": ["Admin Audit <PERSON>"], "summary": "Get recent audit logs", "description": "Retrieve the most recent audit logs for quick administrative overview. Limited to recent activity for performance.", "operationId": "getRecentAuditLogs", "responses": {"403": {"description": "Access denied - Admin role required", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "500": {"description": "Internal server error", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "200": {"description": "Recent audit logs retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/audit-logs/failed-requests": {"get": {"tags": ["Admin Audit <PERSON>"], "summary": "Get failed requests", "description": "Retrieve all audit logs for failed requests (HTTP status >= 400). Useful for identifying and investigating system errors and security incidents.", "operationId": "getFailedRequests", "responses": {"200": {"description": "Failed requests retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "403": {"description": "Access denied - Admin role required", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "500": {"description": "Internal server error", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/audit-logs/errors": {"get": {"tags": ["Admin Audit <PERSON>"], "summary": "Get audit logs with errors", "description": "Retrieve all audit logs that contain error messages. Useful for system troubleshooting and error analysis.", "operationId": "getAuditLogsWithErrors", "responses": {"403": {"description": "Access denied - Admin role required", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "500": {"description": "Internal server error", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "200": {"description": "Error audit logs retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/audit-logs/endpoint": {"get": {"tags": ["Admin Audit <PERSON>"], "summary": "Get audit logs by endpoint", "description": "Retrieve all audit logs for a specific API endpoint. Useful for analyzing endpoint usage patterns and performance.", "operationId": "getAuditLogsByEndpoint", "parameters": [{"name": "endpointUrl", "in": "query", "description": "Endpoint URL to filter audit logs", "required": true, "schema": {"type": "string"}, "example": "/api/user/profile"}], "responses": {"403": {"description": "Access denied - Admin role required", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "500": {"description": "Internal server error", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "200": {"description": "Endpoint audit logs retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/audit-logs/date-range": {"get": {"tags": ["Admin Audit <PERSON>"], "summary": "Get audit logs by date range", "description": "Retrieve all audit logs within a specified date range. Useful for generating reports and analyzing trends over time.", "operationId": "getAuditLogsByDateRange", "parameters": [{"name": "startDate", "in": "query", "description": "Start date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}, "example": "2024-01-01"}, {"name": "endDate", "in": "query", "description": "End date (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}, "example": "2024-01-31"}], "responses": {"400": {"description": "Invalid date range", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "403": {"description": "Access denied - Admin role required", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "500": {"description": "Internal server error", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "200": {"description": "Audit logs retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/audit-logs/client-ip/{clientIp}": {"get": {"tags": ["Admin Audit <PERSON>"], "summary": "Get audit logs by client IP", "description": "Retrieve all audit logs for a specific client IP address. Useful for investigating suspicious activity from specific sources.", "operationId": "getAuditLogsByClientIp", "parameters": [{"name": "clientIp", "in": "path", "description": "Client IP address to filter audit logs", "required": true, "schema": {"type": "string"}, "example": "***********"}], "responses": {"200": {"description": "Client IP audit logs retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "403": {"description": "Access denied - Admin role required", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}, "500": {"description": "Internal server error", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuditLogResponse"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/admin/audit-logs/cleanup": {"delete": {"tags": ["Admin Audit <PERSON>"], "summary": "Cleanup old audit logs", "description": "Manually trigger cleanup of old audit logs before the specified cutoff date. Use with caution as this permanently deletes data.", "operationId": "cleanupOldAuditLogs", "parameters": [{"name": "cutoffDate", "in": "query", "description": "Cutoff date - logs before this date will be deleted (YYYY-MM-DD)", "required": true, "schema": {"type": "string", "format": "date"}, "example": "2023-01-01"}], "responses": {"200": {"description": "Cleanup completed successfully", "content": {"*/*": {"schema": {"type": "string"}}}}, "403": {"description": "Access denied - Admin role required", "content": {"*/*": {"schema": {"type": "string"}}}}, "500": {"description": "Internal server error", "content": {"*/*": {"schema": {"type": "string"}}}}, "400": {"description": "Invalid cutoff date", "content": {"*/*": {"schema": {"type": "string"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"TrialNoteUpdateDto": {"required": ["caseNumber", "noteContent", "noteTitle"], "type": "object", "properties": {"caseNumber": {"type": "string", "description": "Case number to which this trial note belongs", "example": "2023/123"}, "noteContent": {"type": "string", "description": "Content of the trial note", "example": "Updated trial note content with additional details."}, "noteTitle": {"maxLength": 255, "minLength": 0, "type": "string", "description": "Title of the trial note", "example": "Updated First Hearing Notes"}}, "description": "Request object for updating a trial note"}, "TrialNoteResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "Unique identifier of the trial note", "format": "int64", "example": 1}, "caseNumber": {"type": "string", "description": "Case number to which this trial note belongs", "example": "2023/123"}, "noteContent": {"type": "string", "description": "Content of the trial note", "example": "This is a detailed trial note content describing the proceedings."}, "noteTitle": {"type": "string", "description": "Title of the trial note", "example": "First Hearing Notes"}, "createdAt": {"type": "string", "description": "Creation timestamp of the trial note", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Last update timestamp of the trial note", "format": "date-time"}, "ownerId": {"type": "integer", "description": "ID of the owner of this trial note", "format": "int64", "example": 1}, "ownerName": {"type": "string", "description": "Full name of the owner of this trial note", "example": "<PERSON>"}, "ownerEmail": {"type": "string", "description": "Email of the owner of this trial note", "example": "<EMAIL>"}}, "description": "Response object for trial note operations"}, "TransactionRequest": {"required": ["amount", "transactionDate", "transactionTypeId"], "type": "object", "properties": {"transactionTypeId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "amount": {"minimum": 1, "type": "number"}, "transactionDate": {"type": "string", "format": "date-time"}, "caseNumber": {"type": "string"}}, "description": "Transaction Create Or Update Request"}, "TransactionResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "amount": {"type": "number"}, "transactionDate": {"type": "string", "format": "date-time"}, "caseNumber": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "ownerName": {"type": "string"}, "ownerEmail": {"type": "string"}, "transactionType": {"$ref": "#/components/schemas/TransactionTypeResponse"}}, "description": "Transaction Create Or Update Response"}, "TransactionTypeResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "category": {"type": "string", "enum": ["INCOME", "EXPENSE"]}}, "description": "Transaction Type Create Or Update Response"}, "ClientUpdateDto": {"required": ["clientType", "name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 2, "type": "string", "description": "Client name", "example": "<PERSON><PERSON>"}, "identityOrTaxNumber": {"type": "integer", "description": "Identity number for individuals or tax number for corporations", "format": "int64", "example": 12345678901}, "address": {"maxLength": 500, "minLength": 0, "type": "string", "description": "Client address", "example": "Atatürk Cad. No:123 Çankaya/Ankara"}, "phoneNumber": {"maxLength": 15, "minLength": 0, "type": "string", "description": "Client phone number", "example": "***********"}, "email": {"type": "string", "description": "Client email address", "example": "<EMAIL>"}, "clientType": {"type": "string", "description": "Client type", "example": "INDIVIDUAL", "enum": ["INDIVIDUAL", "CORPORATE", "OTHER"]}}, "description": "Client update data"}, "ClientResponseDto": {"type": "object", "properties": {"responseMessage": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "description": "Client ID", "format": "int64", "example": 1}, "name": {"type": "string", "description": "Client name", "example": "<PERSON><PERSON>"}, "identityOrTaxNumber": {"type": "integer", "description": "Identity number for individuals or tax number for corporations", "format": "int64", "example": 12345678901}, "address": {"type": "string", "description": "Client address", "example": "Atatürk Cad. No:123 Çankaya/Ankara"}, "phoneNumber": {"type": "string", "description": "Client phone number", "example": "***********"}, "email": {"type": "string", "description": "Client email address", "example": "<EMAIL>"}, "clientType": {"type": "string", "description": "Client type", "example": "INDIVIDUAL", "enum": ["INDIVIDUAL", "CORPORATE", "OTHER"]}, "ownerId": {"type": "integer", "description": "Owner ID", "format": "int64", "example": 1}, "ownerName": {"type": "string", "description": "Owner name", "example": "<PERSON><PERSON><PERSON>"}, "createdAt": {"type": "string", "description": "Creation timestamp", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Last update timestamp", "format": "date-time"}, "isDeleted": {"type": "boolean", "description": "Is deleted flag"}}, "description": "Client response DTO"}, "ClientNoteRequest": {"required": ["noteContent", "noteTitle"], "type": "object", "properties": {"noteTitle": {"type": "string", "description": "Title of the note", "example": "Meeting Notes"}, "noteContent": {"type": "string", "description": "Content of the note", "example": "Client meeting notes and important details."}}, "description": "Client note data"}, "ClientNoteResponse": {"type": "object", "properties": {"id": {"type": "integer", "description": "Note ID", "format": "int64", "example": 1}, "noteTitle": {"type": "string", "description": "Title of the note", "example": "Meeting Notes"}, "noteContent": {"type": "string", "description": "Content of the note", "example": "Client meeting notes and important details."}, "clientId": {"type": "integer", "description": "Client ID", "format": "int64", "example": 1}, "clientName": {"type": "string", "description": "Client name", "example": "<PERSON>"}, "createdAt": {"type": "string", "description": "Creation timestamp", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Last update timestamp", "format": "date-time"}, "ownerId": {"type": "integer", "description": "Owner ID", "format": "int64", "example": 1}, "ownerName": {"type": "string", "description": "Owner name", "example": "<PERSON>"}, "ownerEmail": {"type": "string", "description": "Owner email", "example": "<EMAIL>"}}, "description": "Response object for client note operations"}, "ClientAccountingUpdateRequest": {"required": ["accountingType", "amount", "recordDate"], "type": "object", "properties": {"accountingType": {"type": "string", "description": "Accounting type", "example": "RECEIVED", "enum": ["RECEIVED", "RECEIVABLE", "REFUND"]}, "amount": {"type": "number", "description": "Amount", "example": 1500.5}, "description": {"type": "string", "description": "Description", "example": "<PERSON><PERSON>"}, "recordDate": {"type": "string", "description": "Record date", "format": "date-time"}, "caseNumber": {"type": "string", "description": "Case number", "example": "2024/123"}}, "description": "Updated accounting data"}, "ClientAccountingResponse": {"type": "object", "properties": {"id": {"type": "integer", "description": "Record ID", "format": "int64", "example": 1}, "clientId": {"type": "integer", "description": "Client ID", "format": "int64", "example": 1}, "clientName": {"type": "string", "description": "Client name", "example": "<PERSON><PERSON>"}, "accountingType": {"type": "string", "description": "Accounting type", "example": "RECEIVED", "enum": ["RECEIVED", "RECEIVABLE", "REFUND"]}, "amount": {"type": "number", "description": "Amount", "example": 1500.5}, "description": {"type": "string", "description": "Description", "example": "<PERSON><PERSON>"}, "recordDate": {"type": "string", "description": "Record date", "format": "date-time"}, "caseNumber": {"type": "string", "description": "Case number", "example": "2024/123"}, "createdAt": {"type": "string", "description": "Created date", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Updated date", "format": "date-time"}, "ownerName": {"type": "string", "description": "Owner name", "example": "<PERSON><PERSON>. <PERSON><PERSON><PERSON>"}, "ownerEmail": {"type": "string", "description": "Owner email", "example": "<EMAIL>"}}, "description": "Client Accounting Response"}, "PowerOfAttorneyRequest": {"required": ["clientNameList", "lawyerList", "notary<PERSON><PERSON>", "powerList", "powerOfAttorneyNumber", "startDate", "yevmiyeNo"], "type": "object", "properties": {"powerOfAttorneyNumber": {"type": "string", "description": "Power of attorney number", "example": "POA-2023-12345"}, "notaryName": {"type": "string", "description": "Notary name", "example": "Istanbul 5. <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "clientNameList": {"type": "array", "description": "List of client names", "example": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "items": {"type": "string", "description": "List of client names", "example": "[\"<PERSON><PERSON> Yılmaz\",\"<PERSON><PERSON><PERSON>\"]"}}, "lawyerList": {"type": "array", "description": "List of lawyer names", "example": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "items": {"type": "string", "description": "List of lawyer names", "example": "[\"<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>. <PERSON>\"]"}}, "powerList": {"type": "array", "description": "List of powers granted", "example": ["<PERSON><PERSON>", "<PERSON><PERSON> o<PERSON>", "Feragat"], "items": {"type": "string", "description": "List of powers granted", "example": "[\"<PERSON><PERSON> açma\",\"<PERSON><PERSON> olma\",\"Feraga<PERSON>\"]"}}, "yevmiyeNo": {"type": "string", "description": "Yev<PERSON><PERSON> number", "example": "12345"}, "startDate": {"type": "string", "description": "Start date of the power of attorney", "format": "date", "example": "2023-01-15"}, "endDate": {"type": "string", "description": "End date of the power of attorney (optional)", "format": "date", "example": "2025-01-15"}, "caseNumber": {"type": "string", "description": "Case number to which this power of attorney belongs", "example": "2023/123"}}, "description": "Power of attorney data"}, "PowerOfAttorneyResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "powerOfAttorneyNumber": {"type": "string"}, "notaryName": {"type": "string"}, "clientNameList": {"type": "array", "items": {"type": "string"}}, "lawyerList": {"type": "array", "items": {"type": "string"}}, "powerList": {"type": "array", "items": {"type": "string"}}, "yevmiyeNo": {"type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "caseNumber": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "ownerId": {"type": "integer", "format": "int64"}, "ownerName": {"type": "string"}, "ownerEmail": {"type": "string"}}, "description": "Response object for power of attorney operations"}, "CaseNoteRequest": {"required": ["caseNumber", "content"], "type": "object", "properties": {"content": {"type": "string", "description": "Content of the note", "example": "This is a sample case note content."}, "caseNumber": {"type": "string", "description": "Case number to which this note belongs", "example": "2023/123"}}, "description": "Request object for creating or updating a case note"}, "CaseNoteResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "content": {"type": "string"}, "caseNumber": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "ownerId": {"type": "integer", "format": "int64"}, "ownerName": {"type": "string"}, "ownerEmail": {"type": "string"}}, "description": "Response object for case note operations"}, "CaseDetailsResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "caseNumber": {"type": "string"}, "caseType": {"type": "string", "enum": ["CEZA_DAVASI", "HUKUK_DAVASI", "IDARI_DAVA", "ICRA_TAKIBI", "ARABULUCULUK", "TAHKIM", "DIGER"]}, "crimeType": {"type": "string", "enum": ["DOLANDIRICILIK", "HIRSIZLIK", "KASTEN_YARALAMA", "TAKSIRLE_YARALAMA", "KASTEN_OLDURME", "TAKSIRLE_OLDURME", "HAKARET", "TEHDIT", "UYUSTURUCU", "CINSEL_SUC", "ZIMMET", "RUSVET", "SAHTECILIK", "VERGI_SUCU", "DIGER"]}, "derdest": {"type": "boolean"}, "caseValue": {"type": "number"}, "caseReason": {"type": "string"}, "caseTitle": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "ownerId": {"type": "integer", "format": "int64"}, "ownerName": {"type": "string"}, "ownerEmail": {"type": "string"}}, "description": "Response object for case details operations"}, "CaseDetailsRequest": {"required": ["caseNumber", "caseTitle", "caseType", "caseValue", "derdest"], "type": "object", "properties": {"caseNumber": {"type": "string", "description": "Case number", "example": "2023/123"}, "caseType": {"type": "string", "description": "Case type", "example": "CEZA_DAVASI", "enum": ["CEZA_DAVASI", "HUKUK_DAVASI", "IDARI_DAVA", "ICRA_TAKIBI", "ARABULUCULUK", "TAHKIM", "DIGER"]}, "crimeType": {"type": "string", "description": "Crime type", "example": "DOLANDIRICILIK", "enum": ["DOLANDIRICILIK", "HIRSIZLIK", "KASTEN_YARALAMA", "TAKSIRLE_YARALAMA", "KASTEN_OLDURME", "TAKSIRLE_OLDURME", "HAKARET", "TEHDIT", "UYUSTURUCU", "CINSEL_SUC", "ZIMMET", "RUSVET", "SAHTECILIK", "VERGI_SUCU", "DIGER"]}, "derdest": {"type": "boolean", "description": "Whether the case is ongoing (derdest)", "example": true}, "caseValue": {"type": "number", "description": "Case value", "example": 50000.0}, "caseReason": {"type": "string", "description": "Case reason", "example": "Taraflar arasındaki anlaşmazlık..."}, "caseTitle": {"type": "string", "description": "Case title", "example": "<PERSON><PERSON> Yılmaz vs. XYZ Şirketi"}}, "description": "Request object for creating or updating case details"}, "TaskRequest": {"required": ["description", "dueDate", "priority", "status", "title"], "type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "priority": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}, "startDate": {"type": "string", "format": "date-time"}, "dueDate": {"type": "string", "format": "date-time"}, "caseNumber": {"type": "string"}, "status": {"type": "string", "enum": ["OPEN", "IN_PROGRESS", "COMPLETED", "CANCELLED"]}, "taskType": {"type": "string", "enum": ["TASK", "TRIAL", "MEETING", "REMINDER", "OTHER"]}}, "description": "Request for creating or updating a task"}, "NoteResponse": {"required": ["content"], "type": "object", "properties": {"content": {"type": "string", "description": "Content of the note", "example": "This is a sample note content."}, "id": {"type": "integer", "format": "int64"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "description": "Response object for creating or updating a note"}, "TaskResponse": {"required": ["description", "priority", "status", "title"], "type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "priority": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}, "startDate": {"type": "string", "format": "date-time"}, "dueDate": {"type": "string", "format": "date-time"}, "caseNumber": {"type": "string"}, "status": {"type": "string", "enum": ["OPEN", "IN_PROGRESS", "COMPLETED", "CANCELLED"]}, "taskType": {"type": "string", "enum": ["TASK", "TRIAL", "MEETING", "REMINDER", "OTHER"]}, "id": {"type": "integer", "format": "int64"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "reporter": {"type": "integer", "format": "int64"}, "notes": {"type": "array", "items": {"$ref": "#/components/schemas/NoteResponse"}}}, "description": "Response for creating or updating a task"}, "NoteRequest": {"required": ["content"], "type": "object", "properties": {"content": {"type": "string", "description": "Content of the note", "example": "This is a sample note content."}}, "description": "Request object for creating or updating a note"}, "ReminderRequest": {"required": ["description", "dueDate", "priority", "repeatIntervalInHours", "title"], "type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "dueDate": {"type": "string", "format": "date-time"}, "repeatIntervalInHours": {"minimum": 1, "type": "integer", "format": "int64"}, "priority": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}}}, "ReminderResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "title": {"type": "string"}, "description": {"type": "string"}, "dueDate": {"type": "string", "format": "date-time"}, "repeatIntervalInHours": {"type": "integer", "format": "int32"}, "priority": {"type": "string"}, "reporterId": {"type": "integer", "format": "int64"}}, "description": "Response for creating or updating a reminder"}, "ProductUpdateRequest": {"required": ["name", "price", "type", "validityPeriodInMonths"], "type": "object", "properties": {"name": {"type": "string", "description": "Product name", "example": "Premium Subscription"}, "price": {"minimum": 0.01, "exclusiveMinimum": false, "type": "number", "description": "Product price", "example": 99.99}, "validityPeriodInMonths": {"maximum": 12, "minimum": 1, "type": "integer", "description": "Product validity period in months after purchase", "format": "int32", "example": 1}, "type": {"type": "string", "enum": ["BASIC", "PREMIUM", "ENTERPRISE"]}}, "description": "Product update request"}, "ProductResponse": {"type": "object", "properties": {"id": {"type": "integer", "description": "Product ID", "format": "int64", "example": 1}, "name": {"type": "string", "description": "Product name", "example": "Premium Subscription"}, "price": {"type": "number", "description": "Product price", "example": 99.99}, "createdAt": {"type": "string", "description": "Creation date", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Last update date", "format": "date-time"}, "validityPeriodInMonths": {"type": "integer", "description": "Product validity period in months after purchase", "format": "int32", "example": 1}, "type": {"type": "string", "description": "Product type", "example": "BASIC", "enum": ["BASIC", "PREMIUM", "ENTERPRISE"]}}, "description": "Product response"}, "TransactionTypeRequest": {"required": ["category", "name"], "type": "object", "properties": {"name": {"maxLength": 2147483647, "minLength": 2, "type": "string", "description": "Transaction type name", "example": "Electric Bill"}, "category": {"type": "string", "description": "Transaction Type category", "example": "EXPENSE", "enum": ["INCOME", "EXPENSE"]}}, "description": "Transaction Type Create Or Update Request"}, "UpdateCouponDto": {"type": "object", "properties": {"description": {"maxLength": 255, "minLength": 0, "type": "string", "description": "Optional description of the coupon", "example": "Updated summer discount coupon"}, "discountType": {"type": "string", "description": "Type of discount", "example": "PERCENTAGE", "enum": ["PERCENTAGE", "FIXED_AMOUNT"]}, "discountValue": {"maximum": 999999.99, "exclusiveMaximum": false, "minimum": 0.01, "exclusiveMinimum": false, "type": "number", "description": "Discount value (percentage or fixed amount)", "example": 15.0}, "usageLimit": {"minimum": 1, "type": "integer", "description": "Maximum number of times this coupon can be used", "format": "int32", "example": 200}, "expirationDate": {"type": "string", "description": "Optional expiration date", "format": "date-time"}, "active": {"type": "boolean", "description": "Whether the coupon is active", "example": true}}, "description": "Coupon update data"}, "CouponResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "Coupon ID", "format": "int64", "example": 1}, "code": {"type": "string", "description": "Unique coupon code", "example": "SUMMER2024"}, "description": {"type": "string", "description": "Description of the coupon", "example": "Summer discount coupon"}, "discountType": {"type": "string", "description": "Type of discount", "example": "PERCENTAGE", "enum": ["PERCENTAGE", "FIXED_AMOUNT"]}, "discountValue": {"type": "number", "description": "Discount value", "example": 10.0}, "usageLimit": {"type": "integer", "description": "Maximum usage limit", "format": "int32", "example": 100}, "currentUsageCount": {"type": "integer", "description": "Current usage count", "format": "int32", "example": 25}, "expirationDate": {"type": "string", "description": "Expiration date", "format": "date-time"}, "active": {"type": "boolean", "description": "Whether the coupon is active", "example": true}, "isValid": {"type": "boolean", "description": "Whether the coupon is valid for use", "example": true}, "isExpired": {"type": "boolean", "description": "Whether the coupon has expired", "example": false}, "isUsageLimitExceeded": {"type": "boolean", "description": "Whether usage limit has been exceeded", "example": false}, "createdAt": {"type": "string", "description": "Creation timestamp", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Last update timestamp", "format": "date-time"}, "createdBy": {"type": "string", "description": "Created by user"}, "updatedBy": {"type": "string", "description": "Last updated by user"}}, "description": "Response DTO for coupon operations"}, "VerifyEmailRequest": {"required": ["email", "otp"], "type": "object", "properties": {"otp": {"maxLength": 8, "minLength": 8, "type": "string", "description": "One-time password sent to the email", "example": "12345678"}, "email": {"type": "string", "description": "Email address to verify", "example": "<EMAIL>"}}, "description": "Email verification request"}, "VerifyEmailResponse": {"type": "object", "properties": {"responseMessage": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "verified": {"type": "boolean", "description": "Whether the email was successfully verified"}, "personId": {"type": "integer", "description": "Person ID if verification was successful", "format": "int64"}}, "description": "Email verification response"}, "PersonLoginRequest": {"required": ["email", "password"], "type": "object", "properties": {"password": {"pattern": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\w\\s])\\S{8,50}$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "example": "********"}, "email": {"type": "string", "description": "Person's email address", "example": "<EMAIL>"}}, "description": "Person Data Transfer Object for Login"}, "PersonLoginResponse": {"type": "object", "properties": {"responseMessage": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "surname": {"type": "string"}, "isNewUser": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "jwt": {"type": "string"}}, "description": "Person Response DTO"}, "ValidateOtpRequest": {"required": ["otpEmail", "otpValue"], "type": "object", "properties": {"otpValue": {"maxLength": 8, "minLength": 8, "type": "string"}, "otpEmail": {"type": "string"}}}, "UpdatePasswordRequest": {"required": ["password"], "type": "object", "properties": {"password": {"pattern": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\w\\s])\\S{8,50}$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "example": "********"}, "validatedOtp": {"$ref": "#/components/schemas/ValidateOtpRequest"}}}, "ForgotPasswordRequest": {"required": ["email"], "type": "object", "properties": {"email": {"type": "string"}}}, "PersonCreateRequest": {"required": ["birthDate", "email", "identityNumber", "mobilePhone", "name", "password", "surname"], "type": "object", "properties": {"password": {"pattern": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\w\\s])\\S{8,50}$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "example": "********"}, "email": {"type": "string", "description": "Person's email address", "example": "<EMAIL>"}, "name": {"maxLength": 50, "minLength": 2, "type": "string", "description": "Person's name", "example": "<PERSON>"}, "surname": {"maxLength": 50, "minLength": 2, "type": "string", "description": "Person's surname", "example": "<PERSON><PERSON>"}, "personInfoText": {"maxLength": 255, "minLength": 0, "type": "string", "description": "Additional information about the person"}, "identityNumber": {"type": "integer", "description": "Person's identity number (TCKN)", "format": "int64", "example": 12345678901}, "birthDate": {"type": "string", "description": "Person's birth date", "format": "date", "example": "1990-01-30"}, "mobilePhone": {"maxLength": 10, "minLength": 10, "type": "string", "description": "Person's mobile phone number", "example": "5551234567"}}, "description": "Person Data Transfer Object"}, "PersonCreateResponse": {"type": "object", "properties": {"responseMessage": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "surname": {"type": "string"}, "personInfoText": {"type": "string"}, "identityNumber": {"type": "integer", "format": "int64"}, "birthDate": {"type": "string", "format": "date"}, "email": {"type": "string"}, "mobilePhone": {"type": "string"}, "isNewUser": {"type": "boolean"}, "isDeleted": {"type": "boolean"}}, "description": "Person Response DTO"}, "EmailRequest": {"required": ["to"], "type": "object", "properties": {"to": {"type": "string"}}}, "TrialNoteCreateDto": {"required": ["caseNumber", "noteContent", "noteTitle"], "type": "object", "properties": {"caseNumber": {"type": "string", "description": "Case number to which this trial note belongs", "example": "2023/123"}, "noteContent": {"type": "string", "description": "Content of the trial note", "example": "This is a detailed trial note content describing the proceedings."}, "noteTitle": {"maxLength": 255, "minLength": 0, "type": "string", "description": "Title of the trial note", "example": "First Hearing Notes"}}, "description": "Request object for creating a trial note"}, "SyncRequest": {"required": ["jsid"], "type": "object", "properties": {"jsid": {"type": "string"}}}, "BaseResponse": {"type": "object", "properties": {"responseMessage": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}, "CreateOrderRequest": {"required": ["productId"], "type": "object", "properties": {"productId": {"minimum": 1, "type": "integer", "description": "Ürün ID", "format": "int64", "example": 1}, "couponCode": {"type": "string", "description": "Optional coupon code to apply discount", "example": "SUMMER2024"}}, "description": "Payment request"}, "PaymentResponse": {"type": "object", "properties": {"id": {"type": "integer", "description": "Payment ID", "format": "int64", "example": 12345}, "conversationId": {"type": "string", "description": "Conversation ID", "example": "123456789"}, "price": {"type": "number", "description": "Price", "example": 100.0}, "paidPrice": {"type": "number", "description": "Paid price", "example": 100.0}, "currency": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "example": "TRY"}, "basketId": {"type": "string", "description": "Basket ID", "example": "B67832"}, "paymentId": {"type": "string", "description": "Payment ID from iyzico", "example": "12345678"}, "paymentTransactionId": {"type": "string", "description": "Payment transaction ID", "example": "12345678"}, "status": {"type": "string", "description": "Payment status", "example": "SUCCESS", "enum": ["PENDING", "SUCCESS", "FAILURE", "CANCELED"]}, "paymentDate": {"type": "string", "description": "Payment date", "format": "date-time"}, "errorCode": {"type": "string", "description": "Error code if payment failed", "example": "123"}, "errorMessage": {"type": "string", "description": "Error message if payment failed", "example": "Payment failed"}, "errorGroup": {"type": "string", "description": "Error group if payment failed", "example": "PAYMENT"}, "paymentPageUrl": {"type": "string", "description": "Payment page URL", "example": "https://sandbox-api.iyzipay.com/payment/pay"}, "appliedCouponCode": {"type": "string", "description": "Applied coupon code", "example": "SUMMER2024"}, "originalPrice": {"type": "number", "description": "Original price before discount", "example": 100.0}, "discountAmount": {"type": "number", "description": "Discount amount applied", "example": 10.0}, "product": {"$ref": "#/components/schemas/ProductResponse"}, "createdAt": {"type": "string", "description": "Creation date", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Last update date", "format": "date-time"}, "validUntil": {"type": "string", "description": "Expiry date", "format": "date-time"}}, "description": "Payment response"}, "FrequentCaseNumberRequest": {"required": ["caseNumber"], "type": "object", "properties": {"caseNumber": {"maxLength": 50, "minLength": 2, "type": "string", "description": "Case number to be added to frequently used list", "example": "2023/123"}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "description": "Optional description for the case number", "example": "<PERSON><PERSON><PERSON><PERSON> dava"}}, "description": "Request DTO for creating or updating a frequently used case number"}, "FrequentCaseNumberResponse": {"type": "object", "properties": {"responseMessage": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "description": "Unique identifier for the frequent case number", "format": "int64", "example": 1}, "caseNumber": {"type": "string", "description": "The case number", "example": "2023/123"}, "description": {"type": "string", "description": "Optional description for the case number", "example": "<PERSON><PERSON><PERSON><PERSON> dava"}, "lastUsedAt": {"type": "string", "description": "When the case number was last used", "format": "date-time", "example": "2023-06-15T10:30:45.123Z"}}, "description": "Response DTO for a frequently used case number"}, "CompensationRequest": {"required": ["endDate", "grossSalary", "startDate"], "type": "object", "properties": {"startDate": {"type": "string", "description": "Start date for the compensation calculation", "format": "date", "example": "2025-01-15"}, "endDate": {"type": "string", "description": "End date for the compensation calculation", "format": "date", "example": "2025-01-15"}, "grossSalary": {"type": "number", "description": "Base salary", "example": 30000}, "cumulativeIncomeTaxBasis": {"type": "number", "description": "Kümülatif gelir vergisi matrahı", "example": 100000}}, "description": "Request for calculating compensation"}, "CompensationResponse": {"type": "object", "properties": {"totalDays": {"type": "integer", "format": "int32"}, "grossSeverancePay": {"type": "string"}, "severancePayStampTax": {"type": "string"}, "netSeverancePay": {"type": "string"}, "noticePeriodInDays": {"type": "integer", "format": "int32"}, "jobSearchLeaveHours": {"type": "integer", "format": "int32"}, "grossNoticePay": {"type": "string"}, "noticePayStampTax": {"type": "string"}, "noticePayIncomeTax": {"type": "string"}, "netNoticePay": {"type": "string"}, "totalCompensation": {"type": "string"}, "severanceNoticeText": {"type": "string"}}, "description": "Response for calculating compensation"}, "ClientCreateDto": {"required": ["clientType", "name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 2, "type": "string", "description": "Client name", "example": "<PERSON><PERSON>"}, "identityOrTaxNumber": {"type": "integer", "description": "Identity number for individuals or tax number for corporations", "format": "int64", "example": 12345678901}, "address": {"maxLength": 500, "minLength": 0, "type": "string", "description": "Client address", "example": "Atatürk Cad. No:123 Çankaya/Ankara"}, "phoneNumber": {"maxLength": 15, "minLength": 0, "type": "string", "description": "Client phone number", "example": "***********"}, "email": {"type": "string", "description": "Client email address", "example": "<EMAIL>"}, "clientType": {"type": "string", "description": "Client type", "example": "INDIVIDUAL", "enum": ["INDIVIDUAL", "CORPORATE", "OTHER"]}}, "description": "Client creation data"}, "ClientAccountingCreateRequest": {"required": ["accountingType", "amount", "clientId", "recordDate"], "type": "object", "properties": {"clientId": {"type": "integer", "description": "Client ID", "format": "int64", "example": 1}, "accountingType": {"type": "string", "description": "Accounting type", "example": "RECEIVED", "enum": ["RECEIVED", "RECEIVABLE", "REFUND"]}, "amount": {"type": "number", "description": "Amount", "example": 1500.5}, "description": {"type": "string", "description": "Description", "example": "<PERSON><PERSON>"}, "recordDate": {"type": "string", "description": "Record date", "format": "date-time"}, "caseNumber": {"type": "string", "description": "Case number", "example": "2024/123"}}, "description": "Client accounting data"}, "ProductCreateRequest": {"required": ["name", "price", "type", "validityPeriodInMonths"], "type": "object", "properties": {"name": {"type": "string", "description": "Product name", "example": "Premium Subscription"}, "price": {"minimum": 0.01, "exclusiveMinimum": false, "type": "number", "description": "Product price", "example": 99.99}, "validityPeriodInMonths": {"maximum": 12, "minimum": 1, "type": "integer", "description": "Product validity period in months after purchase", "format": "int32", "example": 1}, "type": {"type": "string", "enum": ["BASIC", "PREMIUM", "ENTERPRISE"]}}, "description": "Product creation request"}, "FileUploadDto": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "description": {"maxLength": 500, "minLength": 0, "type": "string", "description": "Optional description for the file", "example": "Important legal document"}, "tags": {"maxLength": 500, "minLength": 0, "type": "string", "description": "Comma-separated tags for file categorization", "example": "legal,contract,2024"}, "isPublic": {"type": "boolean", "description": "Whether the file should be publicly accessible", "example": false, "default": false}}, "description": "File upload request with file and metadata"}, "FileResponseDto": {"type": "object", "properties": {"responseMessage": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "description": "File ID", "format": "int64", "example": 1}, "originalFilename": {"type": "string", "description": "Original filename as uploaded", "example": "contract.pdf"}, "contentType": {"type": "string", "description": "File content type/MIME type", "example": "application/pdf"}, "fileSize": {"type": "integer", "description": "File size in bytes", "format": "int64", "example": 1048576}, "fileType": {"type": "string", "description": "File type category", "example": "DOCUMENT", "enum": ["IMAGE", "DOCUMENT", "SPREADSHEET", "PRESENTATION", "ARCHIVE", "VIDEO", "AUDIO", "OTHER"]}, "description": {"type": "string", "description": "File description", "example": "Important legal contract"}, "tags": {"type": "string", "description": "File tags for categorization", "example": "legal,contract,2024"}, "isPublic": {"type": "boolean", "description": "Whether the file is publicly accessible", "example": false}, "downloadCount": {"type": "integer", "description": "Number of times the file has been downloaded", "format": "int64", "example": 5}, "lastAccessedAt": {"type": "string", "description": "Last time the file was accessed", "format": "date-time", "example": "2024-01-15T10:30:00Z"}, "md5Hash": {"type": "string", "description": "MD5 hash of the file content", "example": "d41d8cd98f00b204e9800998ecf8427e"}, "uploader": {"$ref": "#/components/schemas/UploaderInfo"}, "createdAt": {"type": "string", "description": "File creation timestamp", "format": "date-time", "example": "2024-01-15T09:00:00Z"}, "updatedAt": {"type": "string", "description": "File last update timestamp", "format": "date-time", "example": "2024-01-15T09:00:00Z"}, "fileExtension": {"type": "string"}, "formattedFileSize": {"type": "string"}}, "description": "Complete file information response"}, "UploaderInfo": {"type": "object", "properties": {"id": {"type": "integer", "description": "Uploader ID", "format": "int64", "example": 1}, "fullName": {"type": "string", "description": "Uploader full name", "example": "<PERSON>"}, "email": {"type": "string", "description": "Uploader email", "example": "<EMAIL>"}}, "description": "Information about the file uploader"}, "CreateCouponDto": {"required": ["code", "discountType", "discountValue", "usageLimit"], "type": "object", "properties": {"code": {"maxLength": 50, "minLength": 3, "type": "string", "description": "Unique coupon code", "example": "SUMMER2024"}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "description": "Optional description of the coupon", "example": "Summer discount coupon"}, "discountType": {"type": "string", "description": "Type of discount", "example": "PERCENTAGE", "enum": ["PERCENTAGE", "FIXED_AMOUNT"]}, "discountValue": {"maximum": 999999.99, "exclusiveMaximum": false, "minimum": 0.01, "exclusiveMinimum": false, "type": "number", "description": "Discount value (percentage or fixed amount)", "example": 10.0}, "usageLimit": {"minimum": 1, "type": "integer", "description": "Maximum number of times this coupon can be used", "format": "int32", "example": 100}, "expirationDate": {"type": "string", "description": "Optional expiration date", "format": "date-time"}, "active": {"type": "boolean", "description": "Whether the coupon is active", "example": true, "default": true}}, "description": "Coupon creation data"}, "FinancialSummarySection": {"type": "object", "properties": {"totalIncome": {"type": "number", "description": "Total income amount for the case"}, "totalExpenses": {"type": "number", "description": "Total expenses amount for the case"}, "netIncome": {"type": "number", "description": "Net income (income - expenses) for the case"}}, "description": "Summary of case's financial information"}, "TaskSummarySection": {"type": "object", "properties": {"totalTasks": {"type": "integer", "description": "Total number of tasks for the case", "format": "int32"}, "taskCountsByStatus": {"type": "object", "additionalProperties": {"type": "integer", "description": "Count of tasks grouped by status (O<PERSON><PERSON>, IN_PROGRESS, COMPLETED, CANCELLED)", "format": "int32"}, "description": "Count of tasks grouped by status (O<PERSON><PERSON>, IN_PROGRESS, COMPLETED, CANCELLED)"}, "taskCountsByPriority": {"type": "object", "additionalProperties": {"type": "integer", "description": "Count of tasks grouped by priority (LOW, MEDIUM, HIGH, CRITICAL)", "format": "int32"}, "description": "Count of tasks grouped by priority (LOW, MEDIUM, HIGH, CRITICAL)"}}, "description": "Summary of case's tasks with counts by status and priority"}, "UserDetailsSection": {"type": "object", "properties": {"details": {"type": "object", "additionalProperties": {"type": "object", "description": "Map of user details from UYAP system"}, "description": "Map of user details from UYAP system"}, "photoData": {"type": "string", "description": "User's photo data, included only if explicitly requested"}}, "description": "User details from external systems"}, "UserReportResponse": {"type": "object", "properties": {"id": {"type": "integer", "description": "User ID", "format": "int64"}, "name": {"type": "string", "description": "User's first name"}, "surname": {"type": "string", "description": "User's last name"}, "fullName": {"type": "string", "description": "User's full name (first + last name)"}, "email": {"type": "string", "description": "User's email address"}, "mobilePhone": {"type": "string", "description": "User's mobile phone number"}, "identityNumber": {"type": "integer", "description": "User's identity number", "format": "int64"}, "birthDate": {"type": "string", "description": "User's birth date", "format": "date"}, "isEmailVerified": {"type": "boolean", "description": "Whether the user's email is verified"}, "isMobilePhoneVerified": {"type": "boolean", "description": "Whether the user's mobile phone is verified"}, "isNewUser": {"type": "boolean", "description": "Whether the user is new to the system"}, "uyapDetails": {"$ref": "#/components/schemas/UserDetailsSection"}, "financialSummary": {"$ref": "#/components/schemas/FinancialSummarySection"}, "taskSummary": {"$ref": "#/components/schemas/TaskSummarySection"}, "reportGeneratedAt": {"type": "string", "description": "Timestamp when the report was generated", "format": "date-time"}}, "description": "Comprehensive user report containing personal information, financial data, and task statistics"}, "UserPhotoResponse": {"type": "object", "properties": {"responseMessage": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "photo": {"type": "string"}}}, "PaymentStatusResponse": {"type": "object", "properties": {"paymentId": {"type": "string", "description": "Payment ID", "example": "12345678"}, "status": {"type": "string", "description": "Payment status", "example": "SUCCESS", "enum": ["PENDING", "SUCCESS", "FAILURE", "CANCELED"]}, "successful": {"type": "boolean", "description": "Is payment successful", "example": true}, "errorCode": {"type": "string", "description": "Error code if payment failed", "example": "123"}, "errorMessage": {"type": "string", "description": "Error message if payment failed", "example": "Payment failed"}}, "description": "Payment status response"}, "CurrencyDto": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}}}, "CaseReportResponse": {"type": "object", "properties": {"caseNumber": {"type": "string", "description": "Case number"}, "financialSummary": {"$ref": "#/components/schemas/FinancialSummarySection"}, "transactions": {"type": "array", "description": "List of income and expense transactions for the case", "items": {"$ref": "#/components/schemas/TransactionResponse"}}, "taskSummary": {"$ref": "#/components/schemas/TaskSummarySection"}, "tasks": {"type": "array", "description": "List of tasks for the case", "items": {"$ref": "#/components/schemas/TaskResponse"}}, "reportGeneratedAt": {"type": "string", "description": "Timestamp when the report was generated", "format": "date-time"}}, "description": "Comprehensive case report containing financial data and task statistics"}, "CaseTypeOptionsResponse": {"type": "object", "properties": {"caseTypes": {"type": "array", "description": "List of available case types with their display names", "items": {"$ref": "#/components/schemas/TypeOption"}}, "crimeTypes": {"type": "array", "description": "List of available crime types with their display names", "items": {"$ref": "#/components/schemas/TypeOption"}}}, "description": "Response containing available case types and crime types"}, "TypeOption": {"type": "object", "properties": {"code": {"type": "string", "description": "The enum code value to be used in requests", "example": "CEZA_DAVASI"}, "displayName": {"type": "string", "description": "The human-readable display name", "example": "<PERSON>za <PERSON>"}}, "description": "Type option with code and display name"}, "FileListDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "File ID", "format": "int64", "example": 1}, "originalFilename": {"type": "string", "description": "Original filename as uploaded", "example": "contract.pdf"}, "contentType": {"type": "string", "description": "File content type/MIME type", "example": "application/pdf"}, "fileSize": {"type": "integer", "description": "File size in bytes", "format": "int64", "example": 1048576}, "fileType": {"type": "string", "description": "File type category", "example": "DOCUMENT", "enum": ["IMAGE", "DOCUMENT", "SPREADSHEET", "PRESENTATION", "ARCHIVE", "VIDEO", "AUDIO", "OTHER"]}, "description": {"type": "string", "description": "File description", "example": "Important legal contract"}, "tags": {"type": "string", "description": "File tags for categorization", "example": "legal,contract,2024"}, "isPublic": {"type": "boolean", "description": "Whether the file is publicly accessible", "example": false}, "downloadCount": {"type": "integer", "description": "Number of times the file has been downloaded", "format": "int64", "example": 5}, "lastAccessedAt": {"type": "string", "description": "Last time the file was accessed", "format": "date-time", "example": "2024-01-15T10:30:00Z"}, "uploaderName": {"type": "string", "description": "Uploader full name", "example": "<PERSON>"}, "uploaderEmail": {"type": "string", "description": "Uploader email", "example": "<EMAIL>"}, "createdAt": {"type": "string", "description": "File creation timestamp", "format": "date-time", "example": "2024-01-15T09:00:00Z"}, "updatedAt": {"type": "string", "description": "File last update timestamp", "format": "date-time", "example": "2024-01-15T09:00:00Z"}, "fileExtension": {"type": "string"}, "formattedFileSize": {"type": "string"}}, "description": "File list item with essential information"}, "AdminReportAboutUser": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "createdAt": {"type": "string", "format": "date-time"}, "roles": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "subscriptionLevel": {"type": "string", "enum": ["BASIC", "PREMIUM", "ENTERPRISE"]}, "totalPaymentAmount": {"type": "number"}, "newUser": {"type": "boolean"}}}, "FileStatistics": {"type": "object", "properties": {"totalFiles": {"type": "integer", "format": "int64"}, "totalSize": {"type": "integer", "format": "int64"}, "fileCountByType": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}}, "fileCountByUploader": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}}}}, "PageCouponResponseDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "numberOfElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/CouponResponseDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "empty": {"type": "boolean"}}}, "PageableObject": {"type": "object", "properties": {"paged": {"type": "boolean"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "unpaged": {"type": "boolean"}, "offset": {"type": "integer", "format": "int64"}, "sort": {"$ref": "#/components/schemas/SortObject"}}}, "SortObject": {"type": "object", "properties": {"sorted": {"type": "boolean"}, "unsorted": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "AuditLogResponse": {"type": "object", "properties": {"id": {"type": "integer", "description": "Unique identifier of the audit log", "format": "int64", "example": 1}, "endpointUrl": {"type": "string", "description": "Full endpoint URL that was accessed", "example": "/api/user/profile"}, "httpMethod": {"type": "string", "description": "HTTP request method", "example": "GET"}, "queryParameters": {"type": "string", "description": "Query parameters as JSON string", "example": "{\"page\":\"1\",\"size\":\"10\"}"}, "pathParameters": {"type": "string", "description": "Path/URL parameters as JSON string", "example": "{\"id\":\"123\"}"}, "responseStatusCode": {"type": "integer", "description": "HTTP response status code", "format": "int32", "example": 200}, "processingTimeMs": {"type": "integer", "description": "Request processing time in milliseconds", "format": "int64", "example": 150}, "userEmail": {"type": "string", "description": "User email from JWT token", "example": "<EMAIL>"}, "userRoles": {"type": "string", "description": "User roles from JWT token as JSON array string", "example": "[\"ROLE_USER\"]"}, "clientIpAddress": {"type": "string", "description": "Client IP address", "example": "***********"}, "userAgent": {"type": "string", "description": "User-Agent header information", "example": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}, "requestTimestamp": {"type": "string", "description": "Request timestamp when the request was received", "format": "date-time"}, "responseTimestamp": {"type": "string", "description": "Response timestamp when the response was sent", "format": "date-time"}, "requestHeaders": {"type": "string", "description": "Additional request headers as JSON string"}, "requestBodySize": {"type": "integer", "description": "Request body size in bytes", "format": "int64", "example": 1024}, "responseBodySize": {"type": "integer", "description": "Response body size in bytes", "format": "int64", "example": 2048}, "sessionId": {"type": "string", "description": "Session ID if available", "example": "ABC123XYZ"}, "errorMessage": {"type": "string", "description": "Any error message if the request failed"}, "createdAt": {"type": "string", "description": "When the audit log was created", "format": "date-time"}, "updatedAt": {"type": "string", "description": "When the audit log was last updated", "format": "date-time"}, "createdBy": {"type": "string", "description": "Who created the audit log"}, "updatedBy": {"type": "string", "description": "Who last updated the audit log"}}, "description": "Response DTO for audit log entries"}, "AuditLogStatisticsResponse": {"type": "object", "properties": {"totalRequests": {"type": "integer", "description": "Total number of requests", "format": "int64", "example": 1500}, "uniqueUsers": {"type": "integer", "description": "Number of unique users", "format": "int64", "example": 25}, "avgProcessingTime": {"type": "number", "description": "Average processing time in milliseconds", "format": "double", "example": 125.5}, "errorCount": {"type": "integer", "description": "Number of error requests (status >= 400)", "format": "int64", "example": 15}, "mostAccessedEndpoints": {"type": "array", "description": "Most accessed endpoints", "items": {"$ref": "#/components/schemas/EndpointStatistic"}}, "mostActiveUsers": {"type": "array", "description": "Most active users", "items": {"$ref": "#/components/schemas/UserStatistic"}}}, "description": "Response DTO for audit log statistics"}, "EndpointStatistic": {"type": "object", "properties": {"endpointUrl": {"type": "string", "description": "Endpoint URL", "example": "/api/user/profile"}, "accessCount": {"type": "integer", "description": "Number of accesses", "format": "int64", "example": 150}}, "description": "Endpoint access statistics"}, "UserStatistic": {"type": "object", "properties": {"userEmail": {"type": "string", "description": "User email", "example": "<EMAIL>"}, "requestCount": {"type": "integer", "description": "Number of requests", "format": "int64", "example": 75}}, "description": "User activity statistics"}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}